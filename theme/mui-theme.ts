import { createTheme } from '@mui/material/styles';

const theme = createTheme({
  typography: {
    fontFamily: "'Inter', sans-serif;",
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          borderRadius: 100,
          textTransform: 'none',
          boxShadow: 'none',
          '&.MuiButton-contained': {
            background: 'linear-gradient(to bottom, #FD8205, #E97100)',
            color: '#fff',
            height: 42,
            minWidth: 164,
            transition: 'background 0.3s ease',
            '&:hover': {
              background: 'linear-gradient(to bottom,rgb(255, 156, 69),rgb(245, 118, 0))',
              boxShadow: 'none',
            },
          },
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          color: '#DCDFDF',
          '&.Mui-checked': {
            color: '#FD8205',
          },
        },
      },
    },
    MuiFormLabel: {
      styleOverrides: {
        root: {
          '&.Mui-focused': {
            color: 'rgba(0, 0, 0, 0.54)', // Change the color to your desired color
          },
        },
      },
    },
    MuiFilledInput: {
      styleOverrides: {
        root: {
          backgroundColor: '#F7F8FA',
          borderRadius: 8,
          '&:before': {
            borderBottom: 'none',
          },
          '&:hover:not(.Mui-disabled):before': {
            borderBottom: 'none',
          },
          '&:after': {
            borderBottom: 'none',
          },
        },
        underline: {
          '&:before': {
            borderBottom: 'none',
          },
          '&:hover:not(.Mui-disabled):before': {
            borderBottom: 'none',
          },
          '&:after': {
            borderBottom: 'none',
          },
        },
      },
    },
  },
});

export default theme;
