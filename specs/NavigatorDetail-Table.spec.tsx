// import React from 'react';
// import { render, screen } from '@testing-library/react';
// import '@testing-library/jest-dom';
// import NavigatorDetail from '../components/navigators/NavigatorDetail';

// // Mock the useAuth hook to return admin user
// jest.mock('../hooks/use-auth', () => ({
//   __esModule: true,
//   default: () => ({
//     user: {
//       id: 'admin-123',
//       roles: ['admin'],
//       meta: { access: ['tasks'] },
//     },
//     login: jest.fn(),
//     logout: jest.fn(),
//     error: null,
//   }),
// }));

// // Mock the API
// jest.mock('../api/src/lib/api', () => ({
//   __esModule: true,
//   default: {
//     USER: {
//       updateUser: jest.fn().mockResolvedValue({}),
//     },
//   },
// }));

// // Mock next/router
// jest.mock('next/router', () => ({
//   __esModule: true,
//   default: {
//     push: jest.fn(),
//   },
// }));

// // Mock utils/helper
// jest.mock('../utils/helper', () => ({
//   getLatestImage: jest.fn(() => 'mock-image-url'),
//   capitalizeWords: jest.fn((str: string) => str.charAt(0).toUpperCase() + str.slice(1)),
// }));

// const mockNavigator = {
//   id: 'nav-123',
//   firstName: 'John',
//   lastName: 'Doe',
//   email: '<EMAIL>',
//   phones: [],
//   teams: [],
//   roles: ['navigator'],
//   attachments: [],
//   meta: {
//     access: ['tasks', 'chats', 'households'],
//   },
//   auth: 'auth-token',
//   color: '#000000',
//   createdAt: '2023-01-01',
//   updatedAt: '2023-01-01',
//   org: {
//     id: 'org-123',
//     title: 'Test Org',
//     type: 'organization',
//     url: 'test-org',
//     desc: 'Test Organization',
//     createdAt: '2023-01-01',
//   },
// } as any;

// const mockTasks = {
//   'picked up': [],
//   pending: [],
// };

// describe('NavigatorDetail Access Table UI', () => {
//   it('should render access table with proper headers', () => {
//     render(<NavigatorDetail navigator={mockNavigator} tasks={mockTasks} />);

//     // Check for table headers
//     expect(screen.getByText('Module')).toBeInTheDocument();
//     expect(screen.getByText('Description')).toBeInTheDocument();
//     expect(screen.getByText('Access')).toBeInTheDocument();
//   });

//   it('should display all modules with descriptions', () => {
//     render(<NavigatorDetail navigator={mockNavigator} tasks={mockTasks} />);

//     // Check for module names
//     expect(screen.getByText('Tasks')).toBeInTheDocument();
//     expect(screen.getByText('Chats')).toBeInTheDocument();
//     expect(screen.getByText('Households')).toBeInTheDocument();
//     expect(screen.getByText('Navigators')).toBeInTheDocument();
//     expect(screen.getByText('Reporting')).toBeInTheDocument();
//     expect(screen.getByText('Teams')).toBeInTheDocument();
//     expect(screen.getByText('Networks')).toBeInTheDocument();
//     expect(screen.getByText('Members')).toBeInTheDocument();

//     // Check for descriptions
//     expect(screen.getByText('Manage and assign tasks to members')).toBeInTheDocument();
//     expect(screen.getByText('Access chat conversations and messaging')).toBeInTheDocument();
//     expect(screen.getByText('View and manage household information')).toBeInTheDocument();
//   });

//   it('should render switches for each module', () => {
//     render(<NavigatorDetail navigator={mockNavigator} tasks={mockTasks} />);

//     // Should have 8 switches (one for each module)
//     const switches = screen.getAllByRole('checkbox'); // MUI Switch uses checkbox role
//     expect(switches).toHaveLength(8);
//   });
// });
