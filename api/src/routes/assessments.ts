// eslint-disable-next-line import/no-cycle
import { BASE_URL, TOKEN, instance } from 'api/src/lib/api';
import axios, { AxiosRequestConfig } from 'axios';

// eslint-disable-next-line import/prefer-default-export
export const Assessments = {
  fetchTypes: async (orgId: string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/templates/all?org=${orgId?.toLowerCase()}&kind=assessment`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    const request = await axios(config);
    return request.data;
  },
  fetchConsentFormJson: async (orgId: string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/templates/all?org=${orgId?.toLowerCase()}&kind=consent_web`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    const request = await axios(config);
    return request.data;
  },
};
