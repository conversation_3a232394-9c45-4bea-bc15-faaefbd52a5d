/* eslint-disable default-param-last */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable import/prefer-default-export */
// eslint-disable-next-line import/no-cycle
import {
  BASE_URL, ORG_ID, TOKEN, instance,
} from 'api/src/lib/api';

import { AxiosRequestConfig } from 'axios';
// eslint-disable-next-line import/no-cycle

export const SOCIALPLANS = {
  fetchSingleSocialPlan: async (sId:string, token?:string, orgId?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/carepackage/${sId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  endSocialPlan: async (sId:string, data?:any, token?:string, orgId?: string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/carepackage/${sId}/end`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    return instance(config);
  },
  createSocialPlan: async (data?:any, token?:string, orgId?: string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/carepackage`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },
  createItemSocialPlan: async (
    data:any,
    socialPlanId: string,
    token?:string,
    orgId?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/carepackage/${socialPlanId}/item`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },
  removeItemSocialPlan: async (
    socialPlanId: string,
    itemId: string,
    token?:string,
    orgId?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/carepackage/${socialPlanId}/item/${itemId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
        'Content-Type': 'application/json',
      },
    };
    return instance(config);
  },
  updateSocialPlan: async (
    data:any,
    socialPlanId: string,
    networkId: string,
    token?:string,
    orgId?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/carepackage/${socialPlanId}/item/${networkId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },
  fetchSocialPlanTimeline: async (sId:string, token?:string, orgId?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/carepackage/${sId}/timeline`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
};
