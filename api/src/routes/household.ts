/* eslint-disable import/prefer-default-export */
import { AxiosRequestConfig } from 'axios';
// eslint-disable-next-line import/no-cycle
import {
  BASE_URL, ORG_ID, TOKEN, instance,
} from 'api/src/lib/api';

import {
  FetchSingleHouseholdRequest,
  CreateHouseholdRequest,
  CreateHouseholdAttachmentRequest,
  RemoveTeamFromHouseholdRequest,
  RemoveHeadOfHouseRequest,
  RemoveMemberFromHouseholdRequest,
  UpdateHouseHoldRequestBody,
  AddMemberToHouseholdRequest,
  AddTeamToHouseholdRequest,
} from '../types';

export const HOUSEHOLDS = {
  fetchHouseholds: async (token?:string, orgId?: string, search?:string, per = 5, page = 1, sortBy = 'createdAt', sortDirection = 'desc') => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/households/page?org=${ORG_ID() || orgId}&per=${per}&page=${page}&sortBy=${sortBy}&sortDirection=${sortDirection}&search=${search}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchSingleHousehold: async (householdId:string, token?:string, org?:string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/households/${householdId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  createHousehold: async (request: CreateHouseholdRequest) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/households?org=${ORG_ID()}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data: request.body,
    };
    return instance(config);
  },
  setHeadOfHousehold: async (data:any, householdId:string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/households/${householdId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },
  updateHouseHold: async (request: UpdateHouseHoldRequestBody) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/households/${request.householdId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data: request.body,
    };
    return instance(config);
  },
  addMemberToHousehold: async (request: AddMemberToHouseholdRequest) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/households/${request.householdId}/members`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data: request.body,
    };
    return instance(config);
  },
  addTeamToHousehold: async (request: AddTeamToHouseholdRequest) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/households/${request.householdId}/teams`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data: request.body,
    };
    return instance(config);
  },
  createHouseholdAttachment: async (request: CreateHouseholdAttachmentRequest) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/households/${request.householdId}/attachments`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data: request.body,
    };
    return instance(config);
  },

  delete: async (householdId: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/households/${householdId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
    };
    return instance(config);
  },

  removeTeamFromHousehold: async (request: RemoveTeamFromHouseholdRequest) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/households/${request.householdId}/teams`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data: request.body,
    };
    return instance(config);
  },

  removeHeadOfHouse: async (request: RemoveHeadOfHouseRequest) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/households/${request.householdId}/headofhouse`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
    };
    return instance(config);
  },

  removeMemberFromHousehold: async (request: RemoveMemberFromHouseholdRequest) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/households/${request.householdId}/members/${request.memberId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
    };
    return instance(config);
  },
};
