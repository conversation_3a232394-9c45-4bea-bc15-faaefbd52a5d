// eslint-disable-next-line import/no-cycle
import {
  BASE_URL, TOKEN, instance,
} from 'api/src/lib/api';
import { UserSignUp, UserLogin } from 'api/src/types';
import axios, { AxiosRequestConfig } from 'axios';
// eslint-disable-next-line import/prefer-default-export
export const Auth = {
  signUp: async (data: UserSignUp) => {
    const config: AxiosRequestConfig = {
      method: 'post',
      url: `${BASE_URL}/auth/signup`,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data,
    };
    return instance(config);
  },

  login: async (data: UserLogin) => {
    const config: AxiosRequestConfig = {
      method: 'post',
      url: `${BASE_URL}/auth/login`,
      headers: {
        'Content-Type': 'application/json',
      },
      auth: {
        username: data.username,
        password: data.password,
      },
      data,
    };
    return instance(config);
  },
  checkOldPassword: async (data: UserLogin) => {
    const config: AxiosRequestConfig = {
      method: 'post',
      url: `${BASE_URL}/auth/login`,
      headers: {
        'Content-Type': 'application/json',
      },
      auth: {
        username: data.username,
        password: data.password,
      },
      data,
    };
    return axios(config)
  },
  resetPassword:  async (data: {
    password: string;
    email: string;
  }, memberId:string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/auth/${memberId}/passwordrest`,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        Authorization: `Bearer ${TOKEN()}`,
      },
      data,
    };

    return instance(config);
  },
  // eslint-disable-next-line consistent-return
  session: async (token?:string) => {
    try {
      const config: AxiosRequestConfig = {
        method: 'get',
        url: `${BASE_URL}/auth/session`,
        headers: {
          'Content-Type': 'application/json',
          Authorization: `Bearer ${TOKEN() || token}`,
        },
      };
      return instance(config);
    } catch (error) {
      console.log(error);
    }
  },

};
