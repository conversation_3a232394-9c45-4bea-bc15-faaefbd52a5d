/* eslint-disable import/prefer-default-export */
import { AxiosRequestConfig } from 'axios';
// eslint-disable-next-line import/no-cycle
import {
  BASE_URL, ORG_ID, TOKEN, instance,
  rawInstance,
} from 'api/src/lib/api';
import {
  AddUsersToTeamsRequest,
  CreateTeamLeadsRequest,
  CreateTeamRequest,
  FetchTeamHouseholdsRequest,
  RemoveTeamRequest,
} from '../types';

export const TEAMS = {
  addUsersToTeams: async (data: AddUsersToTeamsRequest) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/teams/${data.teamId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: data.body,
    };
    return instance(config);
  },

  fetchTeams: async (
    token?: string,
    orgId?: string,
    pageSize?: number,
    page?: number,
    sortBy?: string,
    sortDirection?: string,
    search?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/teams?org=${ORG_ID() || orgId}&per=${
        pageSize || 15
      }&page=${page || 1}&sortBy=${sortBy || 'createdAt'}&sortDirection=${
        sortDirection || 'desc'
      }&search=${search || ''}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchTeamHouseholds: async (data: FetchTeamHouseholdsRequest, token?:string, orgId?:string, search?:string, per = 5, page = 1, sortBy = 'createdAt', sortDirection = 'desc') => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/teams/${data.teamId}/households/page?org=${ORG_ID() || orgId}&per=${per}&page=${page}&sortBy=${sortBy}&sortDirection=${sortDirection}&search=${search}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  fetchSingleTeam: async (teamId: string, token?: string, orgId?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/teams/${teamId}?org=${ORG_ID() || orgId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  createTeam: async (data: CreateTeamRequest) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/teams`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: data.body,
    };
    return instance(config);
  },

  createTeamLeads: async (data: CreateTeamLeadsRequest) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/teams/${data.teamId}/leads`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: data.body,
    };
    return instance(config);
  },

  removeTeam: async (data: RemoveTeamRequest) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/teams/${data.teamId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },
  updateTeam: async (teamId: string, data: any) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/teams/${teamId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: data.body,
    };
    return instance(config);
  },
  removeTeamFromHousehold: async (householdId: string, teamId: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/households/${householdId}/teams`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: {
        teams: [teamId],
      },
    };
    return rawInstance(config);
  },
  assignTeamToHousehold: async (householdId: string, teamId: string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/households/${householdId}/teams`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: {
        teams: [teamId],
      },
    };
    return rawInstance(config);
  },
};
