// eslint-disable-next-line import/no-cycle
import { BASE_URL, TOKEN, instance } from 'api/src/lib/api';
import { AxiosRequestConfig } from 'axios';

// eslint-disable-next-line import/prefer-default-export
export const NOTES = {
  createNote: async (body:any) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/notes`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: body,
    };
    return instance(config);
  },
  getNote: async (noteId:string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/notes/${noteId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  deleteNote: async (noteId:string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/notes/${noteId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  updateNote: async (noteId:string, body:any, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/notes/${noteId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: body,
    };
    return instance(config);
  },
  deleteAttachments: async (attachmentIds: string[], token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/attachments/batchDelete`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: {
        ids: attachmentIds,
      },
    };
    return instance(config);
  },

};
