// eslint-disable-next-line import/no-cycle
import { BASE_URL, TOKEN, instance } from 'api/src/lib/api';
import axios, { AxiosRequestConfig } from 'axios';

// eslint-disable-next-line import/prefer-default-export
export const TASKS = {
  fetchTasks: async (orgId: string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/tasks?page=0&per=10&org=${orgId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    const request = await axios(config);
    return request;
  },
  fetchTasksPage: async (orgId: string, userId: string, token?:string, pageSize?: number, page?: number) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/tasks/page?org=${orgId}&userId=${userId}&page=${page}&perPage=${pageSize}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    const request = await axios(config);
    return request;
  },
  fetchSingleTask: async (id:string, orgId: string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/tasks/${id}?org=${orgId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    const request = await instance(config);
    return request;
  },
  updateStatus: async (taskId: string, status: string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/tasks/${taskId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: {
        status,
      },
    };
    const request = await axios(config);
    return request.data;
  },
  updateTask: async (taskId: string, data: any, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/tasks/${taskId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    const request = await instance(config);
    return request;
  },
  deleteTask: async (taskId: string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/tasks/${taskId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    const request = await instance(config);
    return request;
  },
  markTaskAsCompleted: async (taskId: string, data:any, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/tasks/${taskId}/complete`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    const request = await instance(config);
    return request;
  },
  createTask: async (data: any, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/tasks`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    const request = await instance(config);
    return request;
  },
  dropTask: async (taskId: string, data: any, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/tasks/${taskId}/drop`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    const request = await instance(config);
    return request;
  },
  pickupTask: async (taskId: string, data: any, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/tasks/${taskId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    const request = await instance(config);
    return request;
  },
};
