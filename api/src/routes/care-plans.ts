/* eslint-disable default-param-last */
/* eslint-disable import/no-extraneous-dependencies */
/* eslint-disable import/prefer-default-export */
// eslint-disable-next-line import/no-cycle
import {
  BASE_URL,
  ORG_ID,
  TOKEN,
  instance,
  rawInstance,
} from 'api/src/lib/api';

import { AxiosRequestConfig, AxiosResponse } from 'axios';
// eslint-disable-next-line import/no-cycle

export const CAREPLANS = {
  fetchMemberCarePlans: async (
    memberId: string,
    token?: string,
    orgId?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/api/members/${memberId}/careplans`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  createCarePlan: async (memberId: string, data: any, token?: string) => {
    const date = new Date();
    const isoWithoutMillis = date.toISOString().replace(/\.\d{3}Z$/, 'Z');
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/api/members/${memberId}/careplans`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: {
        title: data.title || 'New Care Plan',
        startDate: isoWithoutMillis,
        lastReviewed: isoWithoutMillis,
        nextReviewDate: isoWithoutMillis,
        outcome: 'Initial assessment completed',
        status: data.status || 'active',
      },
    };
    return instance(config);
  },
  getCarePlan: async (carePlanId: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/api/careplans/${carePlanId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  createCarePlanSection: async (
    carePlanId: string,
    type: string,
    fields: any,
    token?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/api/careplans/${carePlanId}/${type}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: fields,
    };
    return instance(config);
  },
  deleteCarePlanSection: async (
    carePlanId: string,
    sectionType: string,
    sectionId: string,
    token: string,
  ): Promise<AxiosResponse<any>> => rawInstance({
    method: 'DELETE',
    url: `${BASE_URL}/api/careplans/${carePlanId}/${sectionType}/${
      sectionType === 'team-members' ? 'bulk' : sectionId
    }`,
    headers: {
      Authorization: `Bearer ${token}`,
    },
    validateStatus: (status) => (status >= 200 && status < 300) || status === 204, // accept 204
  }),
  cancelCarePlanDraft: async (carePlanId: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/api/careplans/${carePlanId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  updateCarePlan: async (carePlanId: string, data: any, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/api/careplans/${carePlanId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    return instance(config);
  },
  deleteCarePlan: async (carePlanId: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/api/careplans/${carePlanId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  searchWHODiagnosis: async (search: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/whoicd/search`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: {
        query: search,
        language: 'en',
      },
    };
    return instance(config);
  },
  searchMedication: async (search: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/rxnav/search/approximate`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: {
        name: search,
        includeDetails: true,
      },
    };
    return instance(config);
  },
  fetchMedicationDetails: async (rxcui: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/rxnav/details`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: {
        rxcui,
      },
    };
    return instance(config);
  },
  getCarePlanSection: async (
    carePlanId: string,
    sectionType: string,
    token?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/api/careplans/${carePlanId}/${sectionType}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  getCarePlanItemsByType: async (
    carePlanId: string,
    type: string,
    token?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/api/careplans/${carePlanId}/${type}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  getCarePlanItem: async (
    carePlanId: string,
    type: string,
    itemId: string,
    token?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/api/careplans/${carePlanId}/${type}/${itemId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  deleteCarePlanItem: async (
    carePlanId: string,
    type: string,
    itemId: string,
    token?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/api/careplans/${carePlanId}/${type}/${itemId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  updateCarePlanItem: async (
    carePlanId: string,
    type: string,
    itemId: string,
    data: any,
    token?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/api/careplans/${carePlanId}/${type}/${itemId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    return instance(config);
  },
  getCarePlanTimelineItems: async (carePlanId: string, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/api/careplans/${carePlanId}/timeline-items`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
};
