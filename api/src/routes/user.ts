import { PasswordReset, UserUpdate, UserAttachment } from 'api/src/types';
import { AxiosRequestConfig } from 'axios';
// eslint-disable-next-line import/no-cycle
import {
  BASE_URL, ORG_ID, TOKEN, instance,
} from 'api/src/lib/api';

// eslint-disable-next-line import/prefer-default-export
export const USER = {
  getAllUsers: async (token?:string, orgId?: string, per = 10, page = 1, sortBy = 'firstName', sortDirection = 'desc', search = '') => {
    const config: AxiosRequestConfig = {
      method: 'get',
      url: `${BASE_URL}/users?org=${ORG_ID() || orgId}&per=${per}&page=${page}&sortBy=${sortBy}&sortDirection=${sortDirection}&search=${search}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  getUser: async (userId: string, token?:string, orgId?:string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/users/${userId}?per=200&org=${ORG_ID() || orgId || ''}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  getUserChats: async (userId: string, token: string) => {
    const config: AxiosRequestConfig = {
      method: 'get',
      url: `${BASE_URL}/users/${userId}/chats`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },

  resetPassword: async (userId: string, data: PasswordReset) => {
    const config: AxiosRequestConfig = {
      method: 'put',
      url: `${BASE_URL}/auth/${userId}/passwordrest`,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      data,
    };
    return instance(config);
  },

  updateUser: async (userId: string, data: UserUpdate) => {
    const config: AxiosRequestConfig = {
      method: 'put',
      url: `${BASE_URL}/users/${userId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },

  createUserAttachment: async (userId: string, data: UserAttachment) => {
    const config: AxiosRequestConfig = {
      method: 'post',
      url: `${BASE_URL}/users/${userId}/attachments`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },
  delete: async (userId: string) => {
    const config: AxiosRequestConfig = {
      method: 'delete',
      url: `${BASE_URL}/users/${userId}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },
  fetchNavigatorTasks: async (navigatorId: string, orgId?: string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/tasks/filter`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data: {
        orgID: orgId,
        completedBy: [navigatorId],
      },
    };
    return instance(config);
  },
};
