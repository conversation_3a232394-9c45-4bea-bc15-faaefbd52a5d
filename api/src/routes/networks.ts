// eslint-disable-next-line import/no-cycle
import {
  BASE_URL, ORG_ID, TOKEN, instance,
} from 'api/src/lib/api';
import {
  CreateAddressBody,
  CreateNetworkRequestBody,
  CreateServiceBody,
} from 'api/src/types';
import { AxiosRequestConfig } from 'axios';

// eslint-disable-next-line import/prefer-default-export
export const NETWORKS = {
  fetchNetworks: async (
    token?: string,
    orgId?: string,
    // type?: string,
    // params?: string, ------ type and params used by 'FilterNetworkButton' component,
    // but component is not used in the project currently
    per?: number,
    page?: number,
    sortBy?: string,
    sortDirection?: string,
    search?: string,
    type?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'get',
      url: `${BASE_URL}/networks/page?org=${ORG_ID() || orgId}&per=${
        per || 15
      }&page=${page || 1}&sortBy=${sortBy || 'createdAt'}&sortDirection=${
        sortDirection || 'desc'
      }&search=${search || ''}&type=${type || ''}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);

    // --- Old call with type and params:
    // fetchNetworks: async (token?:string, orgId?: string, type?:string, params?:string) => {
    //   const config: AxiosRequestConfig = {
    //     method: 'get',
    // eslint-disable-next-line max-len
    //     url: `${BASE_URL}/networks?org=${ORG_ID() || orgId}&per=10000${type ? `&type=${type}` : ''}&${params || ''}`,
  },
  fetchNetworksNoPage: async (
    token?: string,
    orgId?: string,
    type?: string,
    // params?: string, ------ params used by 'FilterNetworkButton' component,
    // but component is not used in the project currently
    search?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'get',
      url: `${BASE_URL}/networks?org=${ORG_ID() || orgId}&type=${type || ''}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);

    // --- Old call with type and params:
    // fetchNetworks: async (token?:string, orgId?: string, type?:string, params?:string) => {
    //   const config: AxiosRequestConfig = {
    //     method: 'get',
    // eslint-disable-next-line max-len
    //     url: `${BASE_URL}/networks?org=${ORG_ID() || orgId}&per=10000${type ? `&type=${type}` : ''}&${params || ''}`,
  },
  fetchNetwork: async (token: string, networkID: string) => {
    const config: AxiosRequestConfig = {
      method: 'get',
      url: `${BASE_URL}/networks/${networkID}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  createNetwork: async (request: CreateNetworkRequestBody) => {
    const config: AxiosRequestConfig = {
      method: 'post',
      url: `${BASE_URL}/networks`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: request,
    };
    return instance(config);
  },
  createAddress: async (networkID: string, body: CreateAddressBody) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/networks/${networkID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: body,
    };
    return instance(config);
  },
  createService: async (body: CreateServiceBody) => {
    const config: AxiosRequestConfig = {
      method: 'post',
      url: `${BASE_URL}/services`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: body,
    };
    return instance(config);
  },

  addServices: async (networkID: string, serviceIDs: string[]) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/networks/${networkID}/service`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: {
        services: serviceIDs,
      },
    };
    return instance(config);
  },
  updateNetwork: async (networkID: string, body: any) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/networks/${networkID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
      data: body,
    };
    return instance(config);
  },
  delete: async (networkID: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/networks/${networkID}`,
      headers: {
        Authorization: `Bearer ${TOKEN()}`,
      },
    };
    return instance(config);
  },
  fetchOrgNetworks: async (
    token?: string,
    orgId?: string,
    per?: number,
    page?: number,
    sortBy?: string,
    sortDirection?: string,
    name?: string,
    type?: string,
  ) => {
    const config: AxiosRequestConfig = {
      method: 'get',
      url: `${BASE_URL}/networks/organization/${ORG_ID() || orgId}?per=${
        per || 15
      }&page=${page || 1}&sortBy=${sortBy || 'createdAt'}&sortDirection=${
        sortDirection || 'desc'
      }&name=${name || ''}&type=${type || ''}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
};
