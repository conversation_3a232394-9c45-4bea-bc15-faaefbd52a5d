// eslint-disable-next-line import/no-cycle
import { BASE_URL, TOKEN, instance } from 'api/src/lib/api';
import { AxiosRequestConfig } from 'axios';

// eslint-disable-next-line import/prefer-default-export
export const CHATS = {
  fetchChats: async (navigatorId:string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/users/${navigatorId}/chats`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  fetchMemberChats: async (navigatorId:string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/members/${navigatorId}/chats?per=100`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  fetchSingleChat: async (chatId:string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/conversations/${chatId}/messages?page=0&size=100`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  fetchChatDetails: async (chatId:string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/chats/${chatId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
  updateChatTitle: async (chatId:string, data:object, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/chats/${chatId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    return instance(config);
  },
  createChat: async (data:object, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/chats`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    return instance(config);
  },
  sendMessage: async (data:object, chatId:string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/chats/${chatId}/message`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    return instance(config);
  },
  deleteChat: async (chatId:string, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/chats/${chatId}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
};
