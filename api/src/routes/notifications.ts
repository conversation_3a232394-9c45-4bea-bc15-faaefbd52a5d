import { AxiosRequestConfig } from 'axios';
// eslint-disable-next-line import/no-cycle
import {
  BASE_URL, TOKEN, instance,
} from 'api/src/lib/api';
import { 
  NotificationFilters, 
  MarkNotificationsReadRequest, 
  DeleteNotificationsRequest 
} from 'types/notification-types';

// eslint-disable-next-line import/prefer-default-export
export const NOTIFICATIONS = {
  getNotifications: async (filters: NotificationFilters, token?: string) => {
    const { per = 10, read = false, userID, page = 1 } = filters;
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/notifications?per=${per}&read=${read}&userID=${userID}&page=${page}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },

  markNotificationsAsRead: async (data: MarkNotificationsReadRequest, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'PUT',
      url: `${BASE_URL}/notifications`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },

  deleteNotifications: async (data: DeleteNotificationsRequest, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'DELETE',
      url: `${BASE_URL}/notifications`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
        'Content-Type': 'application/json',
      },
      data,
    };
    return instance(config);
  },

  getNotificationCount: async (userID: string, read?: boolean, token?: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/notifications/count?userID=${userID}${read !== undefined ? `&read=${read}` : ''}`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
    };
    return instance(config);
  },
};
