// eslint-disable-next-line import/no-cycle
import { BASE_URL, TOKEN, instance } from 'api/src/lib/api';
import { AxiosRequestConfig } from 'axios';

// eslint-disable-next-line import/prefer-default-export
export const REPORTS = {
  fetchReporting: async (data: any, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/api/dashboard`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    const request = await instance(config);
    return request;
  },
  exportReporting: async (data: any, token?:string) => {
    const config: AxiosRequestConfig = {
      method: 'POST',
      url: `${BASE_URL}/api/dashboard/export`,
      headers: {
        Authorization: `Bearer ${TOKEN() || token}`,
      },
      data,
    };
    const request = await instance(config);
    return request;
  },
};
