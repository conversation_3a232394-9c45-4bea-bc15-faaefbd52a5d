// eslint-disable-next-line import/no-cycle
import { BASE_URL } from 'api/src/lib/api';
import axios, { AxiosRequestConfig } from 'axios';

// eslint-disable-next-line import/prefer-default-export
export const CONSTANTS = {
  fetchConstants: async () => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/constants`,
    };
    const response = await axios(config);
    return response.data;
  },
  fetchOrgConstants: async (orgId: string) => {
    const config: AxiosRequestConfig = {
      method: 'GET',
      url: `${BASE_URL}/orgs/${orgId}/constants`,
    };
    const response = await axios(config);
    return response.data;
  },
};
