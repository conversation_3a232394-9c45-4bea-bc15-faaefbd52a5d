export interface UserSignUp {
    username: string;
    password: string;
    firstName: string;
    lastName: string;
    roles: string[];
    orgID: string;
}

export interface UserLogin {
    username: string;
    password: string;
}

export interface PasswordReset {
    email: string;
    password: string;
}

export interface UserUpdate {
    firstName?: string;
    lastName?: string;
    roles?: string[];
    phone?: string;
    meta?: any;
    profile?: string;
    color?: string;
    teams?: string[];
}

export interface UserAttachment {
    type: string;
    name: string;
    kind:string;
    base64: string;
}

export interface MemberAttachment {
    type: string;
    name: string;
    base64: string;
}

export interface MemberCreate {
    orgID: string;
    email: string;
    firstName: string;
    middleName: string;
    lastName: string;
    type: string;
    roles: string[];
    dob: string;
    gender: string;
    ethnicity: string;
    sexualIdentity: string;
    genderIdentity: string;
    pronouns: string;
    lang: string;
    referredBy: string;
    status: string;
}

export interface StudentCreate {
    orgID: string;
    email: string;
    firstName: string;
    middleName: string;
    lastName: string;
    type: string;
    roles: string[];
    dob: string;
    gender: string;
    ethnicity: string;
    sexualIdentity: string;
    genderIdentity: string;
    pronouns: string;
    lang: string;
    referredBy: string;
    status: string;
    school: {
        name: string;
        grade: string;
    };
}

export interface MemberAddress {
    street: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    kind: string;
}

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface FetchHouseholdsRequest {}

export interface FetchSingleHouseholdRequest {
  householdId: string;
}

export interface CreateHouseholdRequestBody {
  orgID: string;
  title: string;
  type: string;
  kind: string;
  headOfHouse: object;
}

export interface UpdateHouseHoldRequestBody {
  householdId: string;
  body:{
    title?:string;
    address?: MemberAddress;
    members?: string[];
    teams?: string[];
  }
}
export interface CreateHouseholdRequest {
  body: CreateHouseholdRequestBody;
}
export interface AddMemberToHouseholdRequest{
  householdId: string;
  body: {
    members: string[];
  }
}
export interface AddTeamToHouseholdRequest{
  householdId: string;
  body: {
    teams: string[];
  }
}
export interface CreateHouseholdAttachmentRequestBody {
  type: string;
  name: string;
  base64: string;
}

export interface CreateHouseholdAttachmentRequest {
  householdId: string;
  body: CreateHouseholdAttachmentRequestBody;
}

export interface RemoveTeamFromHouseholdRequestBody {
  teams: string[];
}

export interface RemoveTeamFromHouseholdRequest {
  householdId: string;
  body: RemoveTeamFromHouseholdRequestBody;
}

export interface RemoveHeadOfHouseRequest {
  householdId: string;
}

export interface RemoveMemberFromHouseholdRequest {
  householdId: string;
  memberId: string;
}

export interface AddUsersToTeamsRequestBody {
    navigators: string[];
  }

export interface AddUsersToTeamsRequest {
    teamId: string;
    body: AddUsersToTeamsRequestBody;
  }

// eslint-disable-next-line @typescript-eslint/no-empty-interface
export interface FetchTeamsRequest {}

export interface FetchTeamHouseholdsRequest {
    teamId: string;
  }

export interface CreateTeamRequestBody {
    orgID: string;
    name: string;
    address: {
      street?: string;
      city?: string;
      state?: string;
      zip?: string;
      kind: string;
    };
    navigators: string[];
  }

export interface CreateTeamRequest {
    body: CreateTeamRequestBody;
  }

export interface CreateTeamLeadsRequestBody {
    leads: string[];
  }

export interface CreateTeamLeadsRequest {
    teamId: string;
    body: CreateTeamLeadsRequestBody;
  }

export interface RemoveTeamRequest {
    teamId: string;
  }

export interface Network {
  phones: any[];
  id: string;
  updatedAt: string;
  status: string;
  contact?: string;
  types: string[];
  address: any[];
  createdAt: string;
  services: Service[];
  name: string;
  org: Org;
  website?: any;
}

interface Service {
  updatedAt: string;
  status: string;
  desc: string;
  id: string;
  type: string;
  name: string;
  createdAt: string;
  service: string;
  org: Org;
}

export interface CreateNetworkRequestBody {
  orgID: string;
  name: string;
  types: string[];
  status: string;
  contact: string;
  website?: string;
  carriers?: string[];
}
interface Address{
    street: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    kind: string;
}
export interface CreateAddressBody {
  address:Address;
  phone: {
    number: string;
    label: string
   }
}
export interface CreateServiceBody {
  orgID: string;
  name:string;
  desc:string;
  service:string;
}
interface Org {
  id: string;
}
