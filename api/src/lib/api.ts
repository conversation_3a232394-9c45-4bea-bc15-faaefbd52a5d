/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-console */
import Cookies from 'js-cookie';
/* eslint-disable import/no-cycle */
import axios from 'axios';
import { Auth } from 'api/src/routes/auth';
import { USER } from 'api/src/routes/user';
import { MEMBERS } from 'api/src/routes/members';
import { TEAMS } from 'api/src/routes/teams';
import { HOUSEHOLDS } from 'api/src/routes/household';
import { CONSTANTS } from 'api/src/routes/constants';
import ErrorService from 'api/src/lib/error-service';
import { NETWORKS } from 'api/src/routes/networks';
import { NOTES } from 'api/src/routes/notes';
import { TASKS } from 'api/src/routes/tasks';
import { Assessments } from 'api/src/routes/assessments';
import { REPORTS } from 'api/src/routes/reports';
import { CHATS } from '../routes/chats';
import { SOCIALPLANS } from '../routes/social-plans';
import { CAREPLANS } from '../routes/care-plans';
import { NOTIFICATIONS } from '../routes/notifications';

export const BASE_URL = 'https://donahealth-stag01.duploapps.dona.health';
export const TOKEN = () => Cookies.get('token');
export const ORG_ID = () => Cookies.get('orgID');
// axios instance
export const instance = axios.create({
  baseURL: BASE_URL,
});
export const rawInstance = axios.create({
  baseURL: BASE_URL,
});
export class CustomError extends Error {
  data: any;

  constructor(message:string, data:any) {
    super(message);
    this.data = data;
  }
}
instance.interceptors.request.use(
  (config) => {
    // Do something before request is sent
    if (ErrorService.handleRequest) {
      ErrorService.handleRequest(config);
    }
    // Important: request interceptors **must** return the request.
    return config;
  },
);
instance.interceptors.response.use(
  (response) => {
    // console.log(response.status, 'response');
    // console.log(response, 'response data');
    ErrorService.handleResponse(response);
    return response.data;
  },
  // Any status code that lies within the range of 2xx cause this function to trigger
  // Do something with response data
  // eslint-disable-next-line implicit-arrow-linebreak
  (error) => {
    // console.log(error, 'error');
    // Any status codes that falls outside the range of 2xx cause this function to trigger
    // You can handle errors based on error response here
    if (error.response) {
      if (error.response.status === 401) {
        if (typeof window !== 'undefined' && window?.location?.pathname === '/login') {
          throw new CustomError('Auth Error', { status: error.response.status, ...error.response.data });
        }
        // Redirect to login page if running in a browser environment
        if (typeof window !== 'undefined') {
          window.location.href = '/login';
        }
      }
      console.log(error);
      ErrorService.handleError(error);
    } else if (error.request) {
      console.log(error, 'error request');
      // The request was made but no response was received
      // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
      // http.ClientRequest in node.js
    } else {
      // Something happened in setting up the request that triggered an Error
      console.log('Error', error.message);
    }
  },
);

const API = {
  AUTH: {
    ...Auth,
  },
  ASSESSMENT: {
    ...Assessments,
  },
  TASKS: {
    ...TASKS,
  },
  REPORTS: {
    ...REPORTS,
  },
  USER: {
    ...USER,
  },
  MEMBERS: {
    ...MEMBERS,
  },
  SOCIALPLANS: {
    ...SOCIALPLANS,
  },
  TEAMS: {
    ...TEAMS,
  },
  HOUSEHOLDS: {
    ...HOUSEHOLDS,
  },
  NETWORKS: {
    ...NETWORKS,
  },
  NOTES: {
    ...NOTES,
  },
  CHATS: {
    ...CHATS,
  },
  CONSTANTS: {
    ...CONSTANTS,
  },
  CAREPLANS: {
    ...CAREPLANS,
  },
  NOTIFICATIONS: {
    ...NOTIFICATIONS,
  },
  // ... more API methods here
};

export default API;
