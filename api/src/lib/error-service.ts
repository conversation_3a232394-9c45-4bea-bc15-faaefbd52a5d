/* eslint-disable @typescript-eslint/no-explicit-any */
let modalMethods = {
  showModal: (title:string, message:string) => undefined,
  showLoading: () => undefined,
  hideLoading: () => undefined,
};

const ErrorService = {
  mounted: false,
  setModalMethods: (methods:any) => {
    modalMethods = methods;
  },
  handleError: (error:any) => {
    modalMethods.showModal('Error', error?.response?.data?.reason || error?.message || 'Unknown error');
    modalMethods.hideLoading();
    // Handle other error statuses...
  },
  handleRequest: (request:any) => {
    modalMethods.showLoading();
  },
  handleResponse: (response:any) => {
    modalMethods.hideLoading();
  },
};

export default ErrorService;
