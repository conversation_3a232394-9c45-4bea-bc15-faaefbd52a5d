/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { <PERSON><PERSON>, TextField } from '@mui/material';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import AddressFormModal from 'components/address-collect/address-form-modal';
import BannerPicker from 'components/banner-picker/banner-picker';
import AddMemberDrawer from 'components/members/add-update/add-members-drawer';
import SelectMember from 'components/members/select-member/select-member';
import CustomTable from 'components/table-components/table';
import SelectTeams from 'components/teams/select-teams/select-teams';
import { useModal } from 'contexts/modal-context/modal-context';
import useHouseholdFormHandler from 'logics/household/use-form-handler';
import useSelectMember from 'logics/member/use-select-member';
import useSelectTeam from 'logics/team/use-select-team';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { HouseHold } from 'types/household-types';
import { capitalizeWords, getLatestImage, isEmptyObject } from 'utils/helper';
import Check from '@mui/icons-material/Check';
import API from 'api/src/lib/api';

interface Props {
  household: HouseHold;
  add: boolean;
}

function HouseholdAddUpdate({ household, add }: Props) {
  const { showModal } = useModal();
  const [openAddress, setOpenAddress] = useState(false);
  const {
    formik,
    picture,
    setPicture,
    selectedMembers,
    selectedTeams,
    setSelectedMembers,
    setSelectedTeams,
    setMember,
    member,
  } = useHouseholdFormHandler(add, household.id || '');

  useEffect(() => {
    if (!isEmptyObject(formik?.errors)) {
      if (picture === '') {
        showModal('Missing Fields', 'Please select a image');
      }
      showModal(
        'Missing Fields',
        `
            ${Object.keys(formik?.errors).map(
    (key) => `${(formik as any)?.errors[key]}`,
  )}
          `,
      );
    }
  }, [JSON.stringify(formik?.errors)]);
  const [addressString, setAddressString] = useState('');

  useEffect(() => {
    if (!isEmptyObject(household)) {
      // eslint-disable-next-line no-shadow
      const address: any = household.address.length > 0 && household.address[0];
      // Populate formik values with data from the selected household
      formik.setValues({
        title: household.title,
        street1: address?.street || '',
        street2: address?.street2 || '',
        city: address?.city || '',
        state: address?.state || '',
        zip: address?.zip || '',

        // other values...
      });
      const attachment = getLatestImage(
        household.attachments,
        'householdProfile',
      );
      setPicture(attachment);
      setSelectedMembers(household?.members as any);
      setSelectedTeams(household?.teams as any);
      setAddressString(
        capitalizeWords(
          `${address?.street.trim()}${
            address?.street2 ? ` ${address?.street2.trim()}` : ''
          }, ${address?.city.trim()}, ${address?.state.trim()} ${address?.zip.trim()}`,
          true,
        ),
      );
    }
  }, [household]);

  const {
    MemberSelectorWidget,
    open,
    setOpen,
    openAddMember,
    setOpenAddMember,
  } = useSelectMember(selectedMembers);
  const SelectTeamHook = useSelectTeam(selectedTeams);
  const router = useRouter();

  const handleRemoveTeam = (team: any) => {
    setSelectedTeams((prev) => prev.filter((t) => t.id !== team.id));
  };

  const handleOnDelete = (item: HouseHold) => {
    showModal('Warning', 'Are you sure to delete this household?', async () => {
      await API.HOUSEHOLDS.delete(item.id);
      router.replace('/households');
    });
  };

  return (
    <>
      <div className="grid grid-cols-3 gap-5 pr-5">
        <div className="col-span-3 items-center grid grid-flow-col auto-cols-auto place-content-between w-full mt-4 ml-4">
          <p className="text-[21px] p-0 m-0 font-[600] col-span-3">
            <span
              className="font-[300] cursor-pointer"
              onClick={() => {
                router.replace('/households');
              }}
            >
              Households
            </span>
            <span className="font-[300]">{' > '}</span>
            <span>
              {add ? 'Add Household' : `Update Household (${household?.title})`}
            </span>
          </p>
          <div className="flex gap-3">
            <Button
              onClick={() => handleOnDelete(household)}
              component="label"
              className="rounded-md m-0 text-[15px] text-[#E42B57] grey-btn"
              sx={{
                padding: '11px 15px !important', // Works correctly
              }}
            >
              <DeleteOutlinedIcon
                style={{ width: '16px', height: '16px' }}
                className="mr-2"
              />
              Delete Household
            </Button>
            <Button
              onClick={formik.submitForm}
              variant="contained"
              className="rounded-md h-11  font-[500] text-sm mr-4 !min-w-[100px]"
            >
              <Check style={{ width: '16px', height: '16px' }} className="mr-2" />
              <span className="font-[500]">Save</span>
            </Button>
          </div>
        </div>

        <p className="text-lg p-0 m-0 font-[600] col-span-3 ml-9">
          Household Details
        </p>
        <div className="col-span-3 ml-9">
          <BannerPicker
            picture={picture}
            setPicture={setPicture}
            className="w-[400px]"
          />
        </div>
        <TextField
          value={formik.values.title}
          name="title"
          onChange={formik.handleChange}
          placeholder="Enter Title"
          id="filled-basic"
          label="Title"
          variant="filled"
          className="ml-9 mb-[48px] w-[400px]"
        />
        {/* <TextField
          value={formik.values.street1}
          name="street1"
          onChange={formik.handleChange}
          placeholder="Enter Street 1"
          id="filled-basic"
          label="Street 1"
          variant="filled"
        />
        <TextField
          value={formik.values.street2}
          name="street2"
          onChange={formik.handleChange}
          placeholder="Enter Street 2"
          id="filled-basic"
          label="Street 2"
          variant="filled"
        />
        <TextField
          value={formik.values.city}
          name="city"
          onChange={formik.handleChange}
          placeholder="Enter City"
          id="filled-basic"
          label="City"
          variant="filled"
        />
        <SelectBox
          keyVal="state"
          defaultValue={formik.values.state}
          onChange={formik.handleChange}
          items={constant?.states}
          label="State"
        />
        <TextField
          value={formik.values.zip}
          name="zip"
          onChange={formik.handleChange}
          placeholder="Enter Zip Code"
          id="filled-basic"
          label="Zip Code"
          variant="filled"
        /> */}
      </div>
      <div className="ml-9 pr-10">
        {formik.values.street1 ? (
          <div className="grid grid-flow-col auto-cols-auto  items-center  place-content-between">
            <p className="text-lg font-[600] m-0 p-0">Address</p>
            <p className="m-0 p-0 text-[14px] mt-2">
              <span
                onClick={() => {
                  setOpenAddress(true);
                }}
                className="text-primary  font-[500] cursor-pointer"
              >
                Edit Address
              </span>
            </p>
          </div>
        ) : (
          <div className="grid mb-[48px]">
            <p className="text-lg font-[600] m-0 p-0">Address</p>

            <p className="text-[#646F79] text-sm mt-2">
              No address has been added for this household.
              <span
                onClick={() => {
                  setOpenAddress(true);
                }}
                className="text-primary  font-[500] cursor-pointer"
              >
                {' + Add Address'}
              </span>
            </p>
          </div>
        )}
      </div>
      {formik.values.street1 && (
        <div className="ml-9 mt-2 mb-[48px] pr-10">
          <CustomTable
            source={[
              {
                address: addressString,
              },
            ]}
            variant="address-table"
            style={{
              border: 'solid 1px #DCDFDF',
              borderRadius: '10px',
              borderBottom: 'none',
            }}
            headCellStyle={{
              padding: '4px 16px',
              fontSize: '13px',
              color: '#747A7A',
            }}
            tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
          />
        </div>
      )}
      <div className="ml-9 mb-[48px] pr-10">
        <MemberSelectorWidget />
        {selectedMembers.length > 0 && (
          <div className="mt-2">
            <CustomTable
              setSelected={setSelectedMembers}
              source={selectedMembers}
              metadata={{
                householdId: (household as any)?.id as string,
                householdTitle: household.title,
                headId: (household as any).headOfHouse?.id || '',
                setSelected: setSelectedMembers,
                list: [],
              }}
              variant="addsection-member-table"
              style={{
                border: 'solid 1px #DCDFDF',
                borderRadius: '10px',
                borderBottom: 'none',
                borderTop: 'none',
              }}
              headCellStyle={{
                padding: '4px 16px',
                fontSize: '13px',
                color: '#747A7A',
                borderBottom: 'solid 1px #DCDFDF',
              }}
              tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
            />
          </div>
        )}
        <AddressFormModal
          open={openAddress}
          setOpen={setOpenAddress}
          formik={formik}
          setAddressString={setAddressString}
        />
        <SelectMember
          selectedMembers={selectedMembers}
          open={open}
          setOpen={setOpen}
          setSelectMembers={setSelectedMembers}
          title="Select Member(s) to Add to Household"
        />
      </div>
      <div className="ml-9 mb-[48px] pr-10">
        <SelectTeamHook.TeamSelectorWidget />
        {selectedTeams.length > 0 && (
          <div className="mt-2">
            <CustomTable
              source={selectedTeams}
              variant="addsection-team-table"
              style={{
                border: 'solid 1px #DCDFDF',
                borderRadius: '10px',
                borderBottom: 'none',
                borderTop: 'none',
              }}
              headCellStyle={{
                padding: '4px 16px',
                fontSize: '13px',
                color: '#747A7A',
                borderBottom: 'solid 1px #DCDFDF',
              }}
              tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
              handleDelete={handleRemoveTeam}
            />
          </div>
        )}
        <SelectTeams
          selectedTeams={selectedTeams}
          setSelectTeams={setSelectedTeams}
          open={SelectTeamHook.open}
          setOpen={SelectTeamHook.setOpen}
        />
        <AddMemberDrawer
          status={[]}
          setMember={setMember}
          member={member}
          setSelectedMembers={setSelectedMembers}
          open={openAddMember}
          setOpen={setOpenAddMember}
        />
      </div>
    </>
  );
}

export default HouseholdAddUpdate;
