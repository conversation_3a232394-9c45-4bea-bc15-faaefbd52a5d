/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { Drawer } from '@mui/material';
// import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { isEmptyObject } from 'utils/helper';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import React from 'react';
import HouseholdAddUpdate from 'components/households/add-update';
import { HouseHold } from 'types/household-types';

interface AddHouseholdDrawerProps {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    household: HouseHold;
    setHousehold: React.Dispatch<React.SetStateAction<HouseHold>>;

}

function AddHouseholdDrawer({
  open, household, setOpen, setHousehold,
}: AddHouseholdDrawerProps) {
  const handleClose = () => {
    setOpen(false);
    setHousehold({} as any);
  };

  return (
    <Drawer
      anchor="bottom"
      sx={{
        '& .MuiDrawer-paper': {
          height: '100%',
          width: '100%',
        },
      }}
      open={open}
      onClose={handleClose}
    >
      <div className="w-screen bg-white pb-32">
        <div
          style={{
            position: 'fixed',
            width: '100%',
            backgroundColor: '#fff',
            zIndex: 2,
            borderBottom: '1px solid #E0E0E0',
          }}
          className="grid grid-flow-col
                 gap-8 p-3 pl-5 auto-cols-max
                 items-center relative"
        >
          <CancelOutlinedIcon
            onClick={handleClose}
            style={{ width: 35, height: 35 }}
            className="text-[#646F79] text-xl cursor-pointer"
          />
          <h1 className="font-[500] text-xl">
            {
          isEmptyObject(household) ? 'Add Household' : 'Update Household'
}
          </h1>
        </div>
        <div className="pt-20 pl-20 pr-20 grid gap-7">
          <HouseholdAddUpdate household={household} add={isEmptyObject(household)} />
        </div>

      </div>

    </Drawer>
  );
}

export default AddHouseholdDrawer;
