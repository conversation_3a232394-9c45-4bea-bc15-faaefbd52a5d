import AvatarWithOverlay from 'components/avatar-with-overlay';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import useScore from 'hooks/use-score';
import { useContext } from 'react';
import { Member } from 'types/members-response-type';
import { capitalizeWords, calculateAge, getKeyValue } from 'utils/helper';

interface HouseHoldMembersProps {
    members: Member[],
    headId?: string,
    householdId: string
}

function HouseHoldMembers({ members = [], headId, householdId }: HouseHoldMembersProps) {
  const { getScoreTitle } = useScore();
  const constants = useContext(ConstantsContext);
  const handleClick = (id:string) => {
    window.location.href = `/members/profile/${id}`;
  };
  return (
    <div className="flex flex-wrap gap-x-[64px] gap-y-[32px]">
      {members?.length > 0 && members.map((item) => (
        <div key={item.id} className="justify-center gap-2 items-center justify-items-center">
          <AvatarWithOverlay
            item={item}
            headId={headId}
            handleClick={handleClick}
            householdId={householdId}
            getScoreTitle={getScoreTitle}
            className="w-[192px] h-[192px]"
          />

          <div className="grid justify-items-center mt-1">
            <p className="font-[500] text-sm p-0 m-0">{capitalizeWords(`${item.firstName} ${item.lastName}`, true)}</p>
            <p className="text-xs font-[400] p-0 m-0">
              {`Person - ${capitalizeWords(item.type)}`}
            </p>
            <p className="text-xs font-[400] p-0 m-0">
              {`${capitalizeWords(getKeyValue(item.gender, 'genderIndentity' as any, constants))} - ${calculateAge(item.dob)} yrs`}
            </p>
            <p
              style={{
                color: getScoreTitle(parseInt(item.score as any, 10))?.color,
              }}
              className="text-xs font-[500] p-0 m-0"
            >
              {`${getScoreTitle(parseInt(item.score as any, 10))?.title}`}
            </p>
          </div>
        </div>
      ))}
    </div>

  );
}

export default HouseHoldMembers;
