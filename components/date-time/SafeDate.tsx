'use client';

import React, { useEffect, useState } from 'react';

type SafeDateProps = {
  date: string | number | Date | null | undefined;
  time?: boolean;
  utc?: boolean;
  fallback?: string;
  showDaysFromCurrent?: boolean;
  relativeDaysColor?: string; // New prop
  dash?: boolean;
};

function SafeDate({
  date,
  time = false,
  utc = false,
  fallback = '-',
  showDaysFromCurrent = false,
  relativeDaysColor, // Accept color class as prop
  dash = false,
}: SafeDateProps) {
  const [formattedDate, setFormattedDate] = useState(fallback);
  const [relative, setRelative] = useState('');

  useEffect(() => {
    if (!date) return;

    let parsedDate: Date;

    if (typeof date === 'number') {
      parsedDate = new Date(date < 2e10 ? date * 1000 : date);
    } else if (typeof date === 'string') {
      parsedDate = new Date(date);
    } else {
      parsedDate = date;
    }

    if (Number.isNaN(parsedDate.getTime())) return;

    const now = new Date();
    const msDiff = parsedDate.getTime() - now.getTime();
    const seconds = Math.floor(msDiff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);
    const days = Math.floor(hours / 24);

    let relativeText = '';

    if (showDaysFromCurrent) {
      if (Math.abs(seconds) < 60) {
        relativeText = 'just now';
      } else if (Math.abs(minutes) < 60 && Math.abs(hours) < 1) {
        relativeText = minutes < 0
          ? `${Math.abs(minutes)} minute${Math.abs(minutes) !== 1 ? 's' : ''} ago`
          : `${Math.abs(minutes)} minute${Math.abs(minutes) !== 1 ? 's' : ''} remaining`;
      } else if (Math.abs(hours) < 24 && Math.abs(days) < 1) {
        relativeText = hours < 0
          ? `${Math.abs(hours)} hour${Math.abs(hours) !== 1 ? 's' : ''} ago`
          : `${Math.abs(hours)} hour${Math.abs(hours) !== 1 ? 's' : ''} remaining`;
      } else {
        relativeText = days < 0
          ? `${Math.abs(days)} day${Math.abs(days) !== 1 ? 's' : ''} ago`
          : `${Math.abs(days)} day${Math.abs(days) !== 1 ? 's' : ''} remaining`;
      }
    }

    const dateOptions: Intl.DateTimeFormatOptions = {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    };

    const formattedDateString = new Intl.DateTimeFormat('en-US', dateOptions).format(
      parsedDate,
    );

    if (!time) {
      setFormattedDate(formattedDateString);
      setRelative(showDaysFromCurrent ? relativeText : '');
      return;
    }

    const timeOptions: Intl.DateTimeFormatOptions = {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
      ...(utc && { timeZone: 'UTC' }),
    };

    const formattedTime = new Intl.DateTimeFormat('en-US', timeOptions).format(
      parsedDate,
    );

    setFormattedDate(`${formattedDateString} ${dash ? '- ' : ''}${formattedTime}`);
    setRelative(showDaysFromCurrent ? relativeText : '');
  }, [date, time, utc, showDaysFromCurrent]);

  return (
    <>
      {formattedDate}
      {showDaysFromCurrent && relative && (
        <span className={`ml-1 ${relativeDaysColor ? `text-[${relativeDaysColor}]` : 'text-gray-500'}`}>
          (
          {relative}
          )
        </span>
      )}
    </>
  );
}

export default SafeDate;
