import { Avatar } from '@mui/material';
import { useState, useCallback, useEffect } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { TimeLineResponse } from 'types/timeline-types';
import { dateFormat } from 'utils/helper';
import SafeDate from 'components/date-time/SafeDate';
import API from 'api/src/lib/api';
import Cookies from 'js-cookie';

interface TimeLineItemsProps {
  initialData: {
    items: TimeLineResponse[];
    metadata: {
      per: number;
      total: number;
      page: number;
    };
  };
  memberId: string;
}

function TimeLineItems({ initialData, memberId }: TimeLineItemsProps) {
  const [items, setItems] = useState<TimeLineResponse[]>(initialData.items);
  const [page, setPage] = useState(initialData.metadata.page || 1);
  const [per] = useState(initialData.metadata.per || 30);
  const [total] = useState(initialData.metadata.total || items.length);

  const fetchMore = useCallback(async () => {
    const nextPage = page + 1;
    const token = Cookies.get('token');

    const res: any = await API.MEMBERS.fetchMemberTimeLines(
      memberId,
      token,
      `per=${per}&page=${nextPage}`,
    );

    setItems((prev) => [...prev, ...(res.items || [])]);
    setPage(nextPage);
  }, [page, per, memberId]);

  const hasMore = items.length < total;

  const itemTitleColor = (status: string) => {
    if (status === 'created') return '#008390';
    if (status === 'deleted') return '#E42B57';
    if (status.includes('added')) return '#008390';
    return '#000';
  };

  const [mounted, setMounted] = useState(false);
  useEffect(() => setMounted(true), []);

  if (!mounted) {
    return (
      <div className="mb-10">
        {items.map((item, index) => (
          <div key={item.id || index} />
        ))}
      </div>
    );
  }

  return (
    <InfiniteScroll
      dataLength={items.length}
      next={fetchMore}
      hasMore={hasMore}
      loader={(
        <p className="text-center text-sm text-gray-500 py-2 flicker">
          Loading more timeline...
        </p>
      )}
      endMessage={
        <p className="text-center text-sm text-gray-400 py-2">No more items</p>
      }
      scrollThreshold="0px"
    >
      <div className="mb-[50px]">
        {items.length > 0 ? (
          items.map((item, index) => {
            const month = dateFormat(item.updatedAt).slice(0, 3);
            const year = dateFormat(item.updatedAt).slice(-4);
            const day = dateFormat(item.updatedAt)
              .slice(4, 6)
              .replace(/[^0-9]/g, '');

            return (
              <div
                key={item.id || index}
                className="flex flex-row gap-3 items-center mt-6 justify-between"
              >
                <div className="flex flex-row gap-3 items-start flex-1 min-w-0 items-center">
                  <div className="bg-[#F7F8F8] relative border-white border rounded-full w-14 h-14 flex flex-col justify-center items-center shrink-0">
                    <p className="text-xs text-center font-semibold m-0 p-0 pt-1">
                      {month}
                      {' '}
                      {day}
                    </p>
                    <p className="text-xs font-normal text-grey m-0 p-0">
                      {year}
                    </p>
                    {index !== items.length - 1 && (
                      <div className="absolute bg-[#DCDFDF] h-4 top-[60px] w-1" />
                    )}
                  </div>
                  <div className="flex flex-col min-w-0">
                    <p
                      className="text-sm font-semibold break-words m-0 p-0"
                      style={{ color: itemTitleColor(item.status) }}
                    >
                      {item.title}
                    </p>
                    <p className="text-sm font-normal break-words m-0 p-0">
                      {item.desc}
                    </p>
                  </div>
                </div>
                <div className="flex flex-row items-center gap-1 shrink-0 pl-2">
                  <Avatar
                    src={item.creator.profile}
                    alt="profile"
                    className="w-8 h-8"
                  />
                  <p className="text-sm whitespace-nowrap">
                    {`${item.creator.firstName || ''} ${
                      item.creator.lastName || ''
                    }`}
                  </p>
                  <p className="text-sm">-</p>
                  <p className="text-sm whitespace-nowrap">
                    <SafeDate date={item.updatedAt} time />
                  </p>
                </div>
              </div>
            );
          })
        ) : (
          <p className="grid justify-center italic text-sm text-dark-text p-14">
            No data available for timeline
          </p>
        )}
      </div>
    </InfiniteScroll>
  );
}

export default TimeLineItems;
