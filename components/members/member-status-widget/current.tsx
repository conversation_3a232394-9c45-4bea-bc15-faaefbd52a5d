import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useContext, useEffect, useState } from 'react';
import { MemberStatusProps } from 'types/member-status-types';
import { Member } from 'types/members-response-type';
import { TimeLineResponse } from 'types/timeline-types';
import {
  getKeyValue, getLatestData,
} from 'utils/helper';
import SafeDate from 'components/date-time/SafeDate';

interface MemberStatusWidgetProps {
  timelines: TimeLineResponse[];
  member: Member;
  status: MemberStatusProps[];
}
function CurrentStatus({ status, timelines, member }: MemberStatusWidgetProps) {
  // eslint-disable-next-line no-unused-vars
  const [currentStatus, setCurrentStatus] = useState<MemberStatusProps | null>(null);
  const constants = useContext(ConstantsContext);
  const [filteredTimeline, setFilteredTimeline] = useState<TimeLineResponse[]>([]);
  const prepareTimeline = async () => {
    const tempTimeline = [];
    // eslint-disable-next-line no-restricted-syntax
    for await (const stat of status) {
      // eslint-disable-next-line max-len
      const timeline = getLatestData(timelines.filter((x) => x.status.toLowerCase().includes(stat.state.toLowerCase())), 'updatedAt');
      if (timeline) {
        tempTimeline.push(timeline);
      }
    }
    setFilteredTimeline(tempTimeline);
  };
  useEffect(() => {
    if (status.length) {
      const getStatus = getLatestData(status, 'updatedAt');
      setCurrentStatus(getStatus);
    }
    prepareTimeline();
    return () => {
      setCurrentStatus(null);
      setFilteredTimeline([]);
      // setTimeline(null);
    };
  }, [status, timelines]);

  return (
    <div className="mt-2 flex flex-col gap-2">
      <div className="flex flex-row gap-1">
        <p className="m-0 p-0 text-sm font-normal">Current Status:</p>
        <p className="m-0 p-0 text-sm font-normal first-letter:uppercase">{getKeyValue(member?.status, 'staffStatus', constants)}</p>
      </div>
      <div className="flex flex-row gap-1">
        <p className="m-0 p-0 text-sm font-normal">Date:</p>
        <p className="m-0 p-0 text-sm font-semibold"><SafeDate date={member?.enrolledOn || member?.unenrolledDate} /></p>
      </div>
      <div className="flex flex-row gap-1">
        <p className="m-0 p-0 text-sm font-normal">Refferal Name:</p>
        <p className="m-0 p-0 text-sm font-normal">{member.referredBy}</p>
      </div>
      { filteredTimeline.map((timeline) => (
        <div className="bg-[#F7F8F8] p-3 rounded-md mt-2">
          <p className="m-0 p-0 text-sm font-semibold">{timeline?.title}</p>
          <p className="m-0 p-0 text-sm font-normal">{timeline?.status}</p>
          <p className="m-0 p-0 text-sm font-normal">{timeline?.desc}</p>
        </div>
      ))}
    </div>
  );
}

export default CurrentStatus;
