import { Close } from '@mui/icons-material';
import {
  Button, Checkbox, Dialog, DialogContent,
} from '@mui/material';
import DateSelector from 'components/date-selector/date-selector';
// import { useFormik } from 'formik';
import { useState } from 'react';

function TimelineFilter() {
  const [open, setOpen] = useState(false);
  const [filter, setFilter] = useState<string|null>(null);
  //   const formik = useFormik({
  //     initialValues: {
  //       startDate: '',
  //       endDate: '',
  //       types: [],
  //     },
  //     onSubmit: async (values) => {
  //       console.log(values);
  //     },
  //   });

  const toogle = () => {
    setOpen((prev) => !prev);
  };

  const handleFilterChange = () => {
    setFilter('filled');
    toogle();
  };

  return (
    <div>
      <Button
        onClick={toogle}
        style={{
          backgroundColor: filter ? 'transperent' : '#ECEEED',
          color: 'black',
          height: '40px',
          width: '102px',
        }}
        variant={filter ? 'outlined' : 'text'}
        className="rounded-md bg-icon-filter bg-no-repeat bg-left border-primary bg-origin-content pr-3"
      >
        Filter
      </Button>
      <Dialog open={open} onClose={toogle} maxWidth="md">
        <DialogContent>
          <div className="borderLine min-w-[500px]">
            <p className="text-center m-0">Filter Timeline</p>
            <Close onClick={toogle} className="cursor-pointer absolute top-5 right-2" />
          </div>
          <div className="pt-2 borderLine">
            <p className=" text-sm font-semibold">Date Range</p>
            <DateSelector range />
            <p className=" text-sm font-semibold">Status Types</p>
            <div className="flex flex-row items-center">
              <Checkbox
                defaultChecked
                color="primary"
                inputProps={{ 'aria-label': 'secondary checkbox' }}
              />
              <span className="">Active</span>
              <Checkbox
                defaultChecked
                color="primary"
                inputProps={{ 'aria-label': 'secondary checkbox' }}
              />
              <span className="">Active</span>
              <Checkbox
                defaultChecked
                color="primary"
                inputProps={{ 'aria-label': 'secondary checkbox' }}
              />
              <span className="">Active</span>
              <Checkbox
                defaultChecked
                color="primary"
                inputProps={{ 'aria-label': 'secondary checkbox' }}
              />
              <span className="">Active</span>
            </div>
          </div>
          <div className="pt-4 flex flex-row justify-between items-center">
            <Button
              style={{
                height: '40px',
              }}
              className="rounded-md bg-[#ECEEED] text-[#008390]"
              variant="text"
              onClick={toogle}
            >
              Restore Defaults
            </Button>
            <Button
              style={{
                height: '40px',
              }}
              className="rounded-md bg-primary text-white"
              variant="text"
              onClick={handleFilterChange}
            >
              Save
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default TimelineFilter;
