import CurrentStatus from 'components/members/member-status-widget/current';
import TimeLineItems from 'components/members/member-status-widget/items';
import TabsView from 'components/tabs-view';
import { useState } from 'react';
import { MemberStatusProps } from 'types/member-status-types';
import { Member } from 'types/members-response-type';
import { PaginatedTimeLineResponse } from 'types/timeline-types';

interface MemberStatusWidgetProps {
    timelines: PaginatedTimeLineResponse;
    status: MemberStatusProps[];
    member: Member;
}
function MemberStatusWidget({ timelines, status, member }: MemberStatusWidgetProps) {
  const [tab, setTab] = useState('current');
  return (
    <div className="borderLine">
      <p className="text-lg font-semibold">Member Status</p>
      <TabsView
        tabs={[{ title: 'Current', key: 'current' }, { title: 'Timeline', key: 'timeline' }]}
        value={tab}
        className="flex flex-row gap-0"
        callback={(v) => {
          setTab(v);
        }}
      />
      {tab === 'timeline' && <TimeLineItems initialData={timelines} memberId={member.id} />}
      {tab === 'current' && <CurrentStatus member={member} timelines={timelines.items} status={status} />}
    </div>
  );
}

export default MemberStatusWidget;
