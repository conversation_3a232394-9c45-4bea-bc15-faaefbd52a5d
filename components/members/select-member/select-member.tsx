import React, { useEffect, useState } from 'react';
import Modal from '@mui/material/Modal';
import Search from 'components/search/search';
import { Close } from '@mui/icons-material';
import API from 'api/src/lib/api';
import { Button } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import CustomTable from 'components/table-components/table';
import { Member } from 'types/members-response-type';
import router from 'next/router';

interface PageInfo {
  per: number;
  total: number;
  page: number;
}

interface SelectMemberProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  disableMultiSelect?: boolean;
  selectedMembers?: Member[];
  setSelectMembers?: React.Dispatch<React.SetStateAction<Member[]>>;
  title: string;
}

function SelectMember({
  open,
  setOpen,
  setSelectMembers,
  selectedMembers,
  disableMultiSelect = false,
  title,
}: SelectMemberProps) {
  const [members, setMembers] = useState<Member[]>([]);
  const [memberData, setMemberData] = useState<Member[]>();
  const [pageInfo, setPageInfo] = useState<PageInfo>({
    per: 10,
    total: 0,
    page: 1,
  });
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState('firstName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [searchTerm, setSearchTerm] = useState('');
  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSearchTerm(value); // optional: if you're managing it in state

    setPage(1); // reset to first page when searching

    const response: any = await API.MEMBERS.fetchMembers(
      undefined, // token (if needed)
      undefined, // orgId (if needed)
      10, // per
      1, // page reset
      sortBy,
      sortDirection,
      value, // search query
    );

    setMembers(response?.items || []);
    setMemberData(response?.items || []);
    setPageInfo({
      per: response?.metadata?.per || 10,
      total: response?.metadata?.total || 0,
      page: response?.metadata?.page || 1,
    });
  };

  useEffect(() => {
    const fetchMembers = async () => {
      if (!open) {
        // Reset state when modal closes
        setPage(1);
        setSortBy('firstName');
        setSortDirection('asc');
        setSearchTerm('');
        setMembers([]);
        setMemberData([]);
        return;
      }

      const response: any = await API.MEMBERS.fetchMembers(
        undefined, // token if needed
        undefined, // orgId if needed
        10,
        page,
        sortBy,
        sortDirection,
        searchTerm,
      );

      setMembers(response?.items || []);
      setMemberData(response?.items || []);
      setPageInfo({
        per: response?.metadata?.per,
        total: response?.metadata?.total,
        page: response?.metadata?.page,
      });
    };

    fetchMembers();
  }, [open, page, sortBy, sortDirection, searchTerm]);

  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div className="bg-white my-[4%] mx-[2%] relative mb-10 rounded-lg h-[86%] flex flex-col overflow-hidden">
        <div className="h-[54px] flex items-center justify-center border-b border-gray-300 relative">
          <p>{title}</p>
          <Close
            className="absolute right-5 cursor-pointer"
            onClick={() => setOpen(false)}
          />
        </div>
        <div className="py-3 flex items-center justify-center border-b border-gray-300">
          <Search
            placeholder="Search unassigned members..."
            onChange={handleSearch}
          />
        </div>
        <div className="flex-grow overflow-auto">
          <CustomTable
            selected={selectedMembers || []}
            variant="main-member-table"
            source={memberData || []}
            metadata={{
              setSelected: setSelectMembers,
              disableMultiSelect,
            }}
            setSelected={setSelectMembers}
            select
            pageInfo={pageInfo}
            sortBy={sortBy}
            sortDirection={sortDirection}
            onPageChange={(newPage) => {
              setPage(newPage);
            }}
            onSortChange={(newSortBy, newDirection) => {
              setSortBy(newSortBy);
              setSortDirection(newDirection);
              setPage(1);
            }}
          />
        </div>
        <div className="h-[60px] shrink-0 bg-white border-t border-gray-300 flex items-center justify-end px-5">
          <Button
            onClick={() => {
              setOpen(false);
            }}
            variant="contained"
            className="bg-[#2D62ED] text-white rounded-md w-[122px]"
          >
            <div className="flex items-center gap-2">
              <CheckIcon />
              Save
            </div>
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default SelectMember;
