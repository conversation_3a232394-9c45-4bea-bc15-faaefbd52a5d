/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useContext, useState } from 'react';
import {
  Box, Button, Modal, TextField,
} from '@mui/material';
import { Check, Close } from '@mui/icons-material';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import SelectBox from 'components/select-box/select-box';
import { Member } from 'types/members-response-type';
import { capitalizeWords } from 'utils/helper';
import API from 'api/src/lib/api';
import AttachmentPicker from 'components/attachment-picker/attachment-picker';
import { useModal } from 'contexts/modal-context/modal-context';
import SelectBoxMulti from 'components/select-box-multi/select-box';

interface SelectSocialPlanProps {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    navigatorId: string;
    member: Member;
    refresh: ()=> Promise<void>;
}

// eslint-disable-next-line react/function-component-definition
const NewNoteModal = ({
  open, setOpen, navigatorId, member, refresh,

}: SelectSocialPlanProps) => {
  const constant = useContext(ConstantsContext);
  const [attachment, setAttachment] = useState<string[]>([]);
  const [desc, setDesc] = useState('');
  const [type, setType] = useState('');
  const [tagSelect, setTagSelect] = useState<string[]>([]);
  const [title, setTitle] = useState('');
  const { showModal } = useModal();
  const handleClose = () => {
    setOpen(false);
    setAttachment([]);
    setTagSelect([]);
    setType('');
    setTitle('');
  };
  const handleAttachments = () => attachment.map((att) => ({
    type: 'note',
    name: `Note-${title}`,
    category: att.startsWith('data:application/pdf') ? 'pdf' : 'image',
    base64: att,
  }));

  const handleAdd = async () => {
    if (desc === '' || tagSelect.length === 0 || title === '') {
      setTimeout(() => {
        showModal('Warning', 'Please fill all fields');
      }, 200);
      return;
    }
    if (desc === '') return;
    if (tagSelect.length === 0) return;
    const tags = tagSelect.map((tagKey) => {
      const tag = constant?.noteTags?.find((t) => t.key === tagKey);
      return {
        name: tag?.title,
        key: tag?.key,
        color: tag?.color,
      };
    });

    const model = {
      creator: navigatorId,
      memberID: member.id,
      title,
      type: type || 'basic',
      msg: desc,
      tags, // This now includes multiple tags
      attachments: handleAttachments(),
    };
    await API.NOTES.createNote(model);
    handleClose();
    await refresh();
  };

  return (
    <Modal open={open} onClose={handleClose}>
      <Box sx={{
        position: 'absolute',
        maxHeight: 'auto',
        minWidth: '40%',
        outline: 'none',
        borderRadius: '8px',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 850,
        padding: 2,
        bgcolor: 'background.paper',
        boxShadow: 24,
      }}
      >
        <div className="grid grid-flow-col m-0 p-0 relative auto-cols-auto justify-center justify-items-center items-center borderLine">
          <div className="grid grid-flow-row justify-center items-center">
            <p className="m-0 p-0 text-lg text-center">New Note</p>
            <p className="m-0 p-0 text-center font-[500] text-sm">{capitalizeWords(`${member?.firstName} ${member.lastName} `, true)}</p>
          </div>
          <Close onClick={handleClose} className="cursor-pointer absolute top-0 right-0 float-right w-8 h-8" />
        </div>
        <div
          className="grid grid-flow-row h-full"
        >
          <div className="grid grid-cols-2 pt-3 gap-3 items-center w-full">
            <SelectBoxMulti
              keyVal="tag"
              items={constant?.noteTags}
              defaultValue={tagSelect.join(',')}
              label="Note Tag"
              tags={constant?.noteTags}
              onChange={(e) => {
                setTagSelect(e as unknown as string[]);
              }}
            />
            <SelectBox
              keyVal="category"
              items={constant?.notesSubcategory}
              defaultValue={type}
              label="Category"
              onChange={(e) => { setType(e.target.value); }}
            />
            <TextField
              variant="filled"
              value={title}
              label="Title"
              className="col-span-2"
              onChange={(e) => { setTitle(e.target.value); }}
            />

          </div>
          <div className="borderLine ">
            <TextField
              onChange={(e) => {
                setDesc(e.target.value);
              }}
              multiline
              variant="filled"
              minRows={10}
              inputProps={{
                style: { height: 300 },
              }}
              className="w-full overflow-scroll h-[200px] mt-2 font-[300] placeholder:text-[12px]"
              label="Enter a note..."
            />
          </div>
          <div className="pt-2 w-1/5">
            <AttachmentPicker
              multiple
              attachments={attachment}
              attachmentsToRemove={[]}
              setPicture={setAttachment}
              setAttachmentsToRemove={() => {
                // do nothing
              }}
              edit
            />
          </div>
          <Button
            onClick={() => {
              showModal('Warning', 'Are you sure you want to save this note?', handleAdd);
            }}
            className="bg-primary border-none w-1/6  justify-self-end rounded-md hover:bg-primary hover:opacity-80 text-white mt-5"
          >
            <Check style={{ fontSize: 18, marginRight: 4 }} />
            Save
          </Button>
        </div>

      </Box>
    </Modal>
  );
};

export default NewNoteModal;
