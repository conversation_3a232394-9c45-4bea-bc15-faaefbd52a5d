/* eslint-disable no-nested-ternary */
/* eslint-disable max-len */
import React, { useState, useEffect } from 'react';
import {
  Button, Checkbox, Drawer, Radio,
  TextField,
} from '@mui/material';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';
import { capitalizeWords } from 'utils/helper';
import { Questions } from 'types/members-response-type';
import useAnswerLogic from 'hooks/use-answer';

interface NewEncounterDrawerProps {
    subTitle: string;
    open: boolean;
    questions: Questions[];
    surveyId: string;
    memberId: string;
    takerId: string;
    orgId: string;
    sKey: string;
    setQuestions: React.Dispatch<React.SetStateAction<Questions[]>>;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setAnswers: React.Dispatch<React.SetStateAction<any[]>>;
    answers: any[];
    // eslint-disable-next-line no-unused-vars, react/no-unused-prop-types
    callback?: (id: string) => string;
}

function NewEncounter({
  open, setOpen, subTitle = 'none',
  questions, setQuestions, answers, setAnswers,
  memberId, surveyId, takerId, orgId, sKey,
}: NewEncounterDrawerProps) {
  const isMulti = questions.some((question) => question.type === 'multi');
  const [currentStage, setCurrentStage] = useState(1);
  const [options, setOptions] = useState<any[]>([]);
  const { answerLogic, isAnswer } = useAnswerLogic();
  const handleClose = () => {
    setOpen(false);
    setQuestions([]);
    setCurrentStage(1);
  };
  const handleCheckBox = (
    subQuestionId: string,
    value: string,
    answer: string,
    mainQuestionId: string,
    multiline = false,
  ) => {
    if (multiline) {
      const updatedOptions = [...options.filter((option) => option.id !== subQuestionId), {
        id: subQuestionId, value, answer, mainQuestionId, multiline: true,
      }];
      setOptions(updatedOptions);
    } else {
      const updatedOptions = options.some((option) => option.id === subQuestionId)
        ? options.filter((option) => option.id !== subQuestionId)
        : [...options, {
          id: subQuestionId, value, answer, mainQuestionId, multiline: false,
        }];

      setOptions(updatedOptions);
    }
  };

  const handleSelect = (id: string, message: string, value: string, option = false) => {
    const questionID = questions[currentStage - 1].id.toLowerCase();
    const newAnswers = answerLogic({
      question: [...answers],
      questionId: questionID,
      answerText: message || '',
      answerId: id || '',
      surveyID: surveyId.toLowerCase(),
      takerID: takerId.toLowerCase(),
      giverID: memberId.toLowerCase(),
      parentID: questionID,
      orgID: orgId.toLowerCase(),
      key: sKey,
      options: option,
      value,
      multi: isMulti,
      id,
    });

    setAnswers(newAnswers);

    if (isMulti) {
      // Check if the selected option is still active after the update
      const isOptionSelected = newAnswers.some((answer) => answer.parentID === questionID && answer.answers.some((a) => a.answerID === id));

      if (!isOptionSelected) {
        // If the option is deselected, remove its subquestions from options
        const updatedOptions = options.filter((opt) => opt.mainQuestionId !== id);
        setOptions(updatedOptions);
      } else {
        // If the option is selected, update the options state to include only subquestions
        // related to the selected main question options
        const selectedMainQuestionIds = newAnswers
          .filter((answer) => answer.parentID === questionID)
          .flatMap((answer) => answer.answers.map((a) => a.answerID));

        const filteredOptions = options.filter((opt) => selectedMainQuestionIds.includes(opt.mainQuestionId));

        setOptions(filteredOptions);
      }
    } else {
      setOptions([]);
    }
  };
  const handleNext = async () => {
    if (options.length > 0 && isMulti) {
      const updatedAnswers = [...answers];
      // eslint-disable-next-line no-restricted-syntax
      for (const option of options) {
        const result = answerLogic({
          question: updatedAnswers,
          questionId: questions[currentStage - 1].id.toLowerCase(),
          answerText: option.answer || '',
          answerId: option.id || '',
          surveyID: surveyId.toLowerCase(),
          takerID: takerId.toLowerCase(),
          giverID: memberId.toLowerCase(),
          parentID: option.mainQuestionId.toLowerCase(),
          orgID: orgId.toLowerCase(),
          key: sKey,
          options: true,
          value: option.value,
          multi: isMulti,
        });
        updatedAnswers.splice(0, updatedAnswers.length, ...result);
      }
      setAnswers(updatedAnswers);
    }

    if (currentStage === questions.length) {
      handleClose();
    } else {
      setCurrentStage(currentStage + 1);
    }
  };

  const handleBack = () => {
    if (currentStage === 1) {
      handleClose();
    } else {
      setCurrentStage(currentStage - 1);
      setAnswers(answers.filter((x: any) => x.parentID.toLowerCase() !== questions[currentStage - 1].id));
    }
  };

  useEffect(() => {
    /// clean all the answers from all the stages
    const clean = [...answers];
    questions.forEach((question) => {
      clean.splice(0, clean.length, ...clean.filter((x: any) => x.parentID.toLowerCase() !== question.id.toLowerCase()));
    });
    setAnswers(clean);
  }, []);

  return (
    <Drawer
      sx={{
        '& .MuiDrawer-paper': {
          height: '100%',
          width: '100%',
        },
      }}
      anchor="bottom"
      open={open}
      onClose={handleClose}
    >
      <div className="w-screen bg-white pb-20">
        <div
          style={{
            position: 'fixed',
            width: '100%',
            backgroundColor: '#fff',
            zIndex: 2,
            borderBottom: '1px solid #E0E0E0',
          }}
          className="grid grid-flow-col gap-8 p-3 pl-5 auto-cols-max items-center relative"
        >
          <CancelOutlinedIcon
            onClick={handleClose}
            style={{ width: 35, height: 35 }}
            className="text-[#646F79] text-3xl cursor-pointer"
          />
          <p className="font-[500] p-0 m-0 text-[26px]">
            {capitalizeWords(`${questions.length > 0 ? questions[0].type : 'No Name'} - `, true)}
            <span className="font-[400]">{subTitle}</span>
          </p>
        </div>
      </div>
      <div className="pt-6 pl-20 pr-20 grid grid-rows gap-7">
        <div className="grid grid-flow-col gap-2">
          <p className="font-[500]">
            {`Question ${currentStage} of ${questions.length}`}
          </p>
        </div>
        <h2 className="font-[400]">{questions[(currentStage - 1)]?.message}</h2>
        <div className="grid grid-flow-row gap-1 items-start auto-cols-auto ">

          {questions[(currentStage - 1)]?.type === 'multiline' ? (
            <div>
              <TextField
                id="outlined-multiline-static"
                multiline
                key={questions[currentStage - 1].id}
                rows={4}
                defaultValue={answers.find((x) => x.parentID === questions[currentStage - 1].id.toLowerCase())?.answers[0]?.answer || ''}
                onChange={(e) => handleSelect(questions[currentStage - 1].id, e.target.value, questions[currentStage - 1].value as unknown as string)}
                className="w-full"
                placeholder="Type your answer here"
                variant="outlined"
              />

            </div>
          ) : questions[(currentStage - 1)]?.questions.map(
            (question) => (
              <div className="borderLine" key={question.id}>
                {question.type === 'multi' ? (
                  // eslint-disable-next-line max-len
                  // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
                  <div
                    // eslint-disable-next-line max-len
                    onClick={() => handleSelect(question.id, question.message, question.value.toString())}
                    className="grid grid-flow-col cursor-pointer gap-2 items-center auto-cols-max pt-3"
                  >
                    <Checkbox
                      color="success"
                      // eslint-disable-next-line max-len
                      checked={isAnswer(answers, question.message, question.id)}
                      style={{ width: 15 }}
                      size="small"
                    />
                    <p className="p-0 m-0">{question.message}</p>
                  </div>
                ) : (
                  // eslint-disable-next-line max-len
                  // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
                  <div
                    // eslint-disable-next-line max-len
                    onClick={() => handleSelect(question.id, question.message, question.value.toString())}
                    className="grid grid-flow-col cursor-pointer gap-2 items-center auto-cols-max pt-3"
                  >
                    <Radio
                      color="success"
                      checked={isAnswer(answers, question.message, question.id)}
                      name="answers"
                      style={{ width: 15 }}
                      size="small"
                    />
                    <p className="p-0 m-0">{question.message}</p>
                  </div>
                )}
                {isAnswer(answers, question.message, question.id) && question.questions.map(
                  (subQuestion) => (
                    <>
                      <div key={subQuestion.id} className="pl-12 innerSpace grid grid-flow-col items-center gap-1 auto-cols-max">
                        <Checkbox
                          sx={{ '&.Mui-checked': { color: '#00A343' } }}
                        // eslint-disable-next-line max-len
                          color="success"
                          onChange={() => handleCheckBox(subQuestion.id, subQuestion.value.toString(), subQuestion.message, question.id)}
                          checked={options.findIndex((x) => x.id === subQuestion.id) !== -1}
                        />
                        <p className="p-0 m-0">{subQuestion.message}</p>

                      </div>
                      {options.findIndex((x) => x.id === subQuestion.id) !== -1 && subQuestion.type === 'multiline' && (
                      <div className="innerSpace">
                        <TextField
                          id="outlined-multiline-static"
                          multiline
                          key={subQuestion.id}
                          rows={4}
                          defaultValue={answers.find((x) => x.parentID === subQuestion.id.toLowerCase())?.answers[0]?.answer || ''}
                          onChange={(e) => handleCheckBox(subQuestion.id, subQuestion.value.toString(), e.target.value, question.id, true)}
                          className="w-full"
                          placeholder="Type your answer here"
                          variant="outlined"
                        />
                      </div>
                      )}
                    </>
                  ),
                )}
              </div>
            ),
          )}
        </div>
        <div className="grid grid-flow-col auto-cols-min gap-4 w-full pb-10 ">
          <Button
            className="bg-gray-base rounded-md text-black w-[150px]"
            onClick={handleBack}
            disabled={currentStage === 1}
          >
            Back
          </Button>
          <Button
            className="bg-primary hover:opacity-95 hover:bg-primary text-white w-[150px] rounded-md"
            onClick={handleNext}
          >
            Next
          </Button>
        </div>
      </div>
    </Drawer>
  );
}

export default NewEncounter;
