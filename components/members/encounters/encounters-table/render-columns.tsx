/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React from 'react';
import { CustomizeEncounterFieldsProps } from 'components/members/encounters/encounters-table/columns-keys';
import { StyledAvatar } from 'components/members/encounters/encounters-table/styled-components';
import SafeDate from 'components/date-time/SafeDate';

const pBold = 'text-[14px] font-[500] m-1 p-0 text-[#001018] first-letter:uppercase';
const pSoft = 'text-[14px] font-[400] m-0 p-0 text-[#001018] first-letter:uppercase';

// eslint-disable-next-line react/function-component-definition
const CustomizeEncounterFields: React.FC<CustomizeEncounterFieldsProps> = ({
  field,
  value,
  record,
  children,
  router,
  getScoreTitle,
}: CustomizeEncounterFieldsProps) => {
  switch (field) {
    case 'name':
      return (
        <p
          onClick={() => {
            // eslint-disable-next-line max-len
            const model = record?.sections.map((x) => ({
              id: x.id,
              sKey: record.key,
              type: x.type,
              member: record?.memberID,
              survey: x.survey.id,
              title: record.name,
            }))[0];
            if (record?.status !== 'active') {
              router?.push({
                pathname: `${record?.memberID}/check/check`,
                query: {
                  sections: JSON.stringify(model || {}),
                },
                // eslint-disable-next-line no-void
              });
            } else {
              router?.push({
                pathname: `${record?.memberID}/detail`,
                query: {
                  sections: JSON.stringify(model || {}),
                },
                // eslint-disable-next-line no-void
              });
            }
          }}
          key={field}
          className={`${pBold} cursor-pointer`}
        >
          {value}
        </p>
      );
    case 'navigator':
      return (
        // eslint-disable-next-line max-len
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
        <div
          key={field}
          className="grid grid-flow-col cursor-pointer items-center justify-start"
        >
          {children}
          <StyledAvatar
            className="w-[28px] h-[28px]"
            alt={record?.taker.firstName}
            src={record?.taker?.profile || ''}
          />
          <p className={`${pSoft} pr-1`}>{`${record?.taker.firstName} `}</p>
          <p className={pSoft}>{`${record?.taker.lastName} `}</p>
        </div>
      );
    case 'date':
      return (
        <p key={field} className={pSoft}>
          <SafeDate date={record?.startedAt as string} />
        </p>
      );
    case 'score':
      return (
        <p
          key={field}
          className={`${pBold} text-[${getScoreTitle(value as number)?.color}]`}
        >
          {getScoreTitle(value as number)?.title}
        </p>
      );
    case 'status':
      return (
        <p key={field} className={pSoft}>
          {value}
        </p>
      );
    case 'actions':
      return <p />;
    default:
      return (
        <p key={field} className={pBold}>
          {value}
        </p>
      );
  }
};
export default CustomizeEncounterFields;
