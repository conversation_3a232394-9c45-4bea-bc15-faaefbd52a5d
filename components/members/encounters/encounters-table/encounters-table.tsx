/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable import/named */
import React, { useState, useEffect } from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import ArrowUpwardIcon from '@mui/icons-material/KeyboardArrowUp';
import ArrowDownwardIcon from '@mui/icons-material/KeyboardArrowDown';
import { Checkbox, Pagination } from '@mui/material';
import useScore from 'hooks/use-score';
import ActionContainer from 'components/action-container/action-container';
import { useModal } from 'contexts/modal-context/modal-context';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useRouter } from 'next/router';
import CustomizeEncounterFields from 'components/members/encounters/encounters-table/render-columns';
import API from 'api/src/lib/api';
import { Encounter, TableHeader } from './type';
import {
  StyledTableCell,
  StyledTableRow,
  borderlessTable,
  borderlessTableCell,
  borderlessTableContainer,
} from './styled-components';

interface encounterTableProps {
  EncounterData: Encounter[];
  tableHeaders: TableHeader[];
  select?: boolean;
  selectedencounters?: Encounter[];
  insideModal?: boolean;
  // setEditEncounter?: React.Dispatch<React.SetStateAction<Encounter>>;
  // setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectedencounters?: React.Dispatch<React.SetStateAction<Encounter[]>>;
}

function EncounterTable({
  EncounterData,
  tableHeaders,
  select = false,
  selectedencounters,
  insideModal,
  setSelectedencounters,
}: encounterTableProps) {
  // Click handler for sorting
  const router = useRouter();
  const [sortedencounters, setSortedencounters] = useState<Encounter[]>(EncounterData);
  const [sortingDirection, setSortingDirection] = useState<
    'asc' | 'desc' | null
  >(null);
  const [sortedColumn, setSortedColumn] = useState<keyof Encounter | null>(
    null,
  );
  const [page, setPage] = useState(1);
  const { showModal } = useModal();
  const { refresh } = useRefreshSSR();
  // eslint-disable-next-line no-unused-vars
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const { getScoreTitle } = useScore();

  const handleOnDelete = async (item: Encounter) => {
    showModal(
      'Warning',
      'Are you sure you want to delete this assessment?',
      async () => {
        await API.MEMBERS.deleteMemberEncounter(item.id);
        await refresh();
      },
    );
  };

  // Click handler for sorting
  const handleHeadClick = (property: keyof Encounter) => {
    const newSortingDirection = sortingDirection === 'asc' ? 'desc' : 'asc';
    const newSortedencounters = [...sortedencounters].sort((a, b) => {
      if ((a[property] as any) < (b[property] as any)) { return newSortingDirection === 'asc' ? -1 : 1; }
      if ((a[property] as any) > (b[property] as any)) { return newSortingDirection === 'asc' ? 1 : -1; }
      return 0;
    });

    setSortedencounters(newSortedencounters);
    setSortingDirection(newSortingDirection);
    setSortedColumn(property);
  };
  const handleSelectedencounters = (item: Encounter) => {
    if (setSelectedencounters) {
      setSelectedencounters((prev) => {
        if (prev.includes(item)) {
          return prev.filter((items) => items !== item);
        }
        return [...prev, item];
      });
    }
  };

  const handleIsModalSelected = (item: Encounter) => {
    if (setSelectedencounters) {
      setSelectedencounters((prev) => prev.filter((items) => items.id !== item.id));
    }
    return false;
  };

  useEffect(() => {
    setSortedencounters(EncounterData);
    if (page > 1) {
      setPage(1);
    }
  }, [JSON.stringify(EncounterData)]);

  return (
    <TableContainer component={Paper} sx={borderlessTableContainer}>
      <Table aria-label="encounter table" sx={borderlessTable}>
        <TableHead>
          <TableRow>
            {tableHeaders.map(({ key, value }) => (
              <StyledTableCell
                key={key as string}
                onClick={() => handleHeadClick(key)}
              >
                <div className="grid grid-flow-col items-center auto-cols-min">
                  {value}
                  {sortedColumn === key
                    && (sortingDirection === 'asc' ? (
                      <ArrowUpwardIcon />
                    ) : (
                      <ArrowDownwardIcon />
                    ))}
                </div>
              </StyledTableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {sortedencounters
            .slice((page - 1) * rowsPerPage, page * rowsPerPage)
            .map((encounter, index) => (
              <StyledTableRow key={encounter.id} index={index}>
                {tableHeaders.map(({ key }) => {
                  const value = encounter[key];
                  const isSelected = selectedencounters?.includes(encounter);
                  if ((key as any) === 'actions' && !select) {
                    return (
                      <TableCell
                        key={key}
                        style={{ padding: '8px 16px', ...borderlessTableCell }}
                      >
                        {insideModal ? (
                          <ActionContainer
                            deleteLabel="Remove encounter"
                            editLabel="Edit encounter"
                            onDelete={() => {
                              handleIsModalSelected(encounter);
                            }}
                          />
                        ) : encounter.status !== 'active' ? (
                          encounter.status === 'complete' ? (
                            <ActionContainer
                              deleteLabel="Delete Assessment"
                              onDelete={undefined}
                              editLabel="View Details"
                              onEdit={() => {
                                router.push({
                                  pathname: `${encounter.memberID}/check/check`,
                                  query: {
                                    sections: JSON.stringify(
                                      encounter.sections.map((x) => ({
                                        id: x.id,
                                        sKey: encounter.key,
                                        type: x.type,
                                        member: encounter.memberID,
                                        survey: x.survey.id,
                                        title: encounter.name,
                                      }))[0],
                                    ),
                                  },
                                });
                              }}
                            />
                          ) : (
                            <ActionContainer
                              deleteLabel="Delete Assessment"
                              onDelete={undefined}
                              editLabel="View Details"
                              onEdit={() => {
                                router.push({
                                  pathname: `${encounter.memberID}/detail`,
                                  query: {
                                    sections: JSON.stringify(
                                      encounter.sections.map((x) => ({
                                        id: x.id,
                                        sKey: encounter.key,
                                        type: x.type,
                                        member: encounter.memberID,
                                        survey: x.survey.id,
                                        title: encounter.name,
                                      }))[0],
                                    ),
                                  },
                                });
                              }}
                            />
                          )
                        ) : (
                          <ActionContainer
                            deleteLabel="Delete Assessment"
                            editLabel="View Details"
                            onDelete={() => handleOnDelete(encounter)}
                            onEdit={() => {
                              router.push({
                                pathname: `${encounter.memberID}/check/check`,
                                query: {
                                  sections: JSON.stringify(
                                    encounter.sections.map((x) => ({
                                      id: x.id,
                                      sKey: encounter.key,
                                      type: x.type,
                                      member: encounter.memberID,
                                      survey: x.survey.id,
                                      title: encounter.name,
                                    }))[0],
                                  ),
                                },
                              });
                            }}
                          />
                        )}
                      </TableCell>
                    );
                  }
                  return (
                    <TableCell
                      key={key}
                      style={{ padding: '8px 16px', ...borderlessTableCell }}
                    >
                      <CustomizeEncounterFields
                        key={key}
                        getScoreTitle={getScoreTitle}
                        router={router}
                        field={key}
                        record={encounter}
                        value={value as any}
                        select={select}
                      >
                        {select && (
                          <Checkbox
                            checked={isSelected}
                            onChange={() => handleSelectedencounters(encounter)}
                          />
                        )}
                      </CustomizeEncounterFields>
                    </TableCell>
                  );
                })}
              </StyledTableRow>
            ))}
        </TableBody>
      </Table>
      <Pagination
        hideNextButton={sortedencounters.length <= rowsPerPage}
        hidePrevButton={sortedencounters.length <= rowsPerPage}
        count={Math?.ceil(sortedencounters.length / rowsPerPage)}
        page={page}
        onChange={(event, value) => setPage(value)}
        className="pt-5 align-middle"
      />
    </TableContainer>
  );
}

export default EncounterTable;
