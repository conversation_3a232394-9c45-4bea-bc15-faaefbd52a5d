/* eslint-disable no-use-before-define */
  interface Team {
    name: string;
    id: string;
    org: Survey;
    updatedAt: string;
    createdAt: string;
  }

  interface Section {
    title: string;
    createdAt: string;
    score?: string;
    survey: Survey;
    questions: Question4[];
    id: string;
    updatedAt: string;
    complete: boolean;
    kind?: any;
    type: string;
  }

  interface Question4 {
    type?: string;
    value: number;
    score?: (null | number)[] | number;
    updatedAt: string;
    createdAt: string;
    section: Survey;
    message: string;
    question: Question;
    questions: Question3[];
    id: string;
    title: string;
    level: number;
  }

  interface Question3 {
    score: number;
    updatedAt: string;
    question: Survey;
    questions: (Question2[] | Questions2 | Questions3)[][];
    id: string;
    title: string;
    section: Question;
    level: number;
    value: number;
    type?: null | null | null | string | string | string | string;
    message: string;
    createdAt: string;
  }

  interface Questions3 {
    id: string;
    updatedAt: string;
    createdAt: string;
    value: number;
    section: Question;
    question: Survey;
    title: string;
    level: number;
    type?: any;
    score: number;
    message: string;
  }

  interface Questions2 {
    type?: string | string | string;
    createdAt: string;
    updatedAt: string;
    message: string;
    question: Survey;
    section: Question;
    value: number;
    score: number;
    level: number;
    id: string;
    title: string;
  }

  interface Question2 {
    type: string;
    createdAt: string;
    updatedAt: string;
    message: string;
    question: Survey;
    section: Question;
    value: number;
    score: number;
    level: number;
    id: string;
    title: string;
  }

  interface Question {
    id?: any;
  }

  interface Survey {
    id: string;
  }
  interface Taker {
    updatedAt: string;
    email: string;
    lastName: string;
    middleName?: any;
    createdAt: string;
    firstName: string;
    org: Survey;
    teams: Team[];
    auth: string;
    color: string;
    id: string;
    roles: string[];
    profile: string;
  }

export interface Encounter {
    memberID: string;
    name: string;
    createdAt: string;
    endedAt?: string;
    lastSurvey?: number;
    key: string;
    orgID: string;
    firstSurvey?: number;
    sections: Section[];
    score: string;
    updatedAt: string;
    attachments: any[];
    status: string;
    taker: Taker;
    startedAt: string;
    id: string;
  }
export interface TableHeader {
    key: keyof Encounter;
    value: string;
}
