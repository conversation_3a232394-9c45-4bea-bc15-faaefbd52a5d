/* eslint-disable react/function-component-definition */
import { ScoreResponse } from 'hooks/use-score';
import type React from 'react';
import { NextRouter } from 'next/router';
import { Encounter, TableHeader } from './type';

// eslint-disable-next-line import/prefer-default-export
export const encounterTableColumns = [
  {
    key: 'name',
    value: 'Name',
  },
  {
    key: 'navigator',
    value: 'Navigator',
  },
  {
    key: 'date',
    value: 'Date',
  },
  {
    key: 'status',
    value: 'Status',
  },
  {
    key: 'score',
    value: 'Score',
  },
  {
    key: 'actions',
    value: 'Actions',
  },
] as TableHeader[];

export interface CustomizeEncounterFieldsProps {
    field: string;
    children?: React.ReactNode;
    record?: Encounter;
    value: string | number;
    select?: boolean;
    // eslint-disable-next-line no-unused-vars
    getScoreTitle: (score: number) => ScoreResponse | null;
    router?: NextRouter;
  }
