/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useEffect, useState } from 'react';
import { Box, Modal } from '@mui/material';
import { Close, Description } from '@mui/icons-material';
import API from 'api/src/lib/api';
import router from 'next/router';
import TabsView from 'components/tabs-view';

interface CreateEncounterProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  memberId: string;
  navigatorId: string;
  orgId: string;
  refresh: () => Promise<void>;
  surveyTypes: {
    title: string;
    key: string;
  }[];
  type: string;
}

// eslint-disable-next-line react/function-component-definition
const CreateEncounter = ({
  open,
  setOpen,
  surveyTypes = [],
  memberId,
  navigatorId,
  orgId,
  refresh,
  type,
}: CreateEncounterProps) => {
  const handleClose = () => {
    setOpen(false);
  };

  const [encounterType, setEncounterType] = useState(type);
  const [localSurveyTypes, setLocalSurveyTypes] = useState(surveyTypes);

  useEffect(() => {
    setEncounterType(type || 'assessment');
  }, [type, open]);

  const createEncounter = async (surveyType: string) => {
    if (encounterType === 'consent_web') {
      router.push(`/members/encounters/${memberId}/consent-form/${surveyType}`);
    } else {
      // eslint-disable-next-line no-console
      const model = {
        key: surveyType,
        memberID: memberId,
        navigatorID: navigatorId,
        orgID: orgId,
      };
      await API.MEMBERS.createSurvey(model);
      setOpen(false);
      refresh();
    }
  };

  useEffect(() => {
    const fetchSurveyTypes = async () => {
      try {
        const response: any = await API.MEMBERS.fetchSurveyTypes(
          undefined,
          undefined,
          encounterType,
        );
        setLocalSurveyTypes(response || []);
      } catch (error) {
        // eslint-disable-next-line no-console
        console.error('Failed to fetch survey types:', error);
        setLocalSurveyTypes([]); // fallback
      }
    };

    fetchSurveyTypes();
  }, [encounterType]);

  return (
    <Modal open={open} onClose={handleClose}>
      <Box
        sx={{
          position: 'absolute',
          maxHeight: '70%',
          minWidth: '40%',
          overflow: 'hidden',
          outline: 'none',
          borderRadius: '8px',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 450,
          padding: 2,
          bgcolor: 'background.paper',
          boxShadow: 24,
        }}
      >
        <div className="grid grid-flow-col m-0 p-0 relative auto-cols-auto justify-center justify-items-center items-center">
          <p className="m-0 p-0 text-lg">Select Encounter Type</p>
          <Close
            onClick={handleClose}
            className="cursor-pointer absolute top-0 right-0 float-right w-8 h-8"
          />
        </div>
        <div className="flex justify-center py-2" style={{ borderBottom: '1px solid #E5E5E5' }}>
          <TabsView
            tabs={[
              {
                key: 'assessment',
                title: 'Assessments',
              },
              {
                key: 'consent_web',
                title: 'Consents',
              },
            ]}
            className="grid grid-flow-col auto-cols-max gap-0"
            value={encounterType}
            callback={setEncounterType}
          />
        </div>
        <div className="h-[450px] overflow-auto relative p-2">
          {localSurveyTypes.map((survey) => (
            <div
              key={survey.key}
              onClick={() => createEncounter(survey.key)}
              style={{
                height: '52px',
                width: '100%',
                border: '1px solid #E5E5E5',
              }}
              className="grid grid-flow-col rounded-lg p-2 gap-2 justify-start auto-cols-auto items-center border cursor-pointer hover:bg-gray-100 mb-2"
            >
              <Description className="font-[500] text-2xl" />
              <p className="whitespace-pre-wrap  text-sm font-[500] m-0 p-0">
                {survey.title}
              </p>
            </div>
          ))}
        </div>
      </Box>
    </Modal>
  );
};

export default CreateEncounter;
