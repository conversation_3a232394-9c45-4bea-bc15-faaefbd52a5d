/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import {
  Button,
  Checkbox,
  FormControlLabel,
  Pagination,
  Radio,
  RadioGroup,
  TextField,
} from '@mui/material';
import React, { useContext, useEffect, useState } from 'react';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import Search from 'components/search/search';
import { ArrowBack, ArrowForward, Check } from '@mui/icons-material';
import SelectBox from 'components/select-box/select-box';
import DateSelector from 'components/date-selector/date-selector';
import dayjs from 'dayjs';
import Image from 'next/image';
import { capitalizeWords } from 'utils/helper';
import { MEMBERS } from 'api/src/routes/members';
import { useRouter } from 'next/router';

interface InsuranceAddUpdateProps {
  member: any;
  add: boolean;
  insurance: any;
}
function InsuranceAddUpdate({
  member,
  add,
  insurance,
}: InsuranceAddUpdateProps) {
  const router = useRouter();
  const constants = useContext(ConstantsContext);
  const [insurancesList, setInsurancesList] = useState(
    constants?.carriers || [],
  );
  const [insurancesFromApi, setInsurancesFromApi] = useState(false);
  const [insurancePage, setInsurancePage] = useState(1);
  const [insurancePerPage] = useState(8);
  const [insuranceTotal, setInsuranceTotal] = useState(0);
  const [insuranceSearch, setInsuranceSearch] = useState('');
  const [step, setStep] = useState(1);
  const [prevStep, setPrevStep] = useState(1);
  // Detect direction of step change
  const isIncreasing = step > prevStep;
  const isDecreasing = step < prevStep;
  const [insuranceCarrier, setInsuranceCarrier] = useState<string>(
    add ? '' : insurance?.carrierId?.toLowerCase(),
  );
  const [insuranceCarrierName, setInsuranceCarrierName] = useState<string>(
    add ? '' : capitalizeWords(insurance?.plans?.[0]?.issuer, true),
  );
  const [planType, setPlanType] = useState<any>(add ? '' : insurance?.planType);
  const [planTypeName, setPlanTypeName] = useState<string>(
    add ? '' : insurance?.planTypeName,
  );
  const [partA, setPartA] = useState(add ? false : insurance?.partA);
  const [partB, setPartB] = useState(add ? false : insurance?.partB);
  const [partC, setPartC] = useState(add ? false : insurance?.partC);
  const [partD, setPartD] = useState(add ? false : insurance?.partD);
  const [enrollmentStatusA, setEnrollmentStatusA] = useState<string>(
    add ? '' : insurance?.enrollmentStatusA,
  );
  const [enrollmentStatusB, setEnrollmentStatusB] = useState<string>(
    add ? '' : insurance?.enrollmentStatusB,
  );
  const [enrollmentStatusC, setEnrollmentStatusC] = useState<string>(
    add ? '' : insurance?.enrollmentStatusC,
  );
  const [enrollmentStatusD, setEnrollmentStatusD] = useState<string>(
    add ? '' : insurance?.enrollmentStatusD,
  );
  const [enrollmentDateA, setEnrollmentDateA] = useState<string>(
    add ? '' : insurance?.enrollmentDateA,
  );
  const [enrollmentDateB, setEnrollmentDateB] = useState<string>(
    add ? '' : insurance?.enrollmentDateB,
  );
  const [enrollmentDateC, setEnrollmentDateC] = useState<string>(
    add ? '' : insurance?.enrollmentDateC,
  );
  const [enrollmentDateD, setEnrollmentDateD] = useState<string>(
    add ? '' : insurance?.enrollmentDateD,
  );
  const enrollmentStatusOptions = [
    {
      key: 'enrolled',
      title: 'Enrolled',
    },
    {
      key: 'not_enrolled',
      title: 'Not Enrolled',
    },
  ];
  const [memberId, setMemberId] = useState<string>(
    add ? '' : insurance?.policyNumber,
  );
  const [groupId, setGroupId] = useState<string>(
    add ? '' : insurance?.plans?.[0]?.groupNumber,
  );
  // const [rxBin, setRxBin] = useState<string>(add ? '' : insurance?.rxBin);
  const [planName, setPlanName] = useState<string>(
    add ? '' : insurance?.planName,
  );
  const [insuranceContactPhone, setInsuranceContactPhone] = useState<string>(
    add ? '' : insurance?.phones?.[0]?.number,
  );
  const [coverageStartDate, setCoverageStartDate] = useState<string>(
    add ? '' : insurance?.startDate,
  );
  const [coverageEndDate, setCoverageEndDate] = useState<string>(
    add ? '' : insurance?.endDate,
  );
  const [frontOfCard, setFrontOfCard] = useState<string>();
  const [backOfCard, setBackOfCard] = useState<string>();

  useEffect(() => {
    // Update prevStep on step change
    setPrevStep(step);
  }, [step]);

  const loadCarriers = async (page = 1, name = '') => {
    try {
      const carriers: any = await MEMBERS.fetchCarriers(
        page,
        insurancePerPage,
        name,
      );
      setInsurancesList(carriers.items);
      setInsuranceTotal(carriers.metadata.total);
      setInsurancePage(page);
    } catch (error) {
      console.error('Failed to fetch carriers:', error);
    }
  };

  useEffect(() => {
    if (!constants?.carriers || constants?.carriers?.length === 0) {
      loadCarriers();
      setInsurancesFromApi(true);
    }
  }, [constants?.carriers]);

  useEffect(() => {
    const frontCardAttachment = insurance?.cards
      ?.flatMap((card: any) => card.attachments || []) // Extract and merge all attachments
      .find((attachment: any) => attachment.name === 'front'); // Find the one named 'front'

    setFrontOfCard(frontCardAttachment?.url || ''); // Set the URL or default to ''
  }, [insurance]);

  useEffect(() => {
    const backCardAttachment = insurance?.cards
      ?.flatMap((card: any) => card.attachments || []) // Extract and merge all attachments
      .find((attachment: any) => attachment.name === 'back'); // Find the one named 'back'

    setBackOfCard(backCardAttachment?.url || ''); // Set the URL or default to ''
  }, [insurance]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setInsuranceSearch(value); // Track search state

    if (!insurancesFromApi) {
      if (value === '') {
        setInsurancesList(constants?.carriers || []);
        return;
      }

      const filteredInsurances = constants?.carriers.filter((carrier: any) => carrier.title.toLowerCase().includes(value.toLowerCase()));
      setInsurancesList(filteredInsurances || []);
    } else {
      // API search
      loadCarriers(1, value); // Reset to page 1 when searching
    }
  };

  const handleSelectType = (e: React.ChangeEvent<HTMLInputElement>) => {
    setPlanType(e.target.value);
    setPlanTypeName(
      constants?.planTypes.find((type: any) => type.key === e.target.value)
        ?.title || '',
    );
  };

  const createModelObject = () => ({
    userId: member.id,
    carrierId: insuranceCarrier,
    policyNumber: memberId,
    startDate: coverageStartDate,
    endDate: coverageEndDate,
    planType,
    planName,
    planInfo: [
      ...(planType !== 'medicare'
        ? [
          {
            name: planName,
            number: memberId,
            entitlementDate: coverageStartDate,
            endDate: coverageEndDate,
            type: planType,
            groupNumber: groupId,
            issuer: insuranceCarrierName,
            enrolled: true,
          },
        ]
        : []),
      ...(partA
        ? [
          {
            name: planType,
            number: memberId,
            entitlementDate: coverageStartDate,
            endDate: coverageEndDate,
            type: 'part a',
            groupNumber: groupId,
            issuer: insuranceCarrierName,
            enrolled: enrollmentStatusA === 'enrolled',
          },
        ]
        : []),
      ...(partB
        ? [
          {
            name: planType,
            number: memberId,
            entitlementDate: coverageStartDate,
            endDate: coverageEndDate,
            type: 'part b',
            groupNumber: groupId,
            issuer: insuranceCarrierName,
            enrolled: enrollmentStatusB === 'enrolled',
          },
        ]
        : []),
      ...(partC
        ? [
          {
            name: planType,
            number: memberId,
            entitlementDate: coverageStartDate,
            endDate: coverageEndDate,
            type: 'part c',
            groupNumber: groupId,
            issuer: insuranceCarrierName,
            enrolled: enrollmentStatusC === 'enrolled',
          },
        ]
        : []),
      ...(partD
        ? [
          {
            name: planType,
            number: memberId,
            entitlementDate: coverageStartDate,
            endDate: coverageEndDate,
            type: 'part d',
            groupNumber: groupId,
            issuer: insuranceCarrierName,
            enrolled: enrollmentStatusD === 'enrolled',
          },
        ]
        : []),
    ],
    // rxBin,
    insuranceContactPhone,
    frontOfCard,
    backOfCard,
    phones: [
      {
        label: 'main',
        number: insuranceContactPhone,
      },
    ],
    attachments: [
      ...(frontOfCard
        ? [
          {
            type: 'image',
            name: 'front',
            base64: frontOfCard,
          },
        ]
        : []),
      ...(backOfCard
        ? [
          {
            type: 'image',
            name: 'back',
            base64: backOfCard,
          },
        ]
        : []),
    ],
  });

  const handleConfirm = async () => {
    const model = createModelObject();
    if (add) {
      const response = await MEMBERS.createMemberInsurance(model);
      if (response) {
        router.push(`/members/insurance/${member.id}`);
      }
    } else {
      const response = await MEMBERS.updateMemberInsurance(
        insurance?.id,
        model,
      );
      if (response) {
        router.push(`/members/insurance/${member.id}`);
      }
    }
  };

  const handleSelectEnrollmentStatus = (
    event: React.ChangeEvent<HTMLInputElement>,
    part: string,
  ) => {
    if (part === 'a') {
      setEnrollmentStatusA(event.target.value);
    }
    if (part === 'b') {
      setEnrollmentStatusB(event.target.value);
    }
    if (part === 'c') {
      setEnrollmentStatusC(event.target.value);
    }
    if (part === 'd') {
      setEnrollmentStatusD(event.target.value);
    }
  };

  return (
    <>
      <div className="w-full px-10 pb-[100px]">
        <p className="text-[15px] font-[500] text-[#747A7A] mt-1 mb-2">
          Step
          {' '}
          {step}
          {' '}
          of 4
        </p>
        <div
          className="w-full transition-transform duration-200 ease"
          style={{
            // If increasing step, shift both steps to the left
            // If decreasing step, shift both steps to the right
            transform: `translateX(-${(step - 1) * 5}%)`,
          }}
        >
          {step === 1 && (
            <div
              className="w-full"
              style={{
                transform: isDecreasing
                  ? 'translateX(-4%)'
                  : isIncreasing
                    ? 'translateX(4%)'
                    : 'translateX(0)',
                transition: 'all 0.2s ease', // Add opacity transition
                position: 'relative',
              }}
            >
              <p className="m-0 p-0 font-[600] text-lg">
                Select Insurance Carrier
              </p>
              <div className="w-[392px] mt-4">
                <Search
                  placeholder="Search carriers..."
                  onChange={handleSearch}
                />
              </div>
              <div className="mt-4">
                <RadioGroup
                  value={insuranceCarrier}
                  onChange={(_, value) => {
                    setInsuranceCarrier(value);

                    const selectedCarrier = insurancesList.find(
                      (carrier: any) => (insurancesFromApi ? carrier.id : carrier.key) === value,
                    );
                    setInsuranceCarrierName(
                      insurancesFromApi
                        ? selectedCarrier?.name
                        : selectedCarrier?.title,
                    );
                  }}
                >
                  {insurancesList?.length > 0
                    && insurancesList?.map((carrier: any) => (
                      <FormControlLabel
                        className="py-3"
                        style={{ borderBottom: '1px solid #E0E0E0' }}
                        key={insurancesFromApi ? carrier.id : carrier.key}
                        value={insurancesFromApi ? carrier.id : carrier.key}
                        control={(
                          <Radio
                            sx={{
                              color: 'gray',
                              '&.Mui-checked': {
                                color: 'transparent',
                                '& svg': { fill: 'url(#radioGradient)' },
                              },
                            }}
                          />
                        )}
                        label={insurancesFromApi ? carrier.name : carrier.title}
                        name={insurancesFromApi ? carrier.name : carrier.title}
                      />
                    ))}
                </RadioGroup>
              </div>
              {insurancesFromApi && (
                <div className="mt-4">
                  <Pagination
                    count={Math.ceil(insuranceTotal / insurancePerPage)}
                    page={insurancePage}
                    onChange={(_, value) => {
                      setInsurancePage(value);
                      loadCarriers(value, insuranceSearch);
                    }}
                    className="pt-5 align-middle"
                  />
                </div>
              )}
            </div>
          )}
        </div>
        {step === 2 && (
          <div
            className="w-full"
            style={{
              transform: isDecreasing
                ? 'translateX(-4%)'
                : isIncreasing
                  ? 'translateX(4%)'
                  : 'translateX(0)',
              transition: 'all 0.2s ease', // Add opacity transition
              position: 'relative',
            }}
          >
            <p className="m-0 p-0 font-[600] text-lg">Select Plan Type</p>

            <div className="w-[400px] my-4">
              <SelectBox
                label={planType ? 'Plan type' : 'Select plan type'}
                keyVal="planType"
                defaultValue={planType}
                onChange={handleSelectType}
                items={constants?.planTypes}
              />
            </div>
            {planType === 'medicare' && (
              <div className="mt-12">
                <p className="m-0 p-0 font-[500] text-[15px] mb-3">
                  Add Plan Parts
                </p>
                <div
                  style={{
                    borderBottom: '1px solid #E0E0E0',
                    padding: '10px 0',
                  }}
                >
                  <div className="flex items-center ml-[-10px]">
                    <Checkbox
                      checked={partA}
                      onChange={() => setPartA(!partA)}
                    />
                    <p className="m-0 p-0 font-[500] text-[15px]">Part A</p>
                  </div>
                  {partA && (
                    <div className="flex items-center">
                      <div className="w-[400px] my-4">
                        <SelectBox
                          label={
                            enrollmentStatusA
                              ? 'Enrollment status'
                              : 'Select enrollment status'
                          }
                          keyVal="enrollmentStatusA"
                          defaultValue={enrollmentStatusA}
                          onChange={(event) => handleSelectEnrollmentStatus(event, 'a')}
                          items={enrollmentStatusOptions}
                          labelStyle={{
                            color:
                              enrollmentStatusA === 'enrolled' ? '#008390' : '',
                          }}
                        />
                      </div>
                      <div className="w-[400px] my-4 ml-[30px]">
                        <DateSelector
                          name="enrollmentDateA"
                          defaultValue={enrollmentDateA}
                          pickerProps={
                            {
                              label: enrollmentDateA
                                ? 'Coverage start date'
                                : 'Select coverage start date',
                              slotProps: {
                                field: {
                                  // disabled:
                                  //   formik.values.tmp.dateType === 'no_end_date',
                                  clearable: true,
                                  onClear: () => {
                                    setEnrollmentDateA('');
                                  },
                                },
                              },
                            } as any
                          }
                          onChange={(e) => {
                            const date = dayjs(e.target.value).format(
                              'YYYY-MM-DD',
                            );
                            setEnrollmentDateA(date);
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
                <div
                  style={{
                    borderBottom: '1px solid #E0E0E0',
                    padding: '10px 0',
                  }}
                >
                  <div className="flex items-center ml-[-10px]">
                    <Checkbox
                      checked={partB}
                      onChange={() => setPartB(!partB)}
                    />
                    <p className="m-0 p-0 font-[500] text-[15px]">Part B</p>
                  </div>
                  {partB && (
                    <div className="flex items-center">
                      <div className="w-[400px] my-4">
                        <SelectBox
                          label={
                            enrollmentStatusB
                              ? 'Enrollment status'
                              : 'Select enrollment status'
                          }
                          keyVal="enrollmentStatusB"
                          defaultValue={enrollmentStatusB}
                          onChange={(event) => handleSelectEnrollmentStatus(event, 'b')}
                          items={enrollmentStatusOptions}
                          labelStyle={{
                            color:
                              enrollmentStatusB === 'enrolled' ? '#008390' : '',
                          }}
                        />
                      </div>
                      <div className="w-[400px] my-4 ml-[30px]">
                        <DateSelector
                          name="enrollmentDateB"
                          defaultValue={enrollmentDateB}
                          pickerProps={
                            {
                              label: enrollmentDateB
                                ? 'Coverage start date'
                                : 'Select coverage start date',
                              slotProps: {
                                field: {
                                  // disabled:
                                  //   formik.values.tmp.dateType === 'no_end_date',
                                  clearable: true,
                                  onClear: () => {
                                    setEnrollmentDateB('');
                                  },
                                },
                              },
                            } as any
                          }
                          onChange={(e) => {
                            const date = dayjs(e.target.value).format(
                              'YYYY-MM-DD',
                            );
                            setEnrollmentDateB(date);
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
                <div
                  style={{
                    borderBottom: '1px solid #E0E0E0',
                    padding: '10px 0',
                  }}
                >
                  <div className="flex items-center ml-[-10px]">
                    <Checkbox
                      checked={partC}
                      onChange={() => setPartC(!partC)}
                    />
                    <p className="m-0 p-0 font-[500] text-[15px]">Part C</p>
                  </div>
                  {partC && (
                    <div className="flex items-center">
                      <div className="w-[400px] my-4">
                        <SelectBox
                          label={
                            enrollmentStatusC
                              ? 'Enrollment status'
                              : 'Select enrollment status'
                          }
                          keyVal="enrollmentStatusC"
                          defaultValue={enrollmentStatusC}
                          onChange={(event) => handleSelectEnrollmentStatus(event, 'c')}
                          items={enrollmentStatusOptions}
                          labelStyle={{
                            color:
                              enrollmentStatusC === 'enrolled' ? '#008390' : '',
                          }}
                        />
                      </div>
                      <div className="w-[400px] my-4 ml-[30px]">
                        <DateSelector
                          name="enrollmentDateC"
                          defaultValue={enrollmentDateC}
                          pickerProps={
                            {
                              label: enrollmentDateC
                                ? 'Coverage start date'
                                : 'Select coverage start date',
                              slotProps: {
                                field: {
                                  // disabled:
                                  //   formik.values.tmp.dateType === 'no_end_date',
                                  clearable: true,
                                  onClear: () => {
                                    setEnrollmentDateC('');
                                  },
                                },
                              },
                            } as any
                          }
                          onChange={(e) => {
                            const date = dayjs(e.target.value).format(
                              'YYYY-MM-DD',
                            );
                            setEnrollmentDateC(date);
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
                <div
                  style={{
                    borderBottom: '1px solid #E0E0E0',
                    padding: '10px 0',
                  }}
                >
                  <div className="flex items-center ml-[-10px]">
                    <Checkbox
                      checked={partD}
                      onChange={() => setPartD(!partD)}
                    />
                    <p className="m-0 p-0 font-[500] text-[15px]">Part D</p>
                  </div>
                  {partD && (
                    <div className="flex items-center">
                      <div className="w-[400px] my-4">
                        <SelectBox
                          label={
                            enrollmentStatusD
                              ? 'Enrollment status'
                              : 'Select enrollment status'
                          }
                          keyVal="enrollmentStatusD"
                          defaultValue={enrollmentStatusD}
                          onChange={(event) => handleSelectEnrollmentStatus(event, 'd')}
                          items={enrollmentStatusOptions}
                          labelStyle={{
                            color:
                              enrollmentStatusD === 'enrolled' ? '#008390' : '',
                          }}
                        />
                      </div>
                      <div className="w-[400px] my-4 ml-[30px]">
                        <DateSelector
                          name="enrollmentDateD"
                          defaultValue={enrollmentDateD}
                          pickerProps={
                            {
                              label: enrollmentDateD
                                ? 'Coverage start date'
                                : 'Select coverage start date',
                              slotProps: {
                                field: {
                                  // disabled:
                                  //   formik.values.tmp.dateType === 'no_end_date',
                                  clearable: true,
                                  onClear: () => {
                                    setEnrollmentDateD('');
                                  },
                                },
                              },
                            } as any
                          }
                          onChange={(e) => {
                            const date = dayjs(e.target.value).format(
                              'YYYY-MM-DD',
                            );
                            setEnrollmentDateD(date);
                          }}
                        />
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        )}
        {step === 3 && (
          <div
            className="w-full"
            style={{
              transform: isDecreasing
                ? 'translateX(-4%)'
                : isIncreasing
                  ? 'translateX(4%)'
                  : 'translateX(0)',
              transition: 'all 0.2s ease', // Add opacity transition
              position: 'relative',
            }}
          >
            <p className="m-0 p-0 font-[600] text-lg">Add Plan Details</p>
            <div className="mt-4">
              <div className="flex flex-wrap w-full ml-[-15px]">
                <TextField
                  name="memberId"
                  onChange={(e) => setMemberId(e.target.value)}
                  value={memberId}
                  placeholder="Enter Member ID"
                  label="Member ID"
                  variant="filled"
                  className="md-lg:w-[30%] min-w-[280px] w-[48%] mb-4 mx-[15px]"
                />
                <TextField
                  name="groupId"
                  onChange={(e) => setGroupId(e.target.value)}
                  value={groupId}
                  placeholder="Enter Group ID"
                  label="Group ID"
                  variant="filled"
                  className="md-lg:w-[30%] min-w-[280px] w-[48%] mb-4 mx-[15px]"
                />
                {/* <TextField
                  name="rxBin"
                  onChange={(e) => setRxBin(e.target.value)}
                  value={rxBin}
                  placeholder="Enter Rx Bin No."
                  label="Rx Bin No."
                  variant="filled"
                  className="md-lg:w-[30%] min-w-[280px] w-[48%] mb-4 mx-[15px]"
                /> */}
                <TextField
                  name="planName"
                  onChange={(e) => setPlanName(e.target.value)}
                  value={planName}
                  placeholder="Enter Plan Name"
                  label="Plan Name"
                  variant="filled"
                  className="md-lg:w-[30%] min-w-[280px] w-[48%] mb-4 mx-[15px]"
                />
                <div className="md-lg:w-[30%] min-w-[280px] w-[48%] mb-4 mx-[15px]">
                  <DateSelector
                    name="coverageStartDate"
                    defaultValue={coverageStartDate}
                    pickerProps={
                      {
                        label: coverageStartDate
                          ? 'Coverage start date'
                          : 'Select coverage start date',
                        slotProps: {
                          field: {
                            // disabled:
                            //   formik.values.tmp.dateType === 'no_end_date',
                            clearable: true,
                            onClear: () => {
                              setCoverageStartDate('');
                            },
                          },
                        },
                      } as any
                    }
                    onChange={(e) => {
                      const date = dayjs(e.target.value).format('YYYY-MM-DD');
                      setCoverageStartDate(date);
                    }}
                  />
                </div>
                <div className="md-lg:w-[30%] min-w-[280px] w-[48%] mb-4 mx-[15px]">
                  <DateSelector
                    name="coverageEndDate"
                    defaultValue={coverageEndDate}
                    pickerProps={
                      {
                        label: coverageEndDate
                          ? 'Coverage end date'
                          : 'Select coverage end date',
                        slotProps: {
                          field: {
                            // disabled:
                            //   formik.values.tmp.dateType === 'no_end_date',
                            clearable: true,
                            onClear: () => {
                              setCoverageEndDate('');
                            },
                          },
                        },
                      } as any
                    }
                    onChange={(e) => {
                      const date = dayjs(e.target.value).format('YYYY-MM-DD');
                      setCoverageEndDate(date);
                    }}
                  />
                </div>
                <TextField
                  name="insuranceContactPhone"
                  onChange={(e) => setInsuranceContactPhone(e.target.value)}
                  value={insuranceContactPhone}
                  placeholder="Enter Insurance Contact Phone Number"
                  label="Insurance Contact Phone Number"
                  variant="filled"
                  className="md-lg:w-[30%] min-w-[280px] w-[48%] mb-4 mx-[15px]"
                />
              </div>
              <div className="mt-10">
                <p className="m-0 p-0 font-[600] text-lg">Card Photos</p>
                <div className="flex flex-wrap">
                  <div className="mr-[30px]">
                    <p className="m-0 p-0 font-[500] text-lg mt-5">
                      Front of Card
                    </p>
                    <div
                      className="mt-2 w-[400px] h-[280px] flex flex-col items-center justify-center cursor-pointer card-upload overflow-hidden"
                      onClick={() => {
                        const fileInput = document.createElement('input');
                        fileInput.type = 'file';
                        fileInput.accept = 'image/*';
                        fileInput.onchange = (e) => {
                          const file = (e.target as HTMLInputElement)
                            .files?.[0];
                          if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                              const imageUrl = e.target?.result as string;
                              setFrontOfCard(imageUrl);
                            };
                            reader.readAsDataURL(file);
                          }
                        };
                        fileInput.click();
                      }}
                    >
                      {frontOfCard ? (
                        <Image
                          src={frontOfCard}
                          alt="Insurance Card"
                          width={398}
                          height={278}
                        />
                      ) : (
                        <>
                          <Image
                            src="/icons/icon-file.svg"
                            alt="file"
                            width={72}
                            height={72}
                          />
                          <p className="m-0 p-0 font-[600] text-[15px] text-[#E97100] mt-[10px]">
                            Select Photo of Insurance Card
                          </p>
                          <p className="m-0 p-0 font-[400] text-[11px] text-[#262D2D] mt-[18px] italic">
                            Files must be (jpeg, png, pdf) format
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                  <div>
                    <p className="m-0 p-0 font-[500] text-lg mt-5">
                      Back of Card
                    </p>
                    <div
                      className="mt-2 w-[400px] h-[280px] flex flex-col items-center justify-center cursor-pointer card-upload overflow-hidden"
                      onClick={() => {
                        const fileInput = document.createElement('input');
                        fileInput.type = 'file';
                        fileInput.accept = 'image/*';
                        fileInput.onchange = (e) => {
                          const file = (e.target as HTMLInputElement)
                            .files?.[0];
                          if (file) {
                            const reader = new FileReader();
                            reader.onload = (e) => {
                              const imageUrl = e.target?.result as string;
                              setBackOfCard(imageUrl);
                            };
                            reader.readAsDataURL(file);
                          }
                        };
                        fileInput.click();
                      }}
                    >
                      {backOfCard ? (
                        <Image
                          src={backOfCard}
                          alt="Insurance Card"
                          width={398}
                          height={278}
                        />
                      ) : (
                        <>
                          <Image
                            src="/icons/icon-file.svg"
                            alt="file"
                            width={72}
                            height={72}
                          />
                          <p className="m-0 p-0 font-[600] text-[15px] text-[#E97100] mt-[10px]">
                            Select Photo of Insurance Card
                          </p>
                          <p className="m-0 p-0 font-[400] text-[11px] text-[#262D2D] mt-[18px] italic">
                            Files must be (jpeg, png, pdf) format
                          </p>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
        {step === 4 && (
          <div
            className="w-full"
            style={{
              transform: isDecreasing
                ? 'translateX(-4%)'
                : isIncreasing
                  ? 'translateX(4%)'
                  : 'translateX(0)',
              transition: 'all 0.2s ease', // Add opacity transition
              position: 'relative',
            }}
          >
            <p className="m-0 p-0 font-[600] text-lg">
              Review Insurance Details
            </p>
            <div className="mt-10 flex flex-wrap w-full ml-[-15px]">
              <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                  Carrier
                </p>
                <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                  {insuranceCarrierName}
                </p>
              </div>
              <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                  Plan Type
                </p>
                <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                  {capitalizeWords(planType || '')}
                </p>
              </div>
              {partA && (
                <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                  <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                    Plan Part A
                  </p>
                  <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                    {capitalizeWords(
                      enrollmentStatusA && enrollmentStatusA === 'not_enrolled'
                        ? 'Not Enrolled'
                        : 'Enrolled',
                    )}
                    {enrollmentDateA ? ' - ' : ''}
                    {enrollmentDateA
                      ? dayjs(enrollmentDateA).format('MMM DD, YYYY')
                      : ''}
                  </p>
                </div>
              )}
              {partB && (
                <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                  <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                    Plan Part B
                  </p>
                  <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                    {capitalizeWords(
                      enrollmentStatusB && enrollmentStatusB === 'not_enrolled'
                        ? 'Not Enrolled'
                        : 'Enrolled',
                    )}
                    {enrollmentDateB ? ' - ' : ''}
                    {enrollmentDateB
                      ? dayjs(enrollmentDateB).format('MMM DD, YYYY')
                      : ''}
                  </p>
                </div>
              )}
              {partC && (
                <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                  <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                    Plan Part C
                  </p>
                  <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                    {capitalizeWords(
                      enrollmentStatusC && enrollmentStatusC === 'not_enrolled'
                        ? 'Not Enrolled'
                        : 'Enrolled',
                    )}
                    {enrollmentDateC ? ' - ' : ''}
                    {enrollmentDateC
                      ? dayjs(enrollmentDateC).format('MMM DD, YYYY')
                      : ''}
                  </p>
                </div>
              )}
              {partD && (
                <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                  <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                    Plan Part D
                  </p>
                  <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                    {capitalizeWords(
                      enrollmentStatusD && enrollmentStatusD === 'not_enrolled'
                        ? 'Not Enrolled'
                        : 'Enrolled',
                    )}
                    {enrollmentDateD ? ' - ' : ''}
                    {enrollmentDateD
                      ? dayjs(enrollmentDateD).format('MMM DD, YYYY')
                      : ''}
                  </p>
                </div>
              )}
              <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                  Member No.
                </p>
                <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                  {memberId}
                </p>
              </div>
              <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                  Group No.
                </p>
                <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                  {groupId}
                </p>
              </div>
              {/* <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                  Rx Bin
                </p>
                <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                  {rxBin}
                </p>
              </div> */}
              <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                  Plan name
                </p>
                <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                  {planName}
                </p>
              </div>
              <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                  Coverage start date
                </p>
                <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                  {coverageStartDate
                    ? dayjs(coverageStartDate).format('MMM DD, YYYY')
                    : ''}
                </p>
              </div>
              <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                  Coverage end date
                </p>
                <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                  {coverageEndDate
                    ? dayjs(coverageEndDate).format('MMM DD, YYYY')
                    : ''}
                </p>
              </div>
              <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                  Insurance contact phone
                </p>
                <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                  {insuranceContactPhone}
                </p>
              </div>
            </div>
            <div className="w-full flex flex-wrap">
              {frontOfCard && (
                <div className="mr-[30px]">
                  <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A] mt-5">
                    Front of Card
                  </p>
                  <div
                    className="mt-1 w-[400px] h-[280px] flex flex-col items-center justify-center card-upload no-hover overflow-hidden"
                    // onClick={() => {
                    //   const fileInput = document.createElement('input');
                    //   fileInput.type = 'file';
                    //   fileInput.accept = 'image/*';
                    //   fileInput.onchange = (e) => {
                    //     const file = (e.target as HTMLInputElement).files?.[0];
                    //     if (file) {
                    //       const reader = new FileReader();
                    //       reader.onload = (e) => {
                    //         const imageUrl = e.target?.result as string;
                    //         setFrontOfCard(imageUrl);
                    //       };
                    //       reader.readAsDataURL(file);
                    //     }
                    //   };
                    //   fileInput.click();
                    // }}
                  >
                    {frontOfCard ? (
                      <Image
                        src={frontOfCard}
                        alt="Insurance Card"
                        width={398}
                        height={278}
                      />
                    ) : (
                      // <Avatar alt="A" className="rounded-md w-full h-auto aspect-video" src={frontOfCard} />
                      <>
                        <Image
                          src="/icons/icon-file.svg"
                          alt="file"
                          width={72}
                          height={72}
                        />
                        <p className="m-0 p-0 font-[600] text-[15px] text-[#E97100] mt-[10px]">
                          Select Photo of Insurance Card
                        </p>
                        <p className="m-0 p-0 font-[400] text-[11px] text-[#262D2D] mt-[18px] italic">
                          Files must be (jpeg, png, pdf) format
                        </p>
                      </>
                    )}
                  </div>
                </div>
              )}
              {backOfCard && (
                <div>
                  <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A] mt-5">
                    Back of Card
                  </p>
                  <div
                    className="mt-1 w-[400px] h-[280px] flex flex-col items-center justify-center no-hover card-upload overflow-hidden"
                    // onClick={() => {
                    //   const fileInput = document.createElement('input');
                    //   fileInput.type = 'file';
                    //   fileInput.accept = 'image/*';
                    //   fileInput.onchange = (e) => {
                    //     const file = (e.target as HTMLInputElement).files?.[0];
                    //     if (file) {
                    //       const reader = new FileReader();
                    //       reader.onload = (e) => {
                    //         const imageUrl = e.target?.result as string;
                    //         setBackOfCard(imageUrl);
                    //       };
                    //       reader.readAsDataURL(file);
                    //     }
                    //   };
                    //   fileInput.click();
                    // }}
                  >
                    {backOfCard ? (
                      <Image
                        src={backOfCard}
                        alt="Insurance Card"
                        width={398}
                        height={278}
                      />
                    ) : (
                      <>
                        <Image
                          src="/icons/icon-file.svg"
                          alt="file"
                          width={72}
                          height={72}
                        />
                        <p className="m-0 p-0 font-[600] text-[15px] text-[#E97100] mt-[10px]">
                          Select Photo of Insurance Card
                        </p>
                        <p className="m-0 p-0 font-[400] text-[11px] text-[#262D2D] mt-[18px] italic">
                          Files must be (jpeg, png, pdf) format
                        </p>
                      </>
                    )}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
      {/* SVG Gradient Definition (Placed Once in the Component) */}
      <svg width="0" height="0">
        <defs>
          <linearGradient
            id="radioGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop
              offset="0%"
              style={{ stopColor: '#0198A5', stopOpacity: 1 }}
            />
            <stop
              offset="100%"
              style={{ stopColor: '#008390', stopOpacity: 1 }}
            />
          </linearGradient>
        </defs>
      </svg>
      <div className="ml-[170px] mr-10 py-4 flex justify-between fixed bottom-0 left-0 right-0 z-10">
        {step > 1 ? (
          <Button
            className="rounded-md m-0 px-[14px] text-[15px] flex items-center justify-center !min-w-[100px] text-[#262D2D] font-[500] bg-gradient-to-b from-[#F7F8F8] to-[#DCDFDF] hover:bg-gradient-to-b hover:from-[#F9FAFA] hover:to-[#D0D3D3]"
            onClick={() => setStep(step - 1)}
          >
            <ArrowBack className="mr-3 w-[18px] h-[18px]" />
            <p className="font-[500] m-0 p-0 pt-[1px]">Previous</p>
          </Button>
        ) : (
          <div />
        )}
        <Button
          variant="contained"
          color="primary"
          className="rounded-md m-0 px-[14px] text-[15px] flex items-center justify-center !min-w-[100px]"
          onClick={step === 4 ? () => handleConfirm() : () => setStep(step + 1)}
        >
          <p className="font-[500] m-0 p-0 pt-[1px]">
            {step === 4 ? 'Confirm' : 'Continue'}
          </p>
          {step === 4 ? (
            <Check className="ml-3 w-4 h-4" />
          ) : (
            <ArrowForward className="ml-3 w-[18px] h-[18px]" />
          )}
        </Button>
      </div>
    </>
  );

  // background: linear-gradient(180deg, #0198A5 0%, #008390 100%), #008390;
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    // let orgId;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      org = cookies.orgID;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    // eslint-disable-next-line max-len
    // const request: any = await API.MEMBERS.fetchMemberInsurance(memberId, token);
    return {
      props: {
        // insurances: request.items || [],
        member: member || [],
      },
    };
  } catch (error) {
    return {
      props: {
        // insurances: [],
        member: [],
      },
    };
  }
}

export default InsuranceAddUpdate;
