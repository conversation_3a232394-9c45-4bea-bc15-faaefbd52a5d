/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import React, { useEffect, useState } from 'react';
import dayjs from 'dayjs';
import Image from 'next/image';
import { capitalizeWords } from 'utils/helper';
import { MEMBERS } from 'api/src/routes/members';
import { useRouter } from 'next/router';
import { Avatar, Dialog, DialogContent } from '@mui/material';

interface InsuranceAddUpdateProps {
  member: any;
  insurance: any;
}
function InsuranceDetail({ member, insurance }: InsuranceAddUpdateProps) {
  const router = useRouter();
  const [insuranceCarrier, setInsuranceCarrier] = useState<string>(
    insurance?.carrierId?.toLowerCase() || '',
  );
  const [insuranceCarrierName, setInsuranceCarrierName] = useState<string>(
    capitalizeWords(insurance?.plans?.[0]?.issuer, true) || '',
  );
  const [planType, setPlanType] = useState<any>(insurance?.planType || '');
  const [planTypeName, setPlanTypeName] = useState<string>(
    insurance?.planTypeName || '',
  );
  const [partA, setPartA] = useState(insurance?.partA || false);
  const [partB, setPartB] = useState(insurance?.partB || false);
  const [partC, setPartC] = useState(insurance?.partC || false);
  const [partD, setPartD] = useState(insurance?.partD || false);
  const [enrollmentStatusA, setEnrollmentStatusA] = useState<string>(
    insurance?.enrollmentStatusA || '',
  );
  const [enrollmentStatusB, setEnrollmentStatusB] = useState<string>(
    insurance?.enrollmentStatusB || '',
  );
  const [enrollmentStatusC, setEnrollmentStatusC] = useState<string>(
    insurance?.enrollmentStatusC || '',
  );
  const [enrollmentStatusD, setEnrollmentStatusD] = useState<string>(
    insurance?.enrollmentStatusD || '',
  );
  const [enrollmentDateA, setEnrollmentDateA] = useState<string>(
    insurance?.enrollmentDateA || '',
  );
  const [enrollmentDateB, setEnrollmentDateB] = useState<string>(
    insurance?.enrollmentDateB || '',
  );
  const [enrollmentDateC, setEnrollmentDateC] = useState<string>(
    insurance?.enrollmentDateC || '',
  );
  const [enrollmentDateD, setEnrollmentDateD] = useState<string>(
    insurance?.enrollmentDateD || '',
  );
  const enrollmentStatusOptions = [
    {
      key: 'enrolled',
      title: 'Enrolled',
    },
    {
      key: 'not_enrolled',
      title: 'Not Enrolled',
    },
  ];
  const [memberId, setMemberId] = useState<string>(
    insurance?.policyNumber || '',
  );
  const [groupId, setGroupId] = useState<string>(
    insurance?.plans?.[0]?.groupNumber || '',
  );
  // const [rxBin, setRxBin] = useState<string>(add ? '' : insurance?.rxBin);
  const [planName, setPlanName] = useState<string>(insurance?.planName || '');
  const [insuranceContactPhone, setInsuranceContactPhone] = useState<string>(
    insurance?.phones?.[0]?.number || '',
  );
  const [coverageStartDate, setCoverageStartDate] = useState<string>(
    insurance?.startDate || '',
  );
  const [coverageEndDate, setCoverageEndDate] = useState<string>(
    insurance?.endDate || '',
  );
  const [frontOfCard, setFrontOfCard] = useState<string>();
  const [backOfCard, setBackOfCard] = useState<string>();
  const [open, setOpen] = useState(false); // State to handle Dialog open/close
  const [currentImage, setCurrentImage] = useState(''); // State to handle current image

  useEffect(() => {
    const frontCardAttachment = insurance?.cards
      ?.flatMap((card: any) => card.attachments || []) // Extract and merge all attachments
      .find((attachment: any) => attachment.name === 'front'); // Find the one named 'front'

    setFrontOfCard(frontCardAttachment?.url || ''); // Set the URL or default to ''
  }, [insurance]);

  useEffect(() => {
    const backCardAttachment = insurance?.cards
      ?.flatMap((card: any) => card.attachments || []) // Extract and merge all attachments
      .find((attachment: any) => attachment.name === 'back'); // Find the one named 'back'

    setBackOfCard(backCardAttachment?.url || ''); // Set the URL or default to ''
  }, [insurance]);

  const handleClickOpen = (imageUrl: string) => {
    setCurrentImage(imageUrl);
    if (imageUrl.endsWith('.pdf')) {
      window.open(imageUrl, '_blank');
      return;
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  return (
    <>
      <div className="w-full px-10 pb-[100px]">
        <p className="m-0 p-0 font-[600] text-lg">Insurance Details</p>
        <div className="mt-10 flex flex-wrap w-full ml-[-15px]">
          <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
              Carrier
            </p>
            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
              {insuranceCarrierName}
            </p>
          </div>
          <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
              Plan Type
            </p>
            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
              {capitalizeWords(planType || '')}
            </p>
          </div>
          {partA && (
            <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
              <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                Plan Part A
              </p>
              <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                {capitalizeWords(
                  enrollmentStatusA && enrollmentStatusA === 'not_enrolled'
                    ? 'Not Enrolled'
                    : 'Enrolled',
                )}
                {enrollmentDateA ? ' - ' : ''}
                {enrollmentDateA
                  ? dayjs(enrollmentDateA).format('MMM DD, YYYY')
                  : ''}
              </p>
            </div>
          )}
          {partB && (
            <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
              <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                Plan Part B
              </p>
              <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                {capitalizeWords(
                  enrollmentStatusB && enrollmentStatusB === 'not_enrolled'
                    ? 'Not Enrolled'
                    : 'Enrolled',
                )}
                {enrollmentDateB ? ' - ' : ''}
                {enrollmentDateB
                  ? dayjs(enrollmentDateB).format('MMM DD, YYYY')
                  : ''}
              </p>
            </div>
          )}
          {partC && (
            <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
              <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                Plan Part C
              </p>
              <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                {capitalizeWords(
                  enrollmentStatusC && enrollmentStatusC === 'not_enrolled'
                    ? 'Not Enrolled'
                    : 'Enrolled',
                )}
                {enrollmentDateC ? ' - ' : ''}
                {enrollmentDateC
                  ? dayjs(enrollmentDateC).format('MMM DD, YYYY')
                  : ''}
              </p>
            </div>
          )}
          {partD && (
            <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
              <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                Plan Part D
              </p>
              <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                {capitalizeWords(
                  enrollmentStatusD && enrollmentStatusD === 'not_enrolled'
                    ? 'Not Enrolled'
                    : 'Enrolled',
                )}
                {enrollmentDateD ? ' - ' : ''}
                {enrollmentDateD
                  ? dayjs(enrollmentDateD).format('MMM DD, YYYY')
                  : ''}
              </p>
            </div>
          )}
          <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
              Member No.
            </p>
            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
              {memberId}
            </p>
          </div>
          <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
              Group No.
            </p>
            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
              {groupId}
            </p>
          </div>
          {/* <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
                <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                  Rx Bin
                </p>
                <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                  {rxBin}
                </p>
              </div> */}
          <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
              Plan name
            </p>
            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
              {planName}
            </p>
          </div>
          <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
              Coverage start date
            </p>
            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
              {coverageStartDate
                ? dayjs(coverageStartDate).format('MMM DD, YYYY')
                : ''}
            </p>
          </div>
          <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
              Coverage end date
            </p>
            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
              {coverageEndDate
                ? dayjs(coverageEndDate).format('MMM DD, YYYY')
                : ''}
            </p>
          </div>
          <div className="w-[30%] min-w-[280px] mb-4 mx-[15px]">
            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
              Insurance contact phone
            </p>
            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
              {insuranceContactPhone}
            </p>
          </div>
        </div>
        <div className="w-full flex flex-wrap">
          {frontOfCard && (
            <div className="mr-[30px]">
              <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A] mt-5">
                Front of Card
              </p>
              <div
                className="mt-1 w-[400px] h-[280px] flex flex-col items-center justify-center card-upload overflow-hidden cursor-pointer"
              >
                {frontOfCard ? (
                  <Image
                    src={frontOfCard}
                    alt="Insurance Card"
                    width={398}
                    height={278}
                    onClick={() => handleClickOpen(frontOfCard)}
                  />
                ) : (
                  <>
                    <Image
                      src="/icons/icon-file.svg"
                      alt="file"
                      width={72}
                      height={72}
                    />
                    <p className="m-0 p-0 font-[600] text-[15px] text-[#E97100] mt-[10px]">
                      Select Photo of Insurance Card
                    </p>
                    <p className="m-0 p-0 font-[400] text-[11px] text-[#262D2D] mt-[18px] italic">
                      Files must be (jpeg, png, pdf) format
                    </p>
                  </>
                )}
              </div>
            </div>
          )}
          {backOfCard && (
            <div>
              <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A] mt-5">
                Back of Card
              </p>
              <div
                className="mt-1 w-[400px] h-[280px] flex flex-col items-center justify-center card-upload overflow-hidden cursor-pointer"
              >
                {backOfCard ? (
                  <Image
                    src={backOfCard}
                    alt="Insurance Card"
                    width={398}
                    height={278}
                    onClick={() => handleClickOpen(backOfCard)}
                  />
                ) : (
                  <>
                    <Image
                      src="/icons/icon-file.svg"
                      alt="file"
                      width={72}
                      height={72}
                    />
                    <p className="m-0 p-0 font-[600] text-[15px] text-[#E97100] mt-[10px]">
                      Select Photo of Insurance Card
                    </p>
                    <p className="m-0 p-0 font-[400] text-[11px] text-[#262D2D] mt-[18px] italic">
                      Files must be (jpeg, png, pdf) format
                    </p>
                  </>
                )}
              </div>
            </div>
          )}
        </div>
        <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
          <DialogContent>
            <Avatar
              className="rounded-md  w-[100%] h-auto object-contain"
              src={currentImage}
            />
          </DialogContent>
        </Dialog>
      </div>
      {/* SVG Gradient Definition (Placed Once in the Component) */}
      <svg width="0" height="0">
        <defs>
          <linearGradient
            id="radioGradient"
            x1="0%"
            y1="0%"
            x2="100%"
            y2="100%"
          >
            <stop
              offset="0%"
              style={{ stopColor: '#0198A5', stopOpacity: 1 }}
            />
            <stop
              offset="100%"
              style={{ stopColor: '#008390', stopOpacity: 1 }}
            />
          </linearGradient>
        </defs>
      </svg>
      {/* <div className="ml-[170px] mr-10 bg-white py-4 flex justify-between fixed bottom-0 left-0 right-0 z-10">
        {step > 1 ? (
          <Button
            className="rounded-md m-0 px-[14px] text-[15px] flex items-center justify-center !min-w-[100px] text-[#262D2D] font-[500] bg-gradient-to-b from-[#F7F8F8] to-[#DCDFDF] hover:bg-gradient-to-b hover:from-[#F9FAFA] hover:to-[#D0D3D3]"
            onClick={() => setStep(step - 1)}
          >
            <ArrowBack className="mr-3 w-[18px] h-[18px]" />
            <p className="font-[500] m-0 p-0 pt-[1px]">Previous</p>
          </Button>
        ) : (
          <div />
        )}
        <Button
          variant="contained"
          color="primary"
          className="rounded-md m-0 px-[14px] text-[15px] flex items-center justify-center !min-w-[100px]"
          onClick={step === 4 ? () => handleConfirm() : () => setStep(step + 1)}
        >
          <p className="font-[500] m-0 p-0 pt-[1px]">
            {step === 4 ? 'Confirm' : 'Continue'}
          </p>
          {step === 4 ? (
            <Check className="ml-3 w-4 h-4" />
          ) : (
            <ArrowForward className="ml-3 w-[18px] h-[18px]" />
          )}
        </Button>
      </div> */}
    </>
  );

  // background: linear-gradient(180deg, #0198A5 0%, #008390 100%), #008390;
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    // let orgId;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      org = cookies.orgID;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    // eslint-disable-next-line max-len
    // const request: any = await API.MEMBERS.fetchMemberInsurance(memberId, token);
    return {
      props: {
        // insurances: request.items || [],
        member: member || [],
      },
    };
  } catch (error) {
    return {
      props: {
        // insurances: [],
        member: [],
      },
    };
  }
}

export default InsuranceDetail;
