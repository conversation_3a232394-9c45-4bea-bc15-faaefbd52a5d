/* eslint-disable jsx-a11y/no-static-element-interactions */

import { Modal } from '@mui/material';
import { useRef } from 'react';
import InsuranceAddUpdateModal from 'components/members/insurance/add-edit-modal';

interface AddInsuranceModalProps {
  open: boolean;
  setOpen: (value: boolean) => void;
  member: any;
  handleAdd: (insurance: any) => void;
}

function AddInsuranceModal({
  open, setOpen, member, handleAdd,
}: AddInsuranceModalProps) {
  const backdropRef = useRef<HTMLDivElement | null>(null);
  const mouseDownOnBackdrop = useRef(false);
  const handleAddAndClose = (insurance: any) => {
    handleAdd(insurance);
    setOpen(false);
  };
  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div
        ref={backdropRef}
        className="fixed inset-0 flex items-center justify-center p-4 z-50"
        onMouseDown={(e) => {
          mouseDownOnBackdrop.current = e.target === e.currentTarget;
        }}
        onMouseUp={(e) => {
          if (mouseDownOnBackdrop.current && e.target === e.currentTarget) {
            setOpen(false);
          }
        }}
      >
        <div
          className="flex flex-col h-[95vh] w-[90%] bg-white rounded-lg overflow-hidden"
          // onClick={(e) => e.stopPropagation()}
        //   onKeyDown={(e) => {
        //     if (e.key === 'Enter') {
        //       e.preventDefault();
        //       if (!validateFields()) return;
        //       handleAdd(type, formValues);
        //       setFormValues({});
        //       setMissingFields([]);
        //       setOpen(false);
        //     }
        //   }}
          tabIndex={0}
        >
          <InsuranceAddUpdateModal member={member} add handleAdd={handleAddAndClose} />
        </div>
      </div>
    </Modal>
  );
}

export default AddInsuranceModal;
