/* eslint-disable @typescript-eslint/no-explicit-any */
import React, { useContext, useState, useEffect } from 'react';
import { Drawer, But<PERSON>, Checkbox } from '@mui/material';
import { Member, MemberTags } from 'types/members-response-type';
import MainLayout from 'layouts/main-layout';
import MemberInnerPageHeader from 'components/inner-page-header/member';
import Check from '@mui/icons-material/Check';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { Close } from '@mui/icons-material';

interface MemberTagsDrawerProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  // eslint-disable-next-line no-unused-vars
  handleSave: (tags: Member['tags']) => void;
  existingTags: Member['tags'];
}

function MemberTagsDrawer({
  open,
  setOpen,
  handleSave,
  existingTags,
}: MemberTagsDrawerProps) {
  const constants = useContext(ConstantsContext);
  const { memberTags } = constants || {};
  const [tags, setTags] = useState<Member['tags']>([]);
  const handleClose = () => {
    setOpen(false);
  };

  const saveTags = () => {
    handleSave(tags);
    handleClose();
  };

  function handleChange(key: string, name: string, color: string) {
    setTags((prevTags: MemberTags[]) => {
      if (prevTags.some((tag) => tag.key === key)) {
        return prevTags.filter((tag) => tag.key !== key);
      }
      return [...prevTags, {
        key,
        name,
        color,
        updatedAt: new Date().toISOString(),
      }];
    });
  }

  useEffect(() => {
    setTags(existingTags);
  }, [existingTags]);

  return (
    <Drawer
      sx={{
        '& .MuiDrawer-paper': {
          height: '100%',
          width: '100%',
        },
      }}
      anchor="bottom"
      open={open}
      onClose={handleClose}
    >
      <MainLayout buttonProps={{ addButton: false }}>
        <div className="pt-6" />
        <MemberInnerPageHeader
          title="Members"
          fullName="Add Member"
          updateLabel="Select Tags"
          add={false}
          backRoute="/members"
          btnName="Save"
          callback={handleClose}
          rightSide={(
            <div className="flex flex-row gap-2 items-center">
              <Button
                onClick={handleClose}
                component="label"
                className="rounded-md m-0 p-0 px-5 h-[2.75rem] bg-[#F7F8F8] hover:bg-[#f0f0f0] text-[#E42B57] mr-3 min-w-[100px] transition-none"
              >
                <Close style={{ fontSize: 18, marginRight: 8 }} />
                Cancel
              </Button>
              <Button
                onClick={saveTags}
                style={{
                  background: 'linear-gradient(to bottom, #FD8205, #E97100)',
                  minWidth: '100px',
                }}
                variant="contained"
                className="rounded-md h-11 p-3 font-normal text-sm"
              >
                <Check style={{ fontSize: 18, marginRight: 8 }} />
                <span className="text-center font-semibold">Save</span>
              </Button>
            </div>
          )}
        />
        <div className="pl-5">
          <p className="text-lg font-[500]">Tag Library</p>
          {memberTags?.map((tag) => (
            // eslint-disable-next-line jsx-a11y/label-has-associated-control
            <label className="grid grid-flow-col auto-cols-max items-center">
              <Checkbox
                checked={tags.some((x) => x.key === tag.key)} // Use `.some()` for clarity
                key={tag.key}
                name={tag.name}
                onChange={() => handleChange(tag.key, tag.title, tag.color)}
              />
              <p className="font-[500] text-sm m-0">{tag.title}</p>
            </label>
          ))}
        </div>
      </MainLayout>
    </Drawer>
  );
}

export default MemberTagsDrawer;
