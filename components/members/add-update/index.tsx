/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/label-has-associated-control */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { TextField, Checkbox, Button } from '@mui/material';
import { MEMBERS } from 'api/src/routes/members';
import DateSelector from 'components/date-selector/date-selector';
import InnerPageHeader from 'components/inner-page-header';
import MemberTagsDrawer from 'components/members/add-update/member-tags-drawer';
import ProfilePicturePicker from 'components/profile-picture-picker/profile-picture-picker';
import SelectBox from 'components/select-box/select-box';
import CustomTable from 'components/table-components/table';
import { useModal } from 'contexts/modal-context/modal-context';
import useAddMemberLogic from 'logics/member/use-form-handler';
import React, { useEffect, useState } from 'react';
import { MemberStatusProps } from 'types/member-status-types';
import { Member, MemberTags } from 'types/members-response-type';
import { getLatestImage, isEmptyObject } from 'utils/helper';

interface Props {
  member: Member;
  status: MemberStatusProps[];
  add: boolean;
  noRefreshOnSubmit?: boolean;
  handleClose?: () => void;
  setSelectedMembers?: React.Dispatch<React.SetStateAction<Member[]>>;
}

function MembersAddUpdate({
  member,
  add,
  status,
  noRefreshOnSubmit,
  handleClose,
  setSelectedMembers = () => {},
}: Props) {
  const {
    formik,
    picture,
    setPicture,
    constants,
    setDeleteStatus,
    removeFromProgram,
  } = useAddMemberLogic(add, member?.id, noRefreshOnSubmit);
  const { showModal } = useModal();
  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    formik.setFieldValue(e.target.name, e.target.value);

    if (e.target.name === 'type' && e.target.value !== 'youth') {
      formik.setFieldValue('school', 'no');
    }
  };
  const isWithdrawn = () => formik.values.status?.includes('withdrawn');
  const handleStatusChange = async (
    key: string,
    value: any,
    condition?: boolean,
    selectedStatus?: MemberStatusProps,
  ) => {
    if (add) {
      await formik.setFieldValue(key, value);
    }
    if (condition) {
      showModal(
        'Warning',
        'Are you sure you want to remove the status from member.',
        () => {
          formik.setFieldValue(key, value);
          setDeleteStatus((prev) => [...prev, selectedStatus?.id as string]);
        },
      );
      return;
    }
    if (selectedStatus) {
      setDeleteStatus((prev) => prev.filter((x) => x !== selectedStatus.id));
    }
    await formik.setFieldValue(key, value);
  };
  const [openTagSelect, setOpenTagSelect] = useState(false);
  const [memberTags, setMemberTags] = useState<MemberTags[]>(
    formik.values.tags as MemberTags[],
  );
  useEffect(() => {
    if (!isEmptyObject(formik.errors)) {
      showModal(
        'Missing Fields',
        `
            ${Object.keys(formik.errors).map(
    (key) => `${(formik as any).errors[key]}`,
  )}
          `,
      );
    }
  }, [JSON.stringify(formik.errors)]);

  useEffect(() => {
    if (!isEmptyObject(member)) {
      const isHospital = status?.find((x) => x.kind === 'admission') || false;
      const isHomeles = status?.find((x) => x.kind === 'homeless') || false;
      let hospitalStatus: any = {};
      let homelessDetails: any = {};
      if (isHospital) {
        hospitalStatus = isHospital;
        hospitalStatus.address = isHospital.address[0] || {};
      }
      if (isHomeles) {
        homelessDetails = isHomeles;
        homelessDetails.address = isHomeles.address[0] || {};
      }
      formik.setValues({
        ...formik.values,
        phoneNumber:
          member?.phones?.length > 0
            ? ((member as any)?.phones[0]?.number as any)
            : '',
        firstName: member.firstName,
        lastName: member.lastName,
        email: member.email,
        lang: member.lang,
        referredBy: member.referredBy,
        dob: member.dob,
        type: member.type,
        ethnicity: member.ethnicity,
        school: member.schools?.length > 0 ? 'Yes' : 'No',
        schoolName: member.schools?.length > 0 ? member.schools[0].name : '',
        grade: member.schools?.length > 0 ? member.schools[0].grade : '',
        gender: member.gender || '',
        // eslint-disable-next-line max-len
        lastContact: member.lastContact,
        genderIdentity: member.genderIdentity || '',
        pronouns: member.pronouns || '',
        sexualIdentity: member.sexualIdentity || '',
        status: member.status,
        enrolledOn: member.enrolledOn,
        unenrolledDate: member.unenrolledDate,
        pregnancyStatus: member.pregnancyStatus as any,
        deliveryDate: member.deliveryDate,
        military: member.military,
        tags: member.tags as MemberTags[],
        homeless: !!member.homeless,
        emergencyContact: {
          type: member?.meta?.contacts && member?.meta?.contacts[0].type,
          name: member?.meta?.contacts && member?.meta?.contacts[0].name,
          phone: member?.meta?.contacts && member?.meta?.contacts[0].phone,
        },
        homelessDetails: isHomeles
          ? homelessDetails
          : formik.values.homelessDetails,
        tmp: {
          ...formik.values.tmp,
          isHospital: !!isHospital,
          isPregnant:
            member.pregnancyStatus !== null
            && member.pregnancyStatus !== 'not_pregnant',
          currentStatus: member.status,
        },
        hospitalStatus: isHospital
          ? hospitalStatus
          : formik.values.hospitalStatus,
      });
      const attachment = getLatestImage(member?.attachments, 'profile');
      setPicture(attachment);
    }
  }, [member]);

  const handleSaveTags = (tags: Member['tags']) => {
    setMemberTags(tags);
    formik.setFieldValue('tags', tags);
  };

  const removeTag = async (tagId: string, tagName: string) => {
    if (tagId) {
      try {
        await MEMBERS.deleteMemberTag(tagId); // will throw if status >= 400

        // No error = request was successful
        formik.setFieldValue(
          'tags',
          memberTags.filter((tag) => tag.name !== tagName),
        );
      } catch (error) {
        console.error('Failed to remove tag:', error);
        // Optional: show UI feedback or retry
      }
    } else {
      formik.setFieldValue(
        'tags',
        memberTags.filter((tag) => tag.name !== tagName),
      );
    }
  };

  useEffect(() => {
    setMemberTags(formik.values.tags);
  }, [formik.values.tags]);

  const removeAllTags = async () => {
    const tagsWithIds = memberTags.filter((tag) => typeof tag.id === 'string');
    try {
      // Remove tags that exist on the server
      await Promise.all(tagsWithIds.map((tag) => removeTag(tag.id as string, tag.name)));

      // Remove client-only tags locally
      formik.setFieldValue('tags', []);
    } catch (error) {
      console.error('Failed to remove all tags:', error);
    }
  };

  return (
    <div className="pt-6">
      <InnerPageHeader
        title="Members"
        addLabel="Add Member"
        updateLabel="Update Member"
        add={add}
        backRoute="/members"
        btnName="Save"
        callback={async () => {
          const newMember = await formik.submitForm();
          if (!newMember || !newMember.id) return;

          if (noRefreshOnSubmit) {
            setSelectedMembers((prev) => [...prev, newMember]);
            handleClose?.();
          }
        }}
        className="z-10"
      />
      <div className="grid grid-cols-3 gap-3 px-5">
        <p className="text-lg font-[500] col-span-3">Member Details</p>

        <div className="col-span-3">
          <ProfilePicturePicker picture={picture} setPicture={setPicture} />
        </div>
        <div className="col-span-2 w-1/2">
          <SelectBox
            label="Member Type"
            keyVal="type"
            defaultValue={formik.values.type}
            onChange={handleChange}
            minWidth="100%"
            items={constants?.memberTypes}
          />
        </div>

        <div className="col-span-1" />
        <TextField
          name="firstName"
          onChange={formik.handleChange}
          placeholder="Enter First Name"
          value={formik.values.firstName}
          id="filled-basic"
          label="First Name"
          variant="filled"
        />
        <TextField
          name="lastName"
          value={formik.values.lastName}
          onChange={formik.handleChange}
          placeholder="Enter Last Name"
          id="filled-basic"
          label="Last Name"
          variant="filled"
        />
        <DateSelector
          defaultValue={formik.values.dob}
          pickerProps={
            {
              label: 'Date of Birth',
            } as any
          }
          onChange={handleChange}
        />
        {formik.values.type === 'youth' && (
          <SelectBox
            onChange={handleChange}
            defaultValue={formik.values.school.toLowerCase()}
            label="School"
            keyVal="school"
            items={[
              { key: 'yes', title: 'Yes' },
              { key: 'no', title: 'No' },
            ]}
          />
        )}
        {formik.values.school.toLowerCase() === 'yes' && (
          <>
            <TextField
              onChange={handleChange}
              value={formik.values.schoolName}
              name="schoolName"
              placeholder="Enter School Name"
              id="filled-basic"
              label="School Name"
              variant="filled"
            />
            <TextField
              onChange={handleChange}
              value={formik.values.grade}
              name="grade"
              placeholder="Enter Grade"
              id="filled-basic"
              label="Grade"
              variant="filled"
            />
          </>
        )}
        <SelectBox
          onChange={handleChange}
          defaultValue={formik.values.gender}
          label="Gender"
          keyVal="gender"
          items={(constants?.genderIndentity as any) || []}
        />
        <SelectBox
          onChange={handleChange}
          defaultValue={formik.values.genderIdentity}
          label="Gender Identity"
          keyVal="genderIdentity"
          items={(constants?.genderIndentity as any) || []}
        />
        <SelectBox
          onChange={handleChange}
          defaultValue={formik.values.pronouns}
          label="Pronouns"
          keyVal="pronouns"
          items={(constants?.pronouns as any) || []}
        />
        <SelectBox
          onChange={handleChange}
          defaultValue={formik.values.sexualIdentity}
          label="Sexual Identity"
          keyVal="sexualIdentity"
          items={(constants?.sexualIdentities as any) || []}
        />

        <SelectBox
          onChange={handleChange}
          defaultValue={formik.values.ethnicity}
          label="Ethnicity"
          keyVal="ethnicity"
          items={(constants?.ethnicities as any) || []}
        />
        <SelectBox
          onChange={handleChange}
          defaultValue={formik.values.lang}
          label="Language"
          keyVal="lang"
          items={(constants?.langauges as any) || []}
        />
        <SelectBox
          onChange={handleChange}
          defaultValue={formik.values.military}
          label="Military"
          keyVal="military"
          items={(constants?.militaryStatus as any) || []}
        />

        <TextField
          onChange={handleChange}
          value={formik.values.email}
          name="email"
          placeholder="Enter Email"
          id="filled-basic"
          label="Email"
          variant="filled"
        />
        <TextField
          onChange={handleChange}
          value={formik.values.phoneNumber}
          name="phoneNumber"
          placeholder="Enter Phone Number"
          id="filled-basic"
          label="Phone Number"
          variant="filled"
        />
        <DateSelector
          time
          key={new Date().getTime()}
          defaultValue={formik.values.lastContact}
          name="lastContact"
          pickerProps={
            {
              label: 'Last Contact',
              slotProps: {
                field: {
                  clearable: true,
                  onClear: () => {
                    formik.setFieldValue('tmp.date', null);
                  },
                },
              },
            } as any
          }
          onChange={handleChange}
        />
        <p className="text-lg font-[500] col-span-3 mt-6">Emergency Contact</p>
        <div className="col-span-3 grid grid-cols-3 gap-3 items-center">
          <SelectBox
            onChange={handleChange}
            defaultValue={formik.values.emergencyContact.type}
            label="Type"
            keyVal="emergencyContact.type"
            items={(constants?.relationship as any) || []}
          />
          <TextField
            onChange={handleChange}
            value={formik.values.emergencyContact.name}
            name="emergencyContact.name"
            placeholder="Enter Name"
            id="filled-basic"
            label="Name"
            variant="filled"
          />
          <TextField
            onChange={handleChange}
            value={formik.values.emergencyContact.phone}
            name="emergencyContact.phone"
            placeholder="Enter Phone Number"
            id="filled-basic"
            label="Phone Number"
            variant="filled"
          />
        </div>
        <div className="col-span-3">
          <div className="flex justify-between mt-6">
            <p className="text-lg font-[500]">Member Tags</p>
            <div className="flex gap-6">
              <p
                className="text-sm font-[500] text-[15px] text-[#E97100] flex items-center cursor-pointer hover:text-[#F59C4B]"
                onClick={() => setOpenTagSelect(true)}
              >
                Select Tag(s)
              </p>
              {memberTags && memberTags.length > 0 && (
                <p
                  className="text-sm font-[500] text-[15px] text-[#E42B57] flex items-center cursor-pointer hover:text-[#F77C99]"
                  onClick={removeAllTags}
                >
                  Remove All
                </p>
              )}
            </div>
          </div>
          {memberTags && memberTags.length > 0 ? (
            <CustomTable
              variant="member-tags-table"
              source={memberTags}
              style={{
                border: 'solid 1px lightgray',
                borderRadius: '10px',
                borderBottom: 'none',
              }}
              headCellStyle={{
                padding: '4px 16px',
                fontSize: '13px',
                color: '#747A7A',
              }}
              tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
              handleDelete={(tag) => removeTag(tag.id, tag.name)}
            />
          ) : (
            <p className="text-[13px] text-gray-500 italic m-0">
              No tags associated with this member.
            </p>
          )}
        </div>
        <p className="text-lg font-[500] col-span-3 mt-6">Member Status</p>
        <div className="col-span-3 grid grid-cols-3 items-center gap-3">
          <SelectBox
            label="Status"
            keyVal="status"
            defaultValue={formik.values.status}
            onChange={handleChange}
            minWidth="100%"
            items={constants?.staffStatus}
          />
          <DateSelector
            defaultValue={
              !isWithdrawn()
                ? formik.values.enrolledOn
                : formik.values.unenrolledDate
            }
            name={!isWithdrawn() ? 'enrolledOn' : 'unenrolledDate'}
            pickerProps={
              {
                label: `${
                  !isWithdrawn() ? 'Enrolled Date' : 'Unenrolled Date'
                }` as unknown as string,
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
              } as unknown as any
            }
            onChange={handleChange}
          />
          <TextField
            onChange={handleChange}
            value={formik.values.referredBy}
            name="referredBy"
            placeholder="Enter Name"
            id="filled-basic"
            label="Referred By (enter name)"
            variant="filled"
          />
        </div>
        <div className="col-span-3 borderLine">
          <label className="grid grid-flow-col auto-cols-max items-center">
            <Checkbox
              checked={formik.values.homeless}
              title="Currently Homeless"
              onChange={() => handleStatusChange(
                'homeless',
                !formik.values.homeless,
                !!formik.values.homeless
                    && status.filter((x) => x.kind === 'homeless').length > 0,
                status.find((x) => x.kind === 'homeless'),
              )}
              name="homeless"
            />
            <p className="font-[500] text-sm m-0">Currently Homeless</p>
          </label>
          {formik.values.homeless && (
            <div className="grid grid-cols-3 items-center gap-2">
              <div className="col-span-3 w-1/3">
                <SelectBox
                  label="Homeless Status"
                  keyVal="homelessDetails.state"
                  defaultValue={formik.values.homelessDetails.state}
                  onChange={handleChange}
                  minWidth="100%"
                  items={constants?.homeless}
                />
              </div>
              {formik.values.homelessDetails.state === 'homeless_shelter' && (
                <div className="col-span-3 grid grid-cols-3 gap-3 items-center">
                  <TextField
                    onChange={handleChange}
                    value={formik.values.homelessDetails.name}
                    name="homelessDetails.name"
                    placeholder="Enter Shelter Name"
                    id="filled-basic"
                    label="Shelter Name"
                    variant="filled"
                  />
                  <TextField
                    onChange={handleChange}
                    value={formik.values.homelessDetails.address.street}
                    name="homelessDetails.address.street"
                    placeholder="Enter Street 1"
                    id="filled-basic"
                    label="Street 1"
                    variant="filled"
                  />
                  <TextField
                    onChange={handleChange}
                    value={formik.values.homelessDetails.address.city}
                    name="homelessDetails.address.city"
                    placeholder="Enter City"
                    id="filled-basic"
                    label="City"
                    variant="filled"
                  />
                  <SelectBox
                    onChange={handleChange}
                    defaultValue={formik.values.homelessDetails.address.state}
                    label="State"
                    keyVal="homelessDetails.address.state"
                    items={(constants?.states as any) || []}
                  />
                  <TextField
                    onChange={handleChange}
                    value={formik.values.homelessDetails.address.zip}
                    name="homelessDetails.address.zip"
                    placeholder="Enter Zip Code"
                    id="filled-basic"
                    label="Zip Code"
                    variant="filled"
                  />
                </div>
              )}
            </div>
          )}
        </div>
        <div className="col-span-3 borderLine">
          <label className="grid grid-flow-col auto-cols-max items-center">
            <Checkbox
              checked={formik.values.tmp.isPregnant}
              title="Pregnancy Program"
              onChange={() => {
                handleStatusChange(
                  'tmp.isPregnant',
                  !formik.values.tmp.isPregnant,
                  !!formik.values.tmp.isPregnant
                    && status.filter((x) => x.kind === 'pregnancy').length > 0,
                  status.find((x) => x.kind === 'pregnancy'),
                );
              }}
              name="isPregnant"
            />
            <p className="font-[500] text-sm m-0">Pregnancy Program</p>
          </label>
          {formik.values.tmp.isPregnant && (
            <div className="grid grid-cols-3 items-center gap-2">
              <SelectBox
                onChange={handleChange}
                defaultValue={formik.values.pregnancyStatus as any}
                label="Pregnancy"
                keyVal="pregnancyStatus"
                items={
                  (constants?.pregnancyStatus.filter(
                    (x) => x.key !== 'not_pregnant',
                  ) as any) || []
                }
              />
              <DateSelector
                defaultValue={formik.values.deliveryDate}
                name="deliveryDate"
                pickerProps={
                  {
                    label: 'Delivery Date',
                  } as any
                }
                onChange={handleChange}
              />
            </div>
          )}
        </div>
        <div className="col-span-3 borderLine">
          <label className="grid grid-flow-col auto-cols-max items-center">
            <Checkbox
              checked={formik.values.tmp.isHospital}
              title="Hospitalization"
              onChange={() => handleStatusChange(
                'tmp.isHospital',
                !formik.values.tmp.isHospital,
                !!formik.values.tmp.isHospital
                    && status.filter((x) => x.kind === 'admission').length > 0,
                status.find((x) => x.kind === 'admission'),
              )}
              name="isHospital"
            />
            <p className="font-[500] text-sm m-0">Hospitalization</p>
          </label>
          {formik.values.tmp.isHospital && (
            <div className="col-span-3 grid grid-cols-3 gap-3 items-center">
              <SelectBox
                onChange={handleChange}
                defaultValue={formik.values.hospitalStatus.state}
                label="Hospitalization Status"
                keyVal="hospitalStatus.state"
                items={constants?.hospitalization || []}
              />
              <DateSelector
                defaultValue={formik.values.hospitalStatus.startDate}
                name="hospitalStatus.startDate"
                pickerProps={
                  {
                    label: 'Admittance Date',
                  } as any
                }
                onChange={handleChange}
              />
              {status.find((x) => x.kind === 'admission') && (
                <Button
                  variant="outlined"
                  onClick={() => removeFromProgram(
                      status.find((x) => x.kind === 'admission')?.id as string,
                  )}
                  className="rounded-md h-11 p-3 border-primary font-normal text-sm"
                  type="button"
                >
                  <span className="text-center text-primary font-semibold pl-3">
                    Remove From Program
                  </span>
                </Button>
              )}
              {formik.values.hospitalStatus.state !== 'discharged' && (
                <div className="col-span-3 grid grid-cols-3 gap-3 items-center">
                  <TextField
                    onChange={handleChange}
                    value={formik.values.hospitalStatus.name}
                    name="hospitalStatus.name"
                    placeholder="Facility Title"
                    id="filled-basic"
                    label="Facility Title"
                    variant="filled"
                  />
                  <TextField
                    onChange={handleChange}
                    value={formik.values.hospitalStatus.address.street}
                    name="hospitalStatus.address.street"
                    placeholder="Enter Street 1"
                    id="filled-basic"
                    label="Street 1"
                    variant="filled"
                  />
                  <TextField
                    onChange={handleChange}
                    value={formik.values.hospitalStatus.address.city}
                    name="hospitalStatus.address.city"
                    placeholder="Enter City"
                    id="filled-basic"
                    label="City"
                    variant="filled"
                  />
                  <SelectBox
                    onChange={handleChange}
                    defaultValue={formik.values.hospitalStatus.address.state}
                    label="State"
                    keyVal="hospitalStatus.address.state"
                    items={(constants?.states as any) || []}
                  />
                  <TextField
                    onChange={handleChange}
                    value={formik.values.hospitalStatus.address.zip}
                    name="hospitalStatus.address.zip"
                    placeholder="Enter Zip Code"
                    id="filled-basic"
                    label="Zip Code"
                    variant="filled"
                  />
                </div>
              )}
            </div>
          )}
        </div>
        <MemberTagsDrawer
          open={openTagSelect}
          setOpen={setOpenTagSelect}
          handleSave={handleSaveTags}
          existingTags={memberTags || []}
        />
      </div>
    </div>
  );
}

export default MembersAddUpdate;
