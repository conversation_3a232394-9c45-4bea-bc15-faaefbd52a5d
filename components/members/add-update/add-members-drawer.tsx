/* eslint-disable @typescript-eslint/no-explicit-any */
import React from 'react';
import { Drawer } from '@mui/material';
import { isEmptyObject } from 'utils/helper';
import { Member } from 'types/members-response-type';
import MembersAddUpdate from 'components/members/add-update';
import ClosablePageWrapper from 'components/closable-page-wrapper';
import { MemberStatusProps } from 'types/member-status-types';

interface AddMemberDrawerProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setMember: React.Dispatch<React.SetStateAction<Member>>;
  // eslint-disable-next-line react/no-unused-prop-types
  setSelectedMembers?: React.Dispatch<React.SetStateAction<Member[]>>;
  member: Member;
  status: MemberStatusProps[];
}

function AddMemberDrawer({
  open,
  member,
  setOpen,
  setMember = () => false,
  status,
  setSelectedMembers = () => false,
}: AddMemberDrawerProps) {
  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Drawer
      sx={{
        '& .MuiDrawer-paper': {
          height: '100%',
          width: '100%',
        },
      }}
      anchor="bottom"
      open={open}
      onClose={handleClose}
    >
      <div className="w-screen bg-white pb-32">
        <ClosablePageWrapper onClose={handleClose}>
          <h1 className="font-[500] text-3xl">
            {isEmptyObject(member) ? 'Add Member' : 'Update Member'}
          </h1>
        </ClosablePageWrapper>
        <div className="p-20 mt-5">
          <MembersAddUpdate
            status={status}
            member={member}
            add={isEmptyObject(member)}
            noRefreshOnSubmit
            handleClose={handleClose}
            setSelectedMembers={setSelectedMembers}
          />
        </div>
      </div>
    </Drawer>
  );
}

export default AddMemberDrawer;
