import ViewCarePlanItem from 'components/members/care-plans/view/ViewCarePlanItem';
import { capitalizeWords } from 'utils/helper';

function ViewCarePlanSection({
  section,
  setOpenAddItem,
  handleClick,
  handleDelete,
}: {
  section: any;
  setOpenAddItem: any;
  handleClick: (type: string, id: string) => void;
  handleDelete: (carePlanID: string, type: string, id: string) => void;
}) {
  const formattedTitle = () => {
    if (section.title === 'followups') {
      return 'Follow-ups';
    }
    if (section.title === 'services') {
      return 'Network Appointments';
    }
    return section.title;
  };
  const singularTitle = formattedTitle().replace(/s$/, '');
  return (
    <div className="pt-[40px]">
      <div className="flex items-center mb-3 justify-between">
        <p className="font-semibold text-[24px] text-dark-text m-0 p-0">
          {capitalizeWords(formattedTitle(), true)}
        </p>
        {section.items?.length > 0 && (
          <button
            type="button"
            onClick={() => {
              setOpenAddItem(section.title);
            }}
            className="text-primary font-[500] text-[15px] cursor-pointer hover:text-primary/80 m-0 p-0 border-0 bg-transparent pb-[2px]"
          >
            + Add
            {' '}
            {capitalizeWords(
              singularTitle === 'Network Appointment'
                ? 'Appointment'
                : singularTitle,
            )}
          </button>
        )}
      </div>
      {section.items?.length === 0 ? (
        <div className="flex items-center gap-2">
          <p className="text-[15px] m-0 p-0">
            No
            {' '}
            {section.title}
            {' '}
            {section.title === 'Team' ? 'has' : 'have'}
            {' '}
            been
            added to this care plan.
          </p>
          <button
            type="button"
            onClick={() => {
              setOpenAddItem(true);
            }}
            className="text-primary font-[500] text-[15px] cursor-pointer hover:text-primary/80 m-0 p-0 border-0 bg-transparent pb-[2px]"
          >
            + Add
            {' '}
            {capitalizeWords(singularTitle)}
          </button>
        </div>
      ) : (
        <div className="border-all rounded-md">
          {section.items.map((item: any, index: number) => (
            <ViewCarePlanItem
              key={item.id}
              item={item}
              index={index}
              length={section.items.length}
              type={section.title}
              handleClick={(type: string, id: string) => handleClick(type, id)}
              // eslint-disable-next-line max-len
              handleDelete={(carePlanID: string, type: string, id: string) => handleDelete(carePlanID, type, id)}
            />
          ))}
        </div>
      )}
    </div>
  );
}

export default ViewCarePlanSection;
