import { Button } from '@mui/material';
import { capitalizeWords } from 'utils/helper';
import EditIcon from 'public/icons/EditIcon';

function CarePlanItemStatus({
  type,
  status,
}: {
  type: string;
  status: string;
}) {
  const typeTitle = capitalizeWords((type as string)?.replace(/s$/, '') || '');
  const statusTitlesColors = {
    active: { title: 'Active', color: '#008390' },
    complete: { title: 'Complete', color: '#6E6E6E' },
    in_progress: { title: 'In Progress', color: '#E97100' },
  };
  return (
    <>
      <div className="flex items-center mt-7 justify-between">
        <p className="font-[600] text-[18px] m-0 p-0">
          {typeTitle}
          {' '}
          Status
        </p>
        <Button
          component="label"
          className="rounded-md m-0 text-[15px] text-[#008390] grey-btn"
          sx={{ padding: '11px 15px !important' }}
          onClick={() => {}}
        >
          <EditIcon stroke="#008390" size={18} className="mr-2" />
          Update Status
        </Button>
      </div>
      <div className="mt-6">
        <p className="m-0 p-0 font-[400] text-[15px] text-[#747A7A]">Status</p>
        <p
          className="font-[500] text-[15px] m-0 p-0"
          style={{
            color:
              statusTitlesColors[status as keyof typeof statusTitlesColors]
                .color || '#6E6E6E',
          }}
        >
          {statusTitlesColors[status as keyof typeof statusTitlesColors]?.title || ''}
        </p>
      </div>
    </>
  );
}

export default CarePlanItemStatus;
