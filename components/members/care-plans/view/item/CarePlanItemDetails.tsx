/* eslint-disable react/no-array-index-key */
/* eslint-disable no-nested-ternary */
import { capitalizeWords } from 'utils/helper';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useContext } from 'react';
import SafeDate from 'components/date-time/SafeDate';

function CarePlanItemDetails({ type, item }: { type: string; item: any }) {
  const constant = useContext(ConstantsContext);
  const typeTitle = capitalizeWords((type as string)?.replace(/s$/, '') || '');

  const detailsByType = {
    goals: [
      { label: 'Title', value: item?.title, type: 'text' },
      {
        label: 'Goal Type',
        value: constant?.goalTypes.find((g: any) => g.key === item?.type)
          ?.title,
        type: 'text',
      },
      { label: 'Target Goal Date', value: item?.targetDate, type: 'date' },
      { label: 'Goal Objective', value: item?.objective, type: 'textarea' },
      {
        label: 'Measurement Criteria',
        value: item?.measurementCriteria,
        type: 'textarea',
      },
      {
        label: 'Achievability Note',
        value: item?.achievabilityNote,
        type: 'textarea',
      },
      { label: 'Barriers', value: item?.barriers, type: 'textarea' },
    ],
    interventions: [
      { label: 'Title', value: item?.title, type: 'text' },
      {
        label: 'Responsible Party',
        value: item?.responsibleParty,
        type: 'text',
      },
      { label: 'Target Date', value: item?.dueDate, type: 'date' },
      { label: 'Goal', value: item?.action, type: 'text' },
      { label: 'Intervention Notes', value: item?.note, type: 'textarea' },
    ],
    reviews: [
      { label: 'Title', value: item?.title, type: 'text' },
      { label: 'Review Date', value: item?.reviewDate, type: 'date' },
      { label: 'Review Note', value: item?.notes, type: 'textarea' },
    ],
    followups: [
      {
        label: 'Type',
        value: constant?.followUpTypes.find((g: any) => g.key === item?.type)
          ?.title,
        type: 'text',
      },
      { label: 'Follow-up Date', value: item?.datetime, type: 'date' },
      { label: 'Follow-up Outcome', value: item?.outcome, type: 'text' },
      { label: 'Follow-up Note', value: item?.notes, type: 'textarea' },
    ],
    services: [
      { label: 'Network', value: item?.cboName, type: 'text' },
      { label: 'Appointment Date', value: item?.appointmentDate, type: 'datetime' },
      { label: 'Provider', value: capitalizeWords(item?.staffName, true), type: 'text' },
      { label: 'Appointment Note', value: item?.appointmentNote, type: 'textarea' },
    ],
    notes: [
      { label: 'Title', value: item?.title, type: 'text' },
      { label: 'Date', value: item?.updatedAt, type: 'date' },
      { label: 'Author', value: `${item?.creator?.firstName} ${item?.creator?.lastName}`, type: 'text' },
      { label: 'Note', value: item?.msg, type: 'textarea' },
    ],
    problems: [
      { label: 'Title', value: item?.title, type: 'text' },
      { label: 'ICD Code', value: item?.icdCode?.toUpperCase(), type: 'text' },
      { label: 'Description', value: item?.description, type: 'textarea' },
    ],
  };

  const followUpOutcomesWithColors = [
    {
      key: 'engaged',
      title: 'Contacted and Engaged',
      color: '#008000',
    },
    {
      key: 'unreachable',
      title: 'Unable to Reach',
      color: '#FFA500',
    },
    {
      key: 'refused',
      title: 'Refused Follow-Up',
      color: '#E42B57',
    },
    {
      key: 'ineligible',
      title: 'No Longer Eligible',
      color: '#0000FF',
    },
    {
      key: 'completed',
      title: 'Follow-Up Completed',
      color: '#008000',
    },
    {
      key: 'referred',
      title: 'Referred to Other Services',
      color: '#0000FF',
    },
    {
      key: 'other',
      title: 'Other (Specify)',
      color: '#000000',
    },
  ];

  const currentDetails = detailsByType[type as keyof typeof detailsByType] || [];
  const rowFields = currentDetails.filter((d: any) => d.type !== 'textarea');
  const textAreas = currentDetails.filter((d: any) => d.type === 'textarea');

  return (
    <div className="mt-[55px]">
      <p className="font-[600] text-[18px] m-0 p-0">
        {typeTitle === 'Followup' ? 'Follow-up' : typeTitle}
        {' '}
        Details
      </p>

      {/* Non-textarea details in a responsive grid */}
      {rowFields.length > 0 && (
        <div className="mt-7 grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
          {rowFields.map((field: any, idx: number) => (
            <div key={idx}>
              <p className="m-0 p-0 font-[400] text-[15px] text-[#747A7A]">
                {field.label}
              </p>

              {field.type === 'date' || field.type === 'datetime' ? (
                field.value ? (
                  <SafeDate
                    date={field.value}
                    time={field.type === 'datetime'}
                    dash
                    showDaysFromCurrent={
                      type !== 'reviews' && type !== 'followups' && type !== 'notes' && field.type !== 'datetime'
                    }
                    relativeDaysColor="#E42B57"
                  />
                ) : (
                  <p className="font-[500] text-[15px] m-0 p-0">-</p>
                )
              ) : (
                <p
                  className="font-[500] text-[15px] m-0 p-0"
                  style={{
                    color:
                      type === 'followups'
                      && field.label === 'Follow-up Outcome'
                        ? followUpOutcomesWithColors.find(
                          (f) => f.key === field.value,
                        )?.color
                        : '#262D2D',
                  }}
                >
                  {field.label === 'ICD Code'
                    ? field.value?.toUpperCase()
                    : field.label === 'Network' || field.label === 'Author'
                      ? field.value
                      : capitalizeWords(field.value || '-')}
                </p>
              )}
            </div>
          ))}
        </div>
      )}

      {/* Textarea details full width */}
      {textAreas.length > 0 && (
        <div className="mt-6 flex flex-col gap-6">
          {textAreas.map((field: any, idx: number) => (
            <div
              key={idx}
              className={
                textAreas.length === 1 ? 'flex-grow flex flex-col' : ''
              }
            >
              <p className="m-0 p-0 font-[400] text-[15px] text-[#747A7A]">
                {field.label}
              </p>
              {type === 'problems' && field.value?.toLowerCase().startsWith('http') ? (
                <a
                  className="font-[500] text-[15px] m-0 p-0 whitespace-pre-line"
                  href={field.value}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  {field.value}
                </a>
              ) : (
                <p className="font-[500] text-[15px] m-0 p-0 whitespace-pre-line">
                  {capitalizeWords(field.value || '-')}
                </p>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default CarePlanItemDetails;
