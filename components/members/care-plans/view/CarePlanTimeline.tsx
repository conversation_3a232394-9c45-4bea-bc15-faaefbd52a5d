import { useEffect, useState } from 'react';
import { TimeLineResponse } from 'types/timeline-types';
import CarePlanTimelineItems from 'components/members/care-plans/view/CarePlanTimelineItems';
import { Member } from 'types/members-response-type';
import API from 'api/src/lib/api';
import { getLatestImage } from 'utils/helper';

interface CarePlanTimelineProps {
  member: Member;
  carePlan: any;
  timelines: TimeLineResponse[];
}

function CarePlanTimeline({
  member,
  carePlan,
  timelines,
}: CarePlanTimelineProps) {
  const [timelinesWithProfiles, setTimelinesWithProfiles] = useState<TimeLineResponse[]>([]);

  async function fetchCreatorsWithProfiles(creatorIds: string[]) {
    const uniqueIds = [...new Set(creatorIds)];

    const responses: any[] = await Promise.all(
      uniqueIds.map((id) => API.USER.getUser(id)),
    );

    return responses.reduce((acc, user, idx) => {
      const fullName = `${user.firstName || ''} ${user.lastName || ''}`.trim();
      acc[uniqueIds[idx]] = {
        profile: getLatestImage(user.attachments, 'userProfile') || '',
        name: fullName,
      };
      return acc;
    }, {} as Record<string, { profile: string; name: string }>);
  }

  useEffect(() => {
    if (!timelines || timelines.length === 0) return;

    const creatorIds = timelines.map((item) => item.creator.id);

    fetchCreatorsWithProfiles(creatorIds as string[]).then((creatorMap) => {
      const updatedTimelines = timelines.map((item) => {
        const creatorInfo = creatorMap[item.creator.id as string] || { profile: '', name: '' };
        return {
          ...item,
          creator: {
            ...item.creator,
            profile: creatorInfo.profile,
            name: creatorInfo.name,
          },
        };
      });
      setTimelinesWithProfiles(updatedTimelines);
    });
  }, [timelines]);

  return (
    <div>
      <div className="flex flex-row justify-between items-center py-7">
        <p className="font-[600] text-[18px] m-0 p-0">Plan History</p>
        {/* <Button variant="contained" color="primary" className="rounded-md">
          Filter / Export CSV buttons coming later - change py-7 above to py-5 when added
        </Button> */}
      </div>
      <CarePlanTimelineItems data={timelinesWithProfiles} />
    </div>
  );
}

export default CarePlanTimeline;
