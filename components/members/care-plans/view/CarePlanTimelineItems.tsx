import { useState } from 'react';
import { <PERSON><PERSON>, Button } from '@mui/material';
import { useRouter } from 'next/router';
import { dateFormat } from 'utils/helper';
import SafeDate from 'components/date-time/SafeDate';
import ActionContainer from 'components/action-container/action-container';

interface TimeLineItemsProps {
  data: any[];
  page?: boolean; // Initial state (preview or full)
}

function CarePlanTimelineItems({ data, page = false }: TimeLineItemsProps) {
  const [showAll, setShowAll] = useState(page); // internal page state
  const textColorByTimelineType = {
    updated: '#E97100',
    created: '#008390',
    deleted: '#E42B57',
  };
  const router = useRouter();

  function getFirstWord(text: string): string {
    if (!text) return '';
    return text.trim().split(/\s+/)[0];
  }

  const getThirdWord = (text: string) => text?.trim().split(/\s+/)[2] || '';

  function timelineItemType(desc: string): string {
    let singularType = getFirstWord(desc).toLowerCase();
    if (singularType === 'care') singularType = getThirdWord(desc).toLowerCase();
    if (singularType === 'follow-up') singularType = 'followup';
    const pluralize = (word: string) => (word.endsWith('s') ? word : `${word}s`);
    return pluralize(singularType);
  }

  // Limit items when not on the "full page" view
  const itemsToRender = showAll ? data : data.slice(0, 10);

  return (
    <div>
      {itemsToRender.length > 0 ? (
        itemsToRender.map((item, index) => {
          const month = dateFormat(item.updatedAt).slice(0, 3);
          const year = dateFormat(item.updatedAt).slice(-4);
          const day = dateFormat(item.updatedAt).slice(4, 6).replace(/[^0-9]/g, '');
          return (
            <div key={item.id} className="flex flex-row gap-3 items-center mt-4 justify-between">
              <div className="flex flex-row gap-3 items-start flex-1 min-w-0">
                <div className="bg-[#F7F8F8] m-0 relative border-white border rounded-full w-12 h-12 flex flex-col justify-center items-center shrink-0">
                  <p className="text-xs text-center font-semibold m-0 p-0">
                    {month}
                    {' '}
                    {day}
                  </p>
                  <p className="text-xs font-normal text-grey m-0 p-0">{year}</p>
                  {index !== itemsToRender.length - 1 && (
                    <div className="absolute bg-[#DCDFDF] h-4 top-14 w-1" />
                  )}
                </div>
                <div className="flex flex-col min-w-0">
                  <p
                    className="mb-0 text-sm font-semibold m-0 p-0 break-words"
                    style={{
                      color:
                        textColorByTimelineType[
                          item?.meta?.data?.operation as keyof typeof textColorByTimelineType
                        ],
                    }}
                  >
                    {item.title}
                  </p>
                  <p className="mb-0 text-sm font-normal m-0 p-0 break-words">{item.desc}</p>
                </div>
              </div>
              <div className="flex flex-row items-center gap-1 shrink-0 pl-2">
                <p className="text-sm font-normal m-0 p-0 whitespace-nowrap">{item.creator.name}</p>
                <p className="text-sm font-normal m-0 p-0">-</p>
                <p className="text-sm font-normal m-0 p-0 whitespace-nowrap">
                  <SafeDate date={item.updatedAt} time dash />
                </p>
                <Avatar src={item.creator.profile} alt="profile" className="w-6 h-6 ml-2 mr-4" />
                {item.meta?.data?.operation === 'created' ? (
                  <ActionContainer
                    editLabel="View Details"
                    onEdit={() => {
                      router.push(
                        `/members/care-plans/${item.memberId}/${item.carePlan.id}/${timelineItemType(
                          item.desc,
                        )}/${item?.meta?.data?.ref_id}`,
                      );
                    }}
                  />
                ) : (
                  <div className="px-3" />
                )}
              </div>
            </div>
          );
        })
      ) : (
        <p className="grid justify-center self-center italic text-sm text-dark-text p-14">
          No data available for timeline
        </p>
      )}

      {/* Toggle "View All" */}
      {!showAll && data.length > 10 && (
        <Button
          onClick={() => setShowAll(true)} // <-- Change to true
          variant="outlined"
          fullWidth
          color="primary"
          className="rounded-md w-full mt-5"
        >
          View All Timeline Items
        </Button>
      )}
    </div>
  );
}

export default CarePlanTimelineItems;
