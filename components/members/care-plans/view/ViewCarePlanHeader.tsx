import SafeDate from 'components/date-time/SafeDate';
import { Ava<PERSON>, Button } from '@mui/material';
import { capitalizeWords } from 'utils/helper';
import { useModal } from 'contexts/modal-context/modal-context';
import API from 'api/src/lib/api';
import { useState } from 'react';
import EditableTitle from 'components/common/EditableTitle';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useRouter } from 'next/router';

function ViewCarePlanHeader({
  carePlan,
  member,
  team,
}: {
  carePlan: any;
  member: any;
  team: any;
}) {
  const { refresh } = useRefreshSSR();
  const router = useRouter();
  const [title, setTitle] = useState(
    carePlan.title || `${capitalizeWords(member.firstName)}'s Care Plan`,
  );
  
  const statusColor = carePlan.status === 'active' ? '#008390' : 'dark-text';
  const { showModal } = useModal();

  const handleSaveTitle = async (newTitle: string) => {
    try {
      await API.CAREPLANS.updateCarePlan(carePlan.id, {
        ...carePlan,
        title: newTitle,
      });
      setTitle(newTitle);
    } catch (err) {
      console.error('Failed to update care plan title:', err);
    }
    refresh();
  };

  return (
    <>
      <div className="w-full flex justify-between items-center mt-[36px]">
        {/* Editable Title */}
        <EditableTitle
          initialTitle={title}
          onSave={handleSaveTitle}
        />

        {/* End Plan Button */}
        {carePlan.status !== 'complete' && (
          <Button
            component="label"
            className="rounded-md m-0 text-[15px] text-[#E42B57] grey-btn"
            sx={{ padding: '11px 15px !important' }}
            onClick={() => {
              showModal('Warning', 'Are you sure to end this care plan?', async () => {
                await API.CAREPLANS.updateCarePlan(carePlan.id, {
                  status: 'complete',
                  title: `${carePlan.title}`,
                  startDate: carePlan.startDate,
                });
                router.push(`/members/care-plans/${member.id}`);
              });
            }}
          >
            End Plan
          </Button>
        )}
      </div>

      {/* Header Details */}
      <div className="flex flex-wrap mt-[6px] border-bottom pb-[18px] items-center">
        <div className="flex items-center">
          <p className="p-0 m-0 mr-1">Start Date:</p>
          <p className="p-0 m-0 font-semibold text-dark-text">
            <SafeDate date={carePlan.startDate} />
          </p>
          <p className="p-0 m-0 mx-4 text-[#DCDFDF]">|</p>
        </div>
        <div className="flex items-center">
          <p className="p-0 m-0 mr-1">Status:</p>
          <p className={`p-0 m-0 font-semibold text-[${statusColor}]`}>
            {capitalizeWords(carePlan.status)}
          </p>
          <p className="p-0 m-0 mx-4 text-[#DCDFDF]">|</p>
        </div>
        <div className="flex items-center">
          <p className="p-0 m-0 mr-1">Team:</p>
          <div className="p-0 m-0 font-semibold text-dark-text flex gap-1 ml-2">
            {team.map((user: any) => (
              <Avatar
                key={user.id}
                src={user.avatar}
                sx={{ width: 26, height: 26 }}
              />
            ))}
          </div>
        </div>
      </div>
    </>
  );
}

export default ViewCarePlanHeader;
