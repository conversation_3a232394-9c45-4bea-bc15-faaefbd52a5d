import React, { useContext, useEffect, useState } from 'react';
import ActionContainer from 'components/action-container/action-container';
import SafeDate from 'components/date-time/SafeDate';
import API from 'api/src/lib/api';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { capitalizeWords, getLatestImage } from 'utils/helper';
import router from 'next/router';
import { Avatar } from '@mui/material';

interface DetailItem {
  fieldType: string;
  detail: any;
  showDaysFromCurrent?: boolean;
  capitalize?: boolean;
  handleViewDetails?: (item: any) => void;
}

function ViewCarePlanItem({
  item,
  index,
  length,
  type,
  handleClick,
  handleDelete,
}: {
  item: any;
  index: number;
  length: number;
  type: string;
  handleClick: (itemType: string, id: string) => void;
  handleDelete: (carePlanID: string, type: string, id: string) => void;
}) {
  const constant = useContext(ConstantsContext);
  const [userData, setUserData] = useState<any>(null);

  useEffect(() => {
    if (type === 'notes' && item?.creator?.id) {
      API.USER.getUser(item.creator.id).then((res) => {                
        setUserData(res);
      });
    }
  }, [item?.creator?.id, type]);

  function setTitleByType() {
    switch (type) {
      case 'followups':
        return capitalizeWords(
          constant?.followUpTypes?.find((t) => t.key === item.type)?.title || '',
        );
      case 'services':
        return capitalizeWords(item.cboName, true);
      default:
        return capitalizeWords(item.title);
    }
  }

  function setDetailsByType() {
    switch (type) {
      case 'goals':
        return [
          {
            fieldType: 'type',
            detail:
              constant?.goalTypes?.find((t) => t.key === item.type)?.title
              || '',
          },
          { fieldType: 'date', detail: item.targetDate },
          { fieldType: 'status', detail: item.status },
        ];
      case 'interventions':
        return [
          { fieldType: 'text', detail: item.action },
          { fieldType: 'date', detail: item.dueDate },
          { fieldType: 'status', detail: item.status || 'active' },
        ];
      case 'reviews':
        return [
          { fieldType: 'name', detail: item.reviewerName },
          { fieldType: 'date', detail: item.reviewDate },
          { fieldType: 'text', detail: item.notes },
        ];
      case 'followups':
        return [
          { fieldType: 'name', detail: item.staffName },
          { fieldType: 'date', detail: item.datetime },
          {
            fieldType: 'status',
            detail:
              constant?.followUpOutcomes?.find((t) => t.key === item.outcome)
                ?.title || 'active',
          },
          { fieldType: 'text', detail: item.notes },
        ];
      case 'services':
        return [
          { fieldType: 'name', detail: item.staffName },
          { fieldType: 'datetime', detail: item.appointmentDate },
          { fieldType: 'status', detail: item.status || 'active' },
        ];
      case 'notes':
        return [
          { fieldType: 'user', detail: userData || item.creator },
          {
            fieldType: 'date',
            detail: item.createdAt,
            showDaysFromCurrent: false,
          },
          { fieldType: 'text', detail: item.msg },
        ];
      case 'problems':
        return [
          { fieldType: 'text', detail: item.icdCode || item.type?.title || item.type, capitalize: false },
          { fieldType: "text", 
            detail: item.clinicalNote || 
            (item.type === "diagnosis" ? item.status : item.description)
          },
        ];
      default:
        return [
          { fieldType: 'title', detail: item.title },
          { fieldType: 'type', detail: item.type },
          { fieldType: 'date', detail: item.targetDate },
        ];
    }
  }

  function statusColorByStatus(status: string) {
    switch (status?.toLowerCase()) {
      case 'active':
        return 'text-[#008390]';
      case 'completed':
        return 'text-[#008390]';
      case 'cancelled':
        return 'text-[#E42B57]';
      case 'pending':
        return 'text-[#E97100]';
      default:
        return 'text-[#E42B57]';
    }
  }

  const title = setTitleByType();
  const details = setDetailsByType();

  function renderDetailByFieldType(
    fieldType: string,
    detail: any,
    showDaysFromCurrent?: boolean,
    capitalize?: boolean,
  ) {
    switch (fieldType) {
      case 'text':
        return (
          <p className="p-0 m-0 text-dark-text">
            {capitalize ? capitalizeWords(detail) : detail}
          </p>
        );
      case 'name':
        return (
          <p className="p-0 m-0 text-dark-text">
            {capitalizeWords(detail, true)}
          </p>
        );
      case 'type':
        return (
          <p className="p-0 m-0 text-dark-text">{capitalizeWords(detail)}</p>
        );
      case 'date':
        return (
          <SafeDate
            date={detail}
            relativeDaysColor="#E42B57"
            showDaysFromCurrent={showDaysFromCurrent}
          />
        );
      case 'datetime':
        return (
          <SafeDate
            date={detail}
            showDaysFromCurrent
            relativeDaysColor="#E42B57"
            time
            dash
          />
        );
      case 'status':
        return (
          <p className="p-0 m-0 text-dark-text">
            Status:
            {' '}
            <span className={`font-[500] ${statusColorByStatus(detail)}`}>
              {capitalizeWords(detail)}
            </span>
          </p>
        );
      case 'user':
        if (!detail) {
          // fallback while loading
          return (
            <div className="flex items-center gap-2">
              <Avatar className="w-8 h-8">?</Avatar>
              <p className="p-0 m-0 text-dark-text">Loading...</p>
            </div>
          );
        }

        return (
          <div className="flex items-center gap-2">
            <Avatar
              onClick={() => router.replace('/profile/me')}
              className="cursor-pointer w-8 h-8"
              src={
                getLatestImage(detail?.attachments as any, 'userProfile') || ''
              }
            >
              {detail?.firstName?.[0] || '?'}
            </Avatar>
            <p className="p-0 m-0 text-dark-text">
              {capitalizeWords(
                `${detail?.firstName} ${detail?.lastName}`,
                true,
              ) || 'Unknown'}
            </p>
          </div>
        );
      default:
        return null;
    }
  }
  return (
    // eslint-disable-next-line jsx-a11y/click-events-have-key-events
    <div
      className={`pl-[30px] pt-[18px] pb-[14px] ${
        index === length - 1 ? '' : 'border-bottom'
      } cursor-pointer`}
      onClick={() => {
        handleClick(type, item.id);
      }}
      role="button"
      tabIndex={0}
    >
      <div className="flex items-center justify-between pr-5">
        <p className="m-0 p-0 text-[18px] font-[500] text-dark-text">{title}</p>
        <ActionContainer
          deleteLabel="Delete"
          onDelete={() => {
            handleDelete(item.carePlanID, type, item.id);
          }}
          editLabel="View Details"
          onEdit={() => {
            handleClick(type, item.id);
          }}
        />
      </div>
      <div className="flex items-center text-[15px]">
        {details.map((detail: DetailItem, detailIndex: number) => (
          // eslint-disable-next-line react/no-array-index-key
          <div key={detailIndex} className="flex items-center">
            {(detail.fieldType === 'user' ? detail.detail : detail.detail?.length > 0) ? renderDetailByFieldType(
              detail.fieldType,
              detail.detail,
              detail.showDaysFromCurrent !== false,
              detail.capitalize !== false,
            )
              : '-'}
            {detailIndex !== details.length - 1 && (
              <p className="p-0 m-0 mx-2 pb-[0px] text-[#262D2D]">|</p>
            )}
          </div>
        ))}
      </div>
    </div>
  );
}

export default ViewCarePlanItem;
