import CustomTable from 'components/table-components/table';
import { capitalizeWords } from 'utils/helper';

function AddRow({
  items,
  setOpenAddItem,
  title,
  name,
  handleDelete,
}: {
  items: any;
  setOpenAddItem: any;
  title: string;
  name: string;
  // eslint-disable-next-line no-unused-vars
  handleDelete: (id: any, type: string) => void;
}) {
  const singularTitle = title.replace(/s$/, '');

  const getTitleValue = (val: any) => (typeof val === 'object' && val !== null && 'title' in val ? val.title : val);

  const newItems = items.map((item: any) => Object.fromEntries(
    Object.entries(item).map(([key, val]) => [
      key,
      getTitleValue(val).length > 25
        ? capitalizeWords(getTitleValue(val))
        : capitalizeWords(getTitleValue(val), true),
    ]),
  ));

  const getColumnWidths = (type: string) => {
    const defaultWidths = {
      firstColumnStaticWidth: 30,
      secondColumnStaticWidth: 16,
      thirdColumnStaticWidth: 14,
      fourthColumnStaticWidth: 40,
    };

    switch (type) {
      case 'follow-ups':
        return {
          firstColumnStaticWidth: 30,
          secondColumnStaticWidth: 16,
          thirdColumnStaticWidth: 14,
          fourthColumnStaticWidth: 30,
          fifthColumnStaticWidth: 10,
        };
      case 'team':
        return {
          firstColumnStaticWidth: 30,
          secondColumnStaticWidth: 30,
          thirdColumnStaticWidth: 20,
          fourthColumnStaticWidth: 20,
        };
      default:
        return defaultWidths;
    }
  };

  const columnWidths = getColumnWidths(name);

  return (
    <>
      <div className="flex items-center mt-2 mt-[48px] mb-3 justify-between">
        <p className="font-[500] text-[18px] m-0 p-0">{title}</p>
        {items?.length > 0 && (
          <button
            type="button"
            onClick={() => {
              setOpenAddItem(true);
            }}
            className="text-primary font-[500] text-[15px] cursor-pointer hover:text-primary/80 m-0 p-0 border-0 bg-transparent pb-[2px]"
          >
            + Add
            {' '}
            {capitalizeWords(singularTitle.toLowerCase())}
          </button>
        )}
      </div>
      {items?.length === 0 ? (
        <div className="flex items-center gap-2">
          <p className="text-[15px] m-0 p-0">
            No
            {' '}
            {title.toLowerCase()}
            {' '}
            {name === 'team' ? 'has' : 'have'}
            {' '}
            been added to this care plan.
          </p>
          <button
            type="button"
            onClick={() => {
              setOpenAddItem(true);
            }}
            className="text-primary font-[500] text-[15px] cursor-pointer hover:text-primary/80 m-0 p-0 border-0 bg-transparent pb-[2px]"
          >
            + Add
            {' '}
            {capitalizeWords(singularTitle.toLowerCase())}
          </button>
        </div>
      ) : (
        <CustomTable
          variant={`${name}-table`}
          source={name === 'team' ? items : newItems}
          className="mt-3"
          style={{
            border: 'solid 1px lightgray',
            borderRadius: '10px',
            borderBottom: 'none',
          }}
          headCellStyle={{
            padding: '4px 16px',
            fontSize: '13px',
            color: '#747A7A',
          }}
          tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
          fixedLayout // enables fixed layout + truncation
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...columnWidths}
          handleDelete={(id) => handleDelete(id, name)}
        />
      )}
    </>
  );
}

export default AddRow;
