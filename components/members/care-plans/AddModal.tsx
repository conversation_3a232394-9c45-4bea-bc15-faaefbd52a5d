/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable no-unused-vars */
import {
  Modal,
  TextField,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
  Pagination,
} from '@mui/material';
import VaccinesOutlinedIcon from '@mui/icons-material/VaccinesOutlined';
import AssignmentOutlinedIcon from '@mui/icons-material/AssignmentOutlined';
import MoreHorizOutlinedIcon from '@mui/icons-material/MoreHorizOutlined';
import ControlledSearch from 'components/search/controlled-search';
import SelectBox from 'components/select-box/select-box';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import React, {
  useContext, useEffect, useState, useRef,
} from 'react';
import CheckIcon from '@mui/icons-material/Check';
import dayjs from 'dayjs';
import DateSelector from 'components/date-selector/date-selector';
import Close from '@mui/icons-material/Close';
import { ArrowBack, ArrowForward } from '@mui/icons-material';
import Search from 'components/search/search';
import API, { ORG_ID, TOKEN } from 'api/src/lib/api';
import { NETWORKS } from 'api/src/routes/networks';
import CustomTable from 'components/table-components/table';
import { Network } from 'types/networks-types';
import Cookies from 'js-cookie';
import { capitalizeWords } from 'utils/helper';

interface AddModalProps {
  open: boolean;
  setOpen: (value: boolean) => void;
  handleAdd: (type: string, fields: any) => void;
  type: string;
  carePlanSections: Record<string, any[]>;
}

function AddModal({
  open,
  setOpen,
  handleAdd,
  type,
  carePlanSections,
}: AddModalProps) {
  const constant = useContext(ConstantsContext);
  const backdropRef = useRef<HTMLDivElement | null>(null);
  const mouseDownOnBackdrop = useRef(false);
  const [team, setTeam] = useState<any>(null);
  const teamId = Cookies.get('teamID') || '';
  const [problemType, setProblemType] = useState('');

  function extractNavigatorsFromTeams(teams: any[]) {
    return teams.flatMap((t) => t.navigators || []);
  }

  useEffect(() => {
    const fetchTeam = async () => {
      if (teamId !== 'all') {
        const response: any = await API.TEAMS.fetchSingleTeam(
          teamId,
          TOKEN(),
          ORG_ID(),
        );
        setTeam(response?.navigators || []);
      } else {
        const response: any = await API.TEAMS.fetchTeams(TOKEN(), ORG_ID());
        const allTeamNavigators = extractNavigatorsFromTeams(
          response?.items || [],
        );
        setTeam(allTeamNavigators || []);
      }
    };
    fetchTeam();
  }, []);

  function formatTeamSelectOptions(teamArray: any) {
    return teamArray?.map((t: any) => ({
      key: t.id,
      title: `${t.firstName} ${t.lastName}`,
    }));
  }

  const teamSelectOptions = team && formatTeamSelectOptions(team);

  const carePlanSectionInputs = {
    goals: [
      { label: 'Title of Goal', name: 'title', type: 'text' },
      {
        label: 'Goal Type',
        name: 'goalType',
        type: 'select',
        options: constant?.goalTypes || [],
      },
      { label: 'Target Goal Date', name: 'targetDate', type: 'date' },
      { label: 'Goal Objective', name: 'goalObjective', type: 'textarea' },
      {
        label: 'Measurement Criteria',
        name: 'measurementCriteria',
        type: 'textarea',
      },
      {
        label: 'Achievability Note',
        name: 'achievabilityNote',
        type: 'textarea',
      },
      { label: 'Barriers', name: 'barriers', type: 'textarea' },
    ],
    interventions: [
      { label: 'Title of Intervention', name: 'title', type: 'text' },
      {
        label: 'Responsible Party',
        name: 'responsibleParty',
        type: 'select',
        options: teamSelectOptions || [],
      },
      {
        label: 'Intervention Notes',
        name: 'note',
        type: 'textarea',
      },
      { label: 'Due Date', name: 'dueDate', type: 'date' },
      {
        label: 'Goal',
        name: 'goal',
        type: 'select',
        options: constant?.interventionTypes || [],
      },
      {
        label: 'Auto-assign as Task',
        name: 'autoAssignAsTask',
        type: 'select',
        options: [
          { title: 'Yes', key: 'yes' },
          { title: 'No', key: 'no' },
        ],
      },
    ],
    reviews: [
      { label: 'Review Title', name: 'title', type: 'text' },
      { label: 'Review Date', name: 'date', type: 'date' },
      { label: 'Review Note', name: 'note', type: 'textarea' },
    ],
    'follow-ups': [
      {
        label: 'Follow-up Type',
        name: 'type',
        type: 'select',
        options: constant?.followUpTypes || [],
      },
      { label: 'Follow-up Date', name: 'date', type: 'date' },
      {
        label: 'Outcome',
        name: 'outcome',
        type: 'select',
        options: constant?.followUpOutcomes || [],
      },
      { label: 'Follow-up Note', name: 'note', type: 'textarea' },
    ],
    'network-appointments': [
      {
        label: 'Provider Name (optional)',
        name: 'providerName',
        type: 'text',
        required: false,
      },
      {
        label: 'Appointment Date & Time',
        name: 'appointmentDateTime',
        type: 'datetime',
      },
      {
        label: 'Appointment Note (optional)',
        name: 'appointmentNote',
        type: 'textarea',
        required: false,
      },
    ],
    notes: [
      { label: 'Note Title', name: 'title', type: 'text' },
      { label: 'Primary Concern', name: 'primaryConcern', type: 'text' },
      { label: 'Note', name: 'note', type: 'textarea' },
    ],
    problems:
      problemType === 'other'
        ? [
          { label: 'Title', name: 'title', type: 'text' },
          {
            label: 'Problem Type',
            name: 'type',
            type: 'select',
            options: constant?.problemTypes || [],
          },
          { label: 'Description', name: 'description', type: 'textarea' },
        ]
        : problemType === 'medication'
          ? [
            {
              label: 'Type',
              name: 'type',
              type: 'select',
              options: [
                { key: 'medical', title: 'Medical' },
                { key: 'behavioral', title: 'Behavioral' },
              ],
            },
            {
              label: 'Note (optional)',
              name: 'clinicalNote',
              type: 'textarea',
              required: false,
            },
            {
              label: 'Date Identified (optional)',
              name: 'dateIdentified',
              type: 'date',
              required: false,
            },
          ]
          : [
            {
              label: 'Note (optional)',
              name: 'clinicalNote',
              type: 'textarea',
              required: false,
            },
            {
              label: 'Date Identified (optional)',
              name: 'dateIdentified',
              type: 'date',
              required: false,
            },
          ],
  };

  const [step, setStep] = useState(1);
  const [prevStep, setPrevStep] = useState(1);
  // Detect direction of step change
  const isIncreasing = step > prevStep;
  const isDecreasing = step < prevStep;
  const [formValues, setFormValues] = useState<Record<string, any>>({});
  const [missingFields, setMissingFields] = useState<string[]>([]);
  const [showMissingModal, setShowMissingModal] = useState(false);
  const [networkSearch, setNetworkSearch] = useState('');
  const [networkType, setNetworkType] = useState('');
  const [networksList, setNetworksList] = useState<any[]>([]);
  const [networksFromApi, setNetworksFromApi] = useState(false);
  const [networksTotal, setNetworksTotal] = useState(0);
  const [networksPerPage, setNetworksPerPage] = useState(10);
  const [networkPage, setNetworkPage] = useState(1);
  const [selectedNetworks, setSelectedNetworks] = useState<Network[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedDiagnosis, setSelectedDiagnosis] = useState<any[]>([]);
  const [search, setSearch] = useState('');
  const [debouncedSearch, setDebouncedSearch] = useState('');
  const [diagnosisList, setDiagnosisList] = useState<any[]>([]);
  const [diagnosisPage, setDiagnosisPage] = useState(1);
  const [diagnosisPerPage, setDiagnosisPerPage] = useState(8);
  const [diagnosisTotalItems, setDiagnosisTotalItems] = useState(0);
  const [medicationList, setMedicationList] = useState<any[]>([]);
  const [medicationPage, setMedicationPage] = useState(1);
  const [medicationPerPage, setMedicationPerPage] = useState(8);
  const [medicationTotalItems, setMedicationTotalItems] = useState(0);
  const [selectedMedication, setSelectedMedication] = useState<any[]>([]);
  const [medicationDetails, setMedicationDetails] = useState<any>(null);

  useEffect(() => {
    if (type && carePlanSections[type]) {
      const initialValues: Record<string, any> = {};
      carePlanSections[type].forEach((field: any) => {
        initialValues[field.name] = '';
      });
      setFormValues(initialValues);
    }
  }, [type]);

  function formatTitle(title: string) {
    const singular = title.endsWith('s') ? title.slice(0, -1) : title;
    if (singular.toLowerCase() === 'follow-up') return 'Follow-Up';
    return singular.replace(/-/g, ' ').replace(/\b\w/g, (c) => c.toUpperCase());
  }

  const setFieldValue = (name: string, value: any) => {
    setFormValues((prev) => ({ ...prev, [name]: value }));
  };

  const fields = carePlanSectionInputs[type as keyof typeof carePlanSectionInputs] || [];
  const orderedFields = React.useMemo(() => {
    if (type === 'network-appointments') {
      // Find the datetime and provider fields
      const datetimeField = fields.find(
        (f) => f.name === 'appointmentDateTime',
      );
      const providerField = fields.find((f) => f.name === 'providerName');

      // Put datetime first, then provider, then others excluding those two
      return [
        datetimeField,
        providerField,
        ...fields.filter(
          (f) => f.name !== 'appointmentDateTime' && f.name !== 'providerName',
        ),
      ].filter(Boolean); // filter out any undefined if not found
    }
    return fields;
  }, [fields, type]);
  const rowFields = orderedFields.filter((f: any) => f.type !== 'textarea');
  const textAreas = orderedFields.filter((f: any) => f.type === 'textarea');
  const multipleTextAreas = textAreas.length > 1;

  const validateFields = () => {
    const fieldDefs = carePlanSectionInputs[type as keyof typeof carePlanSectionInputs] || [];
    const missing: string[] = [];

    fieldDefs.forEach((field: any) => {
      const isRequired = field.required !== false; // default to true
      const value = formValues[field.name];

      const isEmpty = value === null
        || value === undefined
        || (typeof value === 'string' && value.trim() === '')
        || (typeof value === 'object' && Object.keys(value).length === 0);

      if (isRequired && isEmpty) {
        missing.push(field.label);
      }
    });

    if (missing.length > 0) {
      setMissingFields(missing);
      setShowMissingModal(true);
      return false;
    }

    return true;
  };

  function renderInput(field: any) {
    const value = formValues[field.name] ?? '';

    switch (field.type) {
      case 'text':
        return (
          <TextField
            key={field.name}
            name={field.name}
            placeholder={field.label}
            label={field.label}
            value={value}
            variant="filled"
            onChange={(e) => setFieldValue(field.name, e.target.value)}
            fullWidth
            className="mt-0"
          />
        );
      case 'select':
        return (
          <SelectBox
            key={field.name}
            label={field.label}
            keyVal={field.name}
            defaultValue={value?.key || ''}
            onChange={(e) => {
              const selectedKey = e.target.value;
              setFieldValue(field.name, {
                key: selectedKey,
                title:
                  field.options.find(
                    (o: any) => o.key.toLowerCase() === selectedKey.toLowerCase(),
                  )?.title || '',
              });
            }}
            items={field.options || []}
          />
        );
      case 'date':
        return (
          <DateSelector
            key={field.name}
            name={field.name}
            defaultValue={value}
            pickerProps={{
              label: field.label,
              slotProps: {
                field: {
                  clearable: true,
                  onClear: () => setFieldValue(field.name, ''),
                },
              },
            }}
            onChange={(e) => {
              const date = dayjs(e.target.value).format('YYYY-MM-DD');
              setFieldValue(field.name, date);
            }}
          />
        );
      case 'datetime':
        return (
          <DateSelector
            key={field.name}
            name={field.name}
            defaultValue={value}
            time
            dateTimePickerProps={{
              label: field.label,
              slotProps: {
                field: {
                  clearable: true,
                  onClear: () => setFieldValue(field.name, ''),
                },
              },
            }}
            onChange={(e) => {
              const date = dayjs(e.target.value).format('YYYY-MM-DD HH:mm');
              setFieldValue(field.name, date);
            }}
          />
        );
      case 'textarea':
        return (
          <TextField
            key={field.name}
            name={field.name}
            label={field.label}
            value={value}
            variant="filled"
            multiline
            rows={multipleTextAreas ? 3 : 6}
            onChange={(e) => setFieldValue(field.name, e.target.value)}
            fullWidth
            sx={{ backgroundColor: '#F7F8FA', marginTop: 2 }}
          />
        );
      default:
        return null;
    }
  }

  const loadNetworks = async (page = 1, search = searchTerm) => {
    const response: any = await NETWORKS.fetchOrgNetworks(
      TOKEN(),
      ORG_ID(),
      8,
      page,
      'createdAt',
      'desc',
      search,
      networkType,
    );
    setNetworksList(response.items);
    setNetworksTotal(response.metadata.total);
    setNetworksFromApi(true);
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newSearch = e.target.value;
    setSearchTerm(newSearch);
    if (type === 'network-appointments') {
      loadNetworks(1, newSearch);
    } else if (type === 'problems') {
      setSearch(newSearch);
    }
  };

  useEffect(() => {
    loadNetworks();
  }, [networkType]);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedSearch(search);
    }, 300); // 300ms delay

    return () => clearTimeout(handler); // clear previous timer
  }, [search]);

  useEffect(() => {
    if (debouncedSearch && problemType === 'medication') {
      const fetchMedication = async () => {
        const response: any = await API.CAREPLANS.searchMedication(
          debouncedSearch,
          TOKEN(),
        );

        // Filter out items with displayName === 'Unknown'
        const filtered = response.medications.filter(
          (med: any) => med.displayName !== 'Unknown',
        );

        // Remove duplicates based on rxcui
        const uniqueByRxcui = Array.from(
          new Map(filtered.map((med: any) => [med.rxcui, med])).values(),
        );

        setMedicationList(uniqueByRxcui);
        setMedicationTotalItems(uniqueByRxcui.length);
      };
      fetchMedication();
    } else if (debouncedSearch && problemType === 'diagnosis') {
      const fetchDiagnosis = async () => {
        const response: any = await API.CAREPLANS.searchWHODiagnosis(
          debouncedSearch,
          TOKEN(),
        );
        setDiagnosisList(response.results);
        setDiagnosisTotalItems(response.totalResults);
      };
      fetchDiagnosis();
    }
  }, [debouncedSearch, problemType]);

  const pagedDiagnosisList = diagnosisList?.slice(
    (diagnosisPage - 1) * diagnosisPerPage,
    diagnosisPage * diagnosisPerPage,
  );
  const pagedMedicationList = medicationList?.slice(
    (medicationPage - 1) * medicationPerPage,
    medicationPage * medicationPerPage,
  );

  const fetchMedicationDetails = async (rxcui: string) => {
    const response: any = await API.CAREPLANS.fetchMedicationDetails(
      rxcui,
      TOKEN(),
    );
    setMedicationDetails(response.medication);
  };

  const createHandleAddModel = () => {
    if (problemType === 'medication') {
      return {
        ...formValues,
        title: medicationDetails.displayName,
        rxcui: medicationDetails.rxcui,
        description: formValues.clinicalNote || '',
        icdCode: medicationDetails.rxcui,
        type: formValues.type,
        source: problemType,
      };
    }
    if (problemType === 'diagnosis') {
      return {
        ...formValues,
        title: selectedDiagnosis[0].title,
        description: selectedDiagnosis[0].id,
        icdCode: selectedDiagnosis[0].theCode,
        type: 'diagnosis',
        source: problemType,
      };
    }
    return {
      ...formValues,
      type: 'other',
      source: problemType,
    };
  };

  useEffect(() => {
    if (step === 1) {
      setProblemType('');
      setSearchTerm('');
      setDiagnosisList([]);
      setMedicationList([]);
      setDiagnosisPage(1);
      setMedicationPage(1);
      setSearch('');
      setDebouncedSearch('');
      setSelectedDiagnosis([]);
      setSelectedMedication([]);
    }
  }, [step, open, setOpen]);

  if (type === 'network-appointments') {
    return (
      <>
        <Modal open={open} onClose={() => setOpen(false)}>
          <div
            ref={backdropRef}
            className="fixed inset-0 flex items-center justify-center p-4 z-50"
            onMouseDown={(e) => {
              mouseDownOnBackdrop.current = e.target === e.currentTarget;
            }}
            onMouseUp={(e) => {
              if (mouseDownOnBackdrop.current && e.target === e.currentTarget) {
                setOpen(false);
              }
            }}
          >
            <div
              className="flex flex-col h-[95vh] w-[90%] bg-white rounded-lg overflow-hidden"
              // onClick={(e) => e.stopPropagation()} (commented out to fix date picker closing conflict with modal)
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  if (!validateFields()) return;
                  handleAdd(type, formValues);
                  setFormValues({});
                  setMissingFields([]);
                  setOpen(false);
                }
              }}
              // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
              tabIndex={0} // Makes div focusable so it can listen for key events
            >
              {/* Header */}
              <div
                style={{ borderBottom: '1px solid #E0E0E0' }}
                className="h-[64px] shrink-0 items-center relative justify-center grid grid-flow-col border border-black "
              >
                <div className="grid grid-flow-row p-4 items-center text-center">
                  <p className="m-0 font-[500] text-[18px]">
                    Add
                    {' '}
                    {formatTitle(type)}
                  </p>
                </div>
                <Close
                  className="absolute right-5 cursor-pointer"
                  onClick={() => setOpen(false)}
                />
              </div>
              {/* Body */}
              <div className="relative w-full flex flex-col flex-grow overflow-hidden overflow-y-auto min-h-0 px-[40px] pt-4 pb-5">
                <p className="text-[15px] font-[500] text-[#747A7A] mt-1 mb-2">
                  Step
                  {' '}
                  {step}
                  {' '}
                  of 2
                </p>
                <div
                  className="relative w-full overflow-hidden flex-grow flex flex-col"
                  style={{ minHeight: '300px' }}
                >
                  <div
                    className="absolute w-full h-full transition-all duration-200 ease flex flex-col"
                    style={{
                      transform:
                        step === 1 ? 'translateX(0%)' : 'translateX(-3%)',
                      opacity: step === 1 ? 1 : 0,
                      zIndex: step === 1 ? 2 : 1,
                    }}
                  >
                    <p className="m-0 p-0 font-[600] text-lg">Select Network</p>
                    <div className="flex gap-x-5 gap-y-3 mt-[10px] flex-wrap">
                      <div className="w-[400px] ml-[1px]">
                        <Search
                          placeholder={
                            selectedNetworks.length > 0
                              ? selectedNetworks[0].name
                              : 'Search networks...'
                          }
                          onChange={handleSearch}
                          className="h-[56px]"
                        />
                      </div>
                      <div className="w-[400px]">
                        <SelectBox
                          key="networkType"
                          label="Network Type"
                          keyVal="networkType"
                          defaultValue={networkType || ''}
                          onChange={(e) => {
                            setNetworkType(e.target.value);
                          }}
                          items={[
                            { key: '', title: 'All Types' },
                            ...(constant?.networkServices || []),
                          ]}
                        />
                      </div>
                    </div>
                    <div className="mt-4 overflow-y-auto flex-grow pb-[0px]">
                      <CustomTable
                        source={networksList}
                        variant="select-network-appointments-table"
                        metadata={{
                          list: networksList,
                          setSelected: setSelectedNetworks,
                          disableMultiSelect: true,
                        }}
                        style={{
                          border: 'solid 1px lightgray',
                          borderRadius: '10px',
                          borderBottom: 'none',
                        }}
                        headCellStyle={{
                          padding: '4px 16px',
                          fontSize: '13px',
                          color: '#747A7A',
                        }}
                        tableCellStyle={{
                          padding: '0px 16px',
                          fontSize: '15px',
                        }}
                        selected={selectedNetworks || []}
                        setSelected={setSelectedNetworks}
                        select
                      />
                      {networksFromApi && (
                        <div className="mt-4">
                          <Pagination
                            count={Math.ceil(networksTotal / networksPerPage)}
                            page={networkPage}
                            onChange={(_, value) => {
                              setNetworkPage(value);
                              loadNetworks(value, networkSearch);
                            }}
                            className="pt-5 align-middle"
                          />
                        </div>
                      )}
                    </div>
                  </div>
                  <div
                    className="absolute w-full h-full transition-all duration-200 ease"
                    style={{
                      transform:
                        step === 2 ? 'translateX(0%)' : 'translateX(3%)',
                      opacity: step === 2 ? 1 : 0,
                      zIndex: step === 2 ? 2 : 1,
                    }}
                  >
                    {/* Body */}
                    <div className="overflow-y-auto max-h-[70vh] pb-5 flex-grow">
                      <p className="m-0 p-0 font-[600] text-lg">
                        Complete Appointment Details
                      </p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-[10px]">
                        {rowFields.map((field: any) => (
                          <div
                            key={field.name}
                            className="flex flex-col h-[56px]"
                          >
                            {renderInput(field)}
                          </div>
                        ))}
                      </div>
                      {textAreas.map((field: any) => (
                        <div
                          key={field.name}
                          className={
                            textAreas.length === 1
                              ? 'flex-grow flex flex-col'
                              : ''
                          }
                        >
                          {renderInput(field)}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div
                id="footer-area"
                style={{ borderTop: '1px solid #E0E0E0', zIndex: 2 }}
                className="shrink-0 bg-white border-t border-gray-300 flex items-center justify-between p-3"
              >
                {step > 1 ? (
                  <Button
                    className="rounded-md m-0 px-[14px] text-[15px] flex items-center justify-center !min-w-[100px] text-[#262D2D] font-[500] bg-gradient-to-b from-[#F7F8F8] to-[#DCDFDF] hover:bg-gradient-to-b hover:from-[#F9FAFA] hover:to-[#D0D3D3]"
                    onClick={() => setStep(step - 1)}
                  >
                    <ArrowBack className="mr-3 w-[18px] h-[18px]" />
                    <p className="font-[500] m-0 p-0 pt-[1px]">Previous</p>
                  </Button>
                ) : (
                  <div />
                )}
                <Button
                  variant="contained"
                  className="bg-[#2D62ED] text-white rounded-md !min-w-fit"
                  onClick={() => {
                    if (step === 1) {
                      setStep(2);
                    } else {
                      if (!validateFields()) return;
                      // Add network field to formValues before submitting
                      const formDataWithNetwork = {
                        ...formValues,
                        networkName: selectedNetworks[0].name,
                      };
                      handleAdd(type, formDataWithNetwork);
                      setFormValues({});
                      setMissingFields([]);
                      setOpen(false);
                    }
                  }}
                >
                  <div className="grid grid-flow-col items-center gap-2">
                    {step === 2 && <CheckIcon />}
                    {step === 1 ? 'Next' : 'Save & Book'}
                    {step === 1 && <ArrowForward />}
                  </div>
                </Button>
              </div>
            </div>
            {/* SVG Gradient Definition (Placed Once in the Component) */}
            <svg width="0" height="0">
              <defs>
                <linearGradient
                  id="radioGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="100%"
                >
                  <stop
                    offset="0%"
                    style={{ stopColor: '#0198A5', stopOpacity: 1 }}
                  />
                  <stop
                    offset="100%"
                    style={{ stopColor: '#008390', stopOpacity: 1 }}
                  />
                </linearGradient>
              </defs>
            </svg>
          </div>
        </Modal>
        <Modal
          open={showMissingModal}
          onClose={() => setShowMissingModal(false)}
        >
          <div
            className="fixed inset-0 flex items-center justify-center p-4 z-50"
            onClick={() => setShowMissingModal(false)}
          >
            <div
              className="bg-white max-w-md w-full p-6 rounded shadow-md"
              onClick={(e) => e.stopPropagation()}
            >
              <h2 className="text-lg font-semibold mb-2 text-center">
                Please complete all required fields:
              </h2>
              <ul className="list-disc list-inside text-sm text-red-600 mb-4">
                {missingFields.map((label) => (
                  <li key={label}>{label}</li>
                ))}
              </ul>
              <div className="flex justify-center">
                <Button
                  onClick={() => setShowMissingModal(false)}
                  variant="contained"
                  className="rounded-md"
                >
                  OK
                </Button>
              </div>
            </div>
          </div>
        </Modal>
      </>
    );
  }

  if (type === 'problems') {
    return (
      <>
        <Modal open={open} onClose={() => setOpen(false)}>
          <div
            ref={backdropRef}
            className="fixed inset-0 flex items-center justify-center p-4 z-50"
            onMouseDown={(e) => {
              mouseDownOnBackdrop.current = e.target === e.currentTarget;
            }}
            onMouseUp={(e) => {
              if (mouseDownOnBackdrop.current && e.target === e.currentTarget) {
                setOpen(false);
              }
            }}
          >
            <div
              className="flex flex-col h-[95vh] w-[90%] bg-white rounded-lg overflow-hidden"
              // onClick={(e) => e.stopPropagation()}
              onKeyDown={(e) => {
                if (e.key === 'Enter') {
                  e.preventDefault();
                  if (!validateFields()) return;
                  handleAdd(type, formValues);
                  setFormValues({});
                  setMissingFields([]);
                  setOpen(false);
                }
              }}
              tabIndex={0}
            >
              {/* Header */}
              <div
                style={{ borderBottom: '1px solid #E0E0E0' }}
                className="h-[64px] shrink-0 items-center relative justify-center grid grid-flow-col border border-black"
              >
                <div className="grid grid-flow-row p-4 items-center text-center">
                  <p className="m-0 font-[500] text-[18px]">
                    Add
                    {' '}
                    {formatTitle(type)}
                  </p>
                </div>
                <Close
                  className="absolute right-5 cursor-pointer"
                  onClick={() => setOpen(false)}
                />
              </div>

              {/* Body */}
              <div className="relative w-full flex flex-col flex-grow overflow-hidden overflow-y-auto min-h-0 px-[40px] pt-4 pb-5">
                <p className="text-[15px] font-[500] text-[#747A7A] mt-1 mb-2">
                  Step
                  {' '}
                  {step}
                  {step !== 1 && (
                    <>
                      {' '}
                      of
                      {' '}
                      {problemType === 'medication' && 4}
                      {problemType === 'diagnosis' && 3}
                      {problemType === 'other' && 2}
                    </>
                  )}
                </p>

                <div
                  className="relative w-full overflow-hidden flex-grow flex flex-col"
                  style={{ minHeight: '300px' }}
                >
                  {/* Step 1 – Select Problem Type */}
                  <div
                    className="absolute w-full h-full transition-all duration-200 ease"
                    style={{
                      transform:
                        step === 1 ? 'translateX(0%)' : 'translateX(-3%)',
                      opacity: step === 1 ? 1 : 0,
                      zIndex: step === 1 ? 2 : 1,
                    }}
                  >
                    <p className="m-0 p-0 font-[600] text-lg">
                      Select Problem Type
                    </p>
                    <div className="flex gap-x-5 gap-y-3 flex-wrap mt-7">
                      <button
                        type="button"
                        className="problem-type-card"
                        onClick={() => {
                          setStep(2);
                          setProblemType('medication');
                        }}
                      >
                        <VaccinesOutlinedIcon className="text-[#ee7502] w-10 h-10" />
                        <p className="m-0 p-0">Medication</p>
                      </button>
                      <button
                        type="button"
                        className="problem-type-card"
                        onClick={() => {
                          setStep(2);
                          setProblemType('diagnosis');
                        }}
                      >
                        <AssignmentOutlinedIcon className="text-[#ee7502] w-10 h-10" />
                        <p className="m-0 p-0">Diagnosis</p>
                      </button>
                      <button
                        type="button"
                        className="problem-type-card"
                        onClick={() => {
                          setStep(2);
                          setProblemType('other');
                        }}
                      >
                        <MoreHorizOutlinedIcon className="text-[#ee7502] w-10 h-10" />
                        <p className="m-0 p-0">Other</p>
                      </button>
                    </div>
                  </div>

                  {/* Step 2 – Select Diagnosis or Medication */}
                  {problemType !== 'other' && (
                    <div
                      className="absolute w-full h-full transition-all duration-200 ease flex flex-col"
                      style={{
                        transform:
                          step === 2
                            ? 'translateX(0%)'
                            : step === 3
                              ? 'translateX(-3%)'
                              : 'translateX(3%)',
                        opacity: step === 2 ? 1 : 0,
                        zIndex: step === 2 ? 2 : 1,
                      }}
                    >
                      <p className="m-0 p-0 font-[600] text-lg">
                        {problemType === 'medication'
                          ? 'Select Medication'
                          : 'Select Diagnosis'}
                      </p>
                      <div className="flex gap-x-5 gap-y-3 mt-[10px] flex-wrap">
                        <div className="w-[400px] ml-[1px]">
                          <ControlledSearch
                            placeholder={`Search ${
                              problemType === 'medication'
                                ? 'medication'
                                : 'diagnoses'
                            }...`}
                            onChange={handleSearch}
                            className="h-[56px]"
                            value={searchTerm}
                          />
                        </div>
                      </div>
                      {problemType === 'diagnosis' && (
                        <div className="mt-4 overflow-y-auto flex-grow pb-[0px]">
                          {diagnosisList?.length > 0 ? (
                            <CustomTable
                              source={pagedDiagnosisList}
                              variant="select-diagnosis-table"
                              metadata={{
                                list: diagnosisList,
                                setSelected: setSelectedDiagnosis,
                                disableMultiSelect: true,
                              }}
                              style={{
                                border: 'solid 1px lightgray',
                                borderRadius: '10px',
                                borderBottom: 'none',
                              }}
                              headCellStyle={{
                                padding: '4px 16px',
                                fontSize: '13px',
                                color: '#747A7A',
                              }}
                              tableCellStyle={{
                                padding: '0px 16px',
                                fontSize: '15px',
                              }}
                              selected={selectedDiagnosis || []}
                              setSelected={setSelectedDiagnosis}
                              select
                              pageInfo={{
                                page: diagnosisPage,
                                per: diagnosisPerPage,
                                total: diagnosisTotalItems,
                              }}
                              onPageChange={(newPage) => {
                                setDiagnosisPage(newPage);
                              }}
                            />
                          ) : (
                            <div className="flex h-full">
                              <p className="text-[15px] font-[400] text-[#747A7A] italic ml-6">
                                Search for a diagnosis to select.
                              </p>
                            </div>
                          )}
                        </div>
                      )}
                      {problemType === 'medication' && (
                        <div className="mt-4 overflow-y-auto flex-grow pb-[0px]">
                          {medicationTotalItems > 0 ? (
                            <CustomTable
                              source={pagedMedicationList}
                              variant="select-medication-table"
                              metadata={{
                                list: medicationList,
                                setSelected: setSelectedMedication,
                                disableMultiSelect: true,
                              }}
                              style={{
                                border: 'solid 1px lightgray',
                                borderRadius: '10px',
                                borderBottom: 'none',
                              }}
                              headCellStyle={{
                                padding: '4px 16px',
                                fontSize: '13px',
                                color: '#747A7A',
                              }}
                              tableCellStyle={{
                                padding: '0px 16px',
                                fontSize: '15px',
                              }}
                              selected={selectedMedication || []}
                              setSelected={setSelectedMedication}
                              select
                              pageInfo={{
                                page: medicationPage,
                                per: medicationPerPage,
                                total: medicationTotalItems,
                              }}
                              onPageChange={(newPage) => {
                                setMedicationPage(newPage);
                              }}
                              rowIdField="rxcui"
                            />
                          ) : (
                            <div className="flex h-full">
                              <p className="text-[15px] font-[400] text-[#747A7A] italic ml-6">
                                Search for a medication to select.
                              </p>
                            </div>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {problemType === 'other' && (
                    <div
                      className="absolute w-full h-full transition-all duration-200 ease flex flex-col"
                      style={{
                        transform:
                          step === 2 ? 'translateX(0%)' : 'translateX(3%)',
                        opacity: step === 2 ? 1 : 0,
                        zIndex: step === 2 ? 2 : 1,
                      }}
                    >
                      <div className="pt-4 overflow-y-auto max-h-[70vh] pb-5 flex-grow">
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                          {rowFields.map((field: any) => (
                            <div key={field.name}>{renderInput(field)}</div>
                          ))}
                        </div>
                        {textAreas.map((field: any) => (
                          <div
                            key={field.name}
                            className={
                              textAreas.length === 1
                                ? 'flex-grow flex flex-col'
                                : ''
                            }
                          >
                            {renderInput(field)}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {/* Step 3 – Additional Info */}
                  <div
                    className="absolute w-full h-full transition-all duration-200 ease"
                    style={{
                      transform:
                        step === 3
                          ? 'translateX(0%)'
                          : step === 4 && problemType === 'medication'
                            ? 'translateX(-3%)'
                            : 'translateX(3%)',
                      opacity: step === 3 ? 1 : 0,
                      zIndex: step === 3 ? 2 : 1,
                    }}
                  >
                    {problemType === 'medication' && medicationDetails && (
                      <div className="overflow-y-auto max-h-[70vh] pb-5 flex-grow">
                        <p className="m-0 p-0 font-[600] text-lg mb-6">
                          Medication Details
                        </p>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-x-4 gap-y-8">
                          <div className="flex flex-col">
                            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                              Name
                            </p>
                            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                              {capitalizeWords(
                                medicationDetails.displayName,
                                true,
                              ) || '-'}
                            </p>
                          </div>
                          <div className="flex flex-col">
                            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                              Generic
                            </p>
                            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                              {medicationDetails.isGeneric ? 'Yes' : 'No'}
                            </p>
                          </div>
                          <div className="flex flex-col">
                            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                              Prescribable
                            </p>
                            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                              {medicationDetails.isPrescribable ? 'Yes' : 'No'}
                            </p>
                          </div>
                          <div className="flex flex-col">
                            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                              Route
                            </p>
                            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                              {medicationDetails.route
                                || medicationDetails.doseForm
                                || '-'}
                            </p>
                          </div>
                          <div className="flex flex-col">
                            <p className="m-0 p-0 font-[400] text-[13px] text-[#747A7A]">
                              Rx #
                            </p>
                            <p className="m-0 p-0 font-[500] text-[15px] text-[#262D2D]">
                              {medicationDetails.rxcui || '-'}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                    {problemType !== 'medication' && (
                      <div className="overflow-y-auto max-h-[70vh] pb-5 flex-grow">
                        <p className="m-0 p-0 font-[600] text-lg">
                          Additional Information
                        </p>
                        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-[10px]">
                          {rowFields.map((field: any) => (
                            <div
                              key={field.name}
                              className="flex flex-col h-[56px]"
                            >
                              {renderInput(field)}
                            </div>
                          ))}
                        </div>
                        {textAreas.map((field: any) => (
                          <div
                            key={field.name}
                            className={
                              textAreas.length === 1
                                ? 'flex-grow flex flex-col'
                                : ''
                            }
                          >
                            {renderInput(field)}
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  {/* Step 4 – Additional Info */}
                  <div
                    className="absolute w-full h-full transition-all duration-200 ease"
                    style={{
                      transform:
                        step === 4 ? 'translateX(0%)' : 'translateX(3%)',
                      opacity: step === 4 ? 1 : 0,
                      zIndex: step === 4 ? 2 : 1,
                    }}
                  >
                    <div className="overflow-y-auto max-h-[70vh] pb-5 flex-grow">
                      <p className="m-0 p-0 font-[600] text-lg">
                        Additional Information
                      </p>
                      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 mt-[10px] ml-[1px]">
                        {rowFields.map((field: any) => (
                          <div
                            key={field.name}
                            className="flex flex-col h-[56px]"
                          >
                            {renderInput(field)}
                          </div>
                        ))}
                      </div>
                      {textAreas.map((field: any) => (
                        <div
                          key={field.name}
                          className={
                            textAreas.length === 1
                              ? 'flex-grow flex flex-col'
                              : ''
                          }
                        >
                          {renderInput(field)}
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>

              {/* Footer */}
              <div
                id="footer-area"
                style={{
                  borderTop: '1px solid #E0E0E0',
                  zIndex: 2,
                  display: step === 1 && !problemType ? 'none' : 'flex',
                }}
                className="shrink-0 bg-white border-t border-gray-300 flex items-center justify-between p-3"
              >
                {step > 1 ? (
                  <Button
                    className="rounded-md m-0 px-[14px] text-[15px] flex items-center justify-center !min-w-[100px] text-[#262D2D] font-[500] bg-gradient-to-b from-[#F7F8F8] to-[#DCDFDF] hover:bg-gradient-to-b hover:from-[#F9FAFA] hover:to-[#D0D3D3]"
                    onClick={() => setStep(step - 1)}
                  >
                    <ArrowBack className="mr-3 w-[18px] h-[18px]" />
                    <p className="font-[500] m-0 p-0 pt-[1px]">Previous</p>
                  </Button>
                ) : (
                  <div />
                )}
                <Button
                  variant="contained"
                  className={`bg-[#2D62ED] text-white rounded-md !min-w-fit ${
                    step === 2
                    && ((problemType === 'medication'
                      && !selectedMedication[0]?.rxcui)
                      || (problemType === 'diagnosis'
                        && !selectedDiagnosis[0]))
                    && 'opacity-50'
                  }`}
                  disabled={
                    (step === 2
                      && problemType === 'medication'
                      && !selectedMedication[0]?.rxcui)
                    || (problemType === 'diagnosis' && !selectedDiagnosis[0])
                  }
                  onClick={() => {
                    if (
                      step < 3
                      || (step === 3 && problemType === 'medication')
                    ) {
                      if (step === 2 && problemType === 'other') {
                        if (!validateFields()) return;
                        const formDataWithDiagnosis = {
                          ...formValues,
                          title: formValues.title,
                          description: formValues.description,
                          type: formValues.type,
                          source: problemType,
                        };
                        handleAdd(type, formDataWithDiagnosis);
                        setFormValues({});
                        setMissingFields([]);
                        setOpen(false);
                      }
                      if (step === 2 && problemType === 'medication') {
                        fetchMedicationDetails(selectedMedication[0].rxcui);
                      }
                      setStep(step + 1);
                    } else {
                      if (!validateFields()) return;
                      const handleAddData = createHandleAddModel();
                      handleAdd(type, handleAddData);
                      setFormValues({});
                      setMissingFields([]);
                      setOpen(false);
                    }
                  }}
                >
                  <div className="grid grid-flow-col items-center gap-2">
                    {((step === 3 && problemType !== 'medication')
                      || (step === 2 && problemType === 'other')
                      || (step === 4 && problemType === 'medication')) && (
                      <CheckIcon />
                    )}
                    {step < 3
                      ? step === 2 && problemType === 'other'
                        ? 'Save'
                        : 'Next'
                      : step === 3 && problemType === 'medication'
                        ? 'Next'
                        : 'Save'}
                    {step < 3 ? (
                      step === 2 && problemType === 'other' ? (
                        <div />
                      ) : (
                        <ArrowForward />
                      )
                    ) : (
                      <div />
                    )}
                    {step === 3 && problemType === 'medication' && (
                      <ArrowForward />
                    )}
                  </div>
                </Button>
              </div>
            </div>

            {/* Gradient Definitions */}
            <svg width="0" height="0">
              <defs>
                <linearGradient
                  id="radioGradient"
                  x1="0%"
                  y1="0%"
                  x2="100%"
                  y2="100%"
                >
                  <stop
                    offset="0%"
                    style={{ stopColor: '#0198A5', stopOpacity: 1 }}
                  />
                  <stop
                    offset="100%"
                    style={{ stopColor: '#008390', stopOpacity: 1 }}
                  />
                </linearGradient>
              </defs>
            </svg>
          </div>
        </Modal>
        <Modal
          open={showMissingModal}
          onClose={() => setShowMissingModal(false)}
        >
          <div
            className="fixed inset-0 flex items-center justify-center p-4 z-50"
            onClick={() => setShowMissingModal(false)}
          >
            <div
              className="bg-white max-w-md w-full p-6 rounded shadow-md"
              onClick={(e) => e.stopPropagation()}
            >
              <h2 className="text-lg font-semibold mb-2 text-center">
                Please complete all required fields:
              </h2>
              <ul className="list-disc list-inside text-sm text-red-600 mb-4">
                {missingFields.map((label) => (
                  <li key={label}>{label}</li>
                ))}
              </ul>
              <div className="flex justify-center">
                <Button
                  onClick={() => setShowMissingModal(false)}
                  variant="contained"
                  className="rounded-md"
                >
                  OK
                </Button>
              </div>
            </div>
          </div>
        </Modal>
      </>
    );
  }

  return (
    <>
      <Modal open={open} onClose={() => setOpen(false)}>
        <div
          ref={backdropRef}
          className="fixed inset-0 flex items-center justify-center p-4 z-50"
          onMouseDown={(e) => {
            mouseDownOnBackdrop.current = e.target === e.currentTarget;
          }}
          onMouseUp={(e) => {
            if (mouseDownOnBackdrop.current && e.target === e.currentTarget) {
              setOpen(false);
            }
          }}
        >
          <div
            className="bg-white max-h-[80vh] w-[90%] rounded-lg overflow-hidden flex flex-col"
            // onClick={(e) => e.stopPropagation()}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                e.preventDefault();
                if (!validateFields()) return;
                handleAdd(type, formValues);
                setFormValues({});
                setMissingFields([]);
                setOpen(false);
              }
            }}
            // eslint-disable-next-line jsx-a11y/no-noninteractive-tabindex
            tabIndex={0} // Makes div focusable so it can listen for key events
          >
            {/* Header */}
            <div
              style={{ borderBottom: '1px solid #E0E0E0' }}
              className="h-[64px] items-center relative justify-center grid grid-flow-col border border-black "
            >
              <div className="grid grid-flow-row p-4 items-center text-center">
                <p className="m-0 font-[500] text-[18px]">
                  Add
                  {' '}
                  {formatTitle(type)}
                </p>
              </div>
              <Close
                className="absolute right-5 cursor-pointer"
                onClick={() => setOpen(false)}
              />
            </div>

            {/* Body */}
            <div className="px-[40px] pt-4 overflow-y-auto max-h-[70vh] pb-5 flex-grow">
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                {rowFields.map((field: any) => (
                  <div key={field.name}>{renderInput(field)}</div>
                ))}
              </div>
              {textAreas.map((field: any) => (
                <div
                  key={field.name}
                  className={
                    textAreas.length === 1 ? 'flex-grow flex flex-col' : ''
                  }
                >
                  {renderInput(field)}
                </div>
              ))}
            </div>

            {/* Footer */}
            <div
              id="footer-area"
              style={{ borderTop: '1px solid #E0E0E0', zIndex: 2 }}
              className="shrink-0 bg-white border-t border-gray-300 flex items-center justify-end p-3"
            >
              <Button
                variant="contained"
                className="bg-[#2D62ED] text-white rounded-md !min-w-fit"
                onClick={() => {
                  if (!validateFields()) return;
                  handleAdd(type, formValues);
                  setFormValues({});
                  setMissingFields([]);
                  setOpen(false);
                }}
              >
                <div className="grid grid-flow-col items-center gap-2">
                  <CheckIcon />
                  Save
                </div>
              </Button>
            </div>
          </div>
        </div>
      </Modal>

      {/* Missing fields modal */}
      <Modal open={showMissingModal} onClose={() => setShowMissingModal(false)}>
        <div
          className="fixed inset-0 flex items-center justify-center p-4 z-50"
          onClick={() => setShowMissingModal(false)}
        >
          <div
            className="bg-white max-w-md w-full p-6 rounded shadow-md"
            onClick={(e) => e.stopPropagation()}
          >
            <h2 className="text-lg font-semibold mb-2 text-center">
              Please complete all required fields:
            </h2>
            <ul className="list-disc list-inside text-sm text-red-600 mb-4">
              {missingFields.map((label) => (
                <li key={label}>{label}</li>
              ))}
            </ul>
            <div className="flex justify-center">
              <Button
                onClick={() => setShowMissingModal(false)}
                variant="contained"
                className="rounded-md"
              >
                OK
              </Button>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
}

export default AddModal;
