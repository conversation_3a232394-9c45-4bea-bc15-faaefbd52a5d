/* eslint-disable react/function-component-definition */
import { ScoreResponse } from 'hooks/use-score';
import type React from 'react';
import { NextRouter } from 'next/router';
import { Network } from 'api/src/types';
import { TableHeader } from './type';

// eslint-disable-next-line import/prefer-default-export
export const ItemsSocialPlansTableColumns = [
  {
    key: 'name',
    value: 'Name',
  },
  {
    key: 'status',
    value: 'Status',
  },
  {
    key: 'desc',
    value: 'Status Update',
  },
  {
    key: 'actions',
  },
] as TableHeader[];

export interface CustomizeSocialPlanFieldsProps {
    field: string;
    children?: React.ReactNode;
    record?: Network;
    value: string | number;
    select?: boolean;
    // eslint-disable-next-line no-unused-vars
    getScoreTitle: (score: number) => ScoreResponse | null;
    router?: NextRouter;
  }
