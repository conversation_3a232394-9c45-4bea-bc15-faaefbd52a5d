/* eslint-disable react/jsx-one-expression-per-line */
import { CustomizeNetworkFieldsProps } from 'components/networks/network-table/columns-keys';
import React from 'react';
import SafeDate from 'components/date-time/SafeDate';

const pStyle = 'text-[13px] font-[400] text-[#001018] first-letter:uppercase';

// eslint-disable-next-line react/function-component-definition
const CustomizeNavigatorFields: React.FC<CustomizeNetworkFieldsProps> = ({
  field,
  value,
  record,
}: CustomizeNetworkFieldsProps) => {
  const address = record?.address && record?.address?.length > 0 ? record?.address[0] : '';
  switch (field) {
    case 'name':
      return <p className="text-[13px] w-[200px]  text-[#001018] font-[500]">{record?.network?.name || record?.name}</p>;
    case 'types':
      return <p className={pStyle}>{record?.types.join('')}</p>;
    case 'address':
      return (
        address === '' ? (
          <p className="pStyle">
            N/A
          </p>
        ) : (
          <div className="grid grid-flow-col auto-cols-max gap-1">
            <p className={pStyle}>{address?.street}</p>
            <p className={pStyle}>{address?.city}</p>
            <p className={pStyle}>{address?.state}</p>
          </div>
        )
      );
    case 'services':
      return <p className={pStyle}>{record?.services?.length || ''}</p>;
    case 'desc':
      return <p className={pStyle}>{record?.desc || ''}</p>;
    case 'updatedAt':
      return <p className={pStyle}><SafeDate date={value as string} time /></p>;

    default:
      return <p className={pStyle}>{value}</p>;
  }
};
export default CustomizeNavigatorFields;
