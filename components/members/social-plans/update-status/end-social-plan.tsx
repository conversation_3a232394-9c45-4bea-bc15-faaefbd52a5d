/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useContext, useState } from 'react';
import {
  Box, Button, Checkbox, Modal, TextField,
} from '@mui/material';
import { Check, Close } from '@mui/icons-material';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import SelectBox from 'components/select-box/select-box';
import API from 'api/src/lib/api';
import DateSelector from 'components/date-selector/date-selector';
import { capitalizeWords, dateToEpoch } from 'utils/helper';
import Cookies from 'js-cookie';
import { useRouter } from 'next/router';

interface EndSocialPlanProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  socialPlanId: string;
  refresh: () => Promise<void>;
  memberName: string;
  memberId: string;
}

// eslint-disable-next-line react/function-component-definition
const EndSocialPlan = ({
  open,
  setOpen,
  refresh,
  socialPlanId,
  memberName,
  memberId,
}: EndSocialPlanProps) => {
  const router = useRouter();
  const reasons = useContext(ConstantsContext)?.reasons;
  const [desc, setDesc] = useState('');
  const [reason, setReason] = useState<string>('');
  const handleClose = () => {
    setOpen(false);
    setReason('');
  };

  const handleEndSocialPlan = async () => {
    const selectedReasonTitle = reasons?.find((r) => r.key === reason)?.title;
    const model = {
      reason: selectedReasonTitle,
      msg: desc,
      creatorID: Cookies.get('userID'),
    };
    await API.SOCIALPLANS.endSocialPlan(socialPlanId, model);
    handleClose();
    router.push(`/members/social-plans/${memberId}`);
  };

  return (
    <Modal open={open} onClose={handleClose}>
      <Box
        sx={{
          position: 'absolute',
          maxHeight: 'auto',
          minWidth: '40%',
          outline: 'none',
          borderRadius: '8px',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 850,
          padding: 2,
          bgcolor: 'background.paper',
          boxShadow: 24,
        }}
      >
        <div className="grid grid-flow-col m-0 p-0 relative auto-cols-auto justify-center justify-items-center items-center borderLine">
          <div className="grid grid-flow-row justify-center items-center">
            <p className="m-0 p-0 text-lg text-center">End Social Plan</p>
            <p className="m-0 p-0 text-center font-[500] text-sm">
              {capitalizeWords(memberName, true)}
            </p>
          </div>
          <Close
            onClick={handleClose}
            className="cursor-pointer absolute top-0 right-0 float-right w-8 h-8"
          />
        </div>
        <div className="grid grid-flow-row h-full">
          <div className=" pt-3 grid grid-flow-col items-center auto-cols-max gap-3 ">
            <SelectBox
              keyVal="reason"
              items={reasons}
              defaultValue={reason}
              minWidth={300}
              label="Reason"
              onChange={(e) => {
                setReason(e.target.value);
              }}
            />
          </div>
          <div className="borderLine">
            <TextField
              onChange={(e) => {
                setDesc(e.target.value);
              }}
              multiline
              variant="filled"
              minRows={10}
              className="w-full mt-2 font-[300] placeholder:text-[12px]"
              label="Enter a short description of what happened..."
            />
          </div>

          <Button
            onClick={handleEndSocialPlan}
            className="bg-primary border-none w-1/6  justify-self-end rounded-md hover:bg-primary hover:opacity-80 text-white mt-5"
          >
            <Check style={{ fontSize: 18, marginRight: 4 }} />
            Save
          </Button>
        </div>
      </Box>
    </Modal>
  );
};

export default EndSocialPlan;
