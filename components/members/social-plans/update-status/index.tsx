/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useContext, useState } from 'react';
import {
  Box, Button, Checkbox, Modal, TextField,
} from '@mui/material';
import { Check, Close } from '@mui/icons-material';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import SelectBox from 'components/select-box/select-box';
import API from 'api/src/lib/api';
import DateSelector from 'components/date-selector/date-selector';
import { dateToEpoch } from 'utils/helper';

interface SelectSocialPlanProps {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    network: any;
    navigatorId: string;
    socialPlanId: string;
    refresh: ()=> Promise<void>;
}

// eslint-disable-next-line react/function-component-definition
const UpdateNetworkStatus = ({
  open, setOpen, network, navigatorId, socialPlanId, refresh,

}: SelectSocialPlanProps) => {
  const statusTypes = useContext(ConstantsContext)?.timelineStatus;
  const [desc, setDesc] = useState('');
  const [dateTime, setDateTime] = useState('');
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [type, setType] = useState('');
  const handleClose = () => {
    setOpen(false);
    setType('');
    setSelectedServices([]);
  };

  const handleSelectCheckBox = (id: string) => {
    const updatedOptions:any = [...selectedServices];
    const optionIndex = updatedOptions
      .findIndex((option:any) => option === id);

    if (optionIndex !== -1) {
      updatedOptions.splice(optionIndex, 1);
    } else {
      updatedOptions.push(id);
    }
    // Update the state with the modified answers array.
    setSelectedServices(updatedOptions);
  };

  const handleUpdate = async () => {
    const model = {
      creatorID: navigatorId,
      appointmentEpoc: dateTime ? dateToEpoch(dateTime) : undefined,
      type,
      item: {
        status: type,
        desc,
        services: selectedServices,
      },
    };
    await API.SOCIALPLANS.updateSocialPlan(model, socialPlanId, network.id);
    handleClose();
    await refresh();
  };

  return (
    <Modal open={open} onClose={handleClose}>
      <Box sx={{
        position: 'absolute',
        maxHeight: 'auto',
        minWidth: '40%',
        outline: 'none',
        borderRadius: '8px',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 850,
        padding: 2,
        bgcolor: 'background.paper',
        boxShadow: 24,
      }}
      >
        <div className="grid grid-flow-col m-0 p-0 relative auto-cols-auto justify-center justify-items-center items-center borderLine">
          <div className="grid grid-flow-row justify-center items-center">
            <p className="m-0 p-0 text-lg text-center">Update Network Status</p>
            <p className="m-0 p-0 text-center font-[500] text-sm">{network?.network?.name}</p>
          </div>
          <Close onClick={handleClose} className="cursor-pointer absolute top-0 right-0 float-right w-8 h-8" />
        </div>
        <div
          className="grid grid-flow-row h-full"
        >
          <div className=" pt-3 grid grid-flow-col items-center auto-cols-max gap-3 ">
            <SelectBox
              keyVal="status"
              items={statusTypes}
              defaultValue={type}
              minWidth={300}
              label="Status"
              onChange={(e) => { setType(e.target.value); setDateTime(''); }}
            />
            {
            type === 'booked' && (
              <DateSelector
                time
                name="dateTime"
                pickerProps={{
                  label: 'Date & Time',
                } as any}
                onChange={(t) => {
                  setDateTime(t.target.value);
                }}
              />
            )
          }
          </div>

          {
            type === 'complete' && network.network.services.length > 0 && (
            <div className="pt-3">
              <p className="m-0 p-0 text-sm">What service did the member receive?</p>
              { network.network.services.map((service:any) => (
                <div className=" grid grid-flow-col items-center gap-1 auto-cols-max">
                  <Checkbox
                    onChange={() => handleSelectCheckBox(service.id)}
                    sx={
                  {
                    // checked color
                    '&.Mui-checked': {
                      color: '#00A343',
                    },
                  }
              }
                    color="success"
                  />
                  <p className="p-0 m-0 text-sm font-[400]">{service.name}</p>
                </div>
              ))}
            </div>
            )
          }
          <div className="borderLine">
            <TextField
              onChange={(e) => {
                setDesc(e.target.value);
              }}
              multiline
              variant="filled"
              minRows={10}
              className="w-full mt-2 font-[300] placeholder:text-[12px]"
              label="Enter a short description of what happened..."
            />
          </div>

          <Button
            onClick={handleUpdate}
            className="bg-primary border-none w-1/6  justify-self-end rounded-md hover:bg-primary hover:opacity-80 text-white mt-5"
          >
            <Check style={{ fontSize: 18, marginRight: 4 }} />
            Save
          </Button>
        </div>
      </Box>
    </Modal>
  );
};

export default UpdateNetworkStatus;
