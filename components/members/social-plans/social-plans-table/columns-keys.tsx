/* eslint-disable react/function-component-definition */
import { ScoreResponse } from 'hooks/use-score';
import type React from 'react';
import { NextRouter } from 'next/router';
import { SocialPlan } from 'types/social-plans-types';
import { TableHeader } from './type';

// eslint-disable-next-line import/prefer-default-export
export const socialPlansTableColumns = [
  {
    key: 'title',
    value: 'Title',
  },
  {
    key: 'type',
    value: 'Type',
  },
  {
    key: 'status',
    value: 'Status',
  },
  {
    key: 'services',
    value: 'Services',
  },
] as TableHeader[];

export interface CustomizeSocialPlanFieldsProps {
    field: string;
    children?: React.ReactNode;
    record?: SocialPlan;
    value: string | number;
    select?: boolean;
    // eslint-disable-next-line no-unused-vars
    getScoreTitle: (score: number) => ScoreResponse | null;
    router?: NextRouter;
  }
