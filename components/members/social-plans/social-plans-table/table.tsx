/* eslint-disable import/named */
import React, { useState, useEffect } from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import ArrowUpwardIcon from '@mui/icons-material/KeyboardArrowUp';
import ArrowDownwardIcon from '@mui/icons-material/KeyboardArrowDown';
import { Checkbox, Pagination } from '@mui/material';
import useScore from 'hooks/use-score';
import ActionContainer from 'components/action-container/action-container';
import { useModal } from 'contexts/modal-context/modal-context';
import API from 'api/src/lib/api';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useRouter } from 'next/router';

import CustomizeMemberFields from 'components/members/social-plans/social-plans-table/render-columns';
import { SocialPlan } from 'types/social-plans-types';
import { TableHeader } from './type';
import {
  StyledTableCell,
  StyledTableRow, borderlessTable,
  borderlessTableCell, borderlessTableContainer,
} from './styled-components';

interface SocialPlansProps {
  memberData: SocialPlan[];
  tableHeaders: TableHeader[];
  select?: boolean;
  selectedMembers?: SocialPlan[];
  insideModal?: boolean;
  setEditMember?: React.Dispatch<React.SetStateAction<SocialPlan>>;
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectedMembers?: React.Dispatch<React.SetStateAction<SocialPlan[]>>;
}

function SocialPlansTable({
  memberData, tableHeaders, select = false, selectedMembers, insideModal,
  setSelectedMembers, setEditMember, setOpen,
}: SocialPlansProps) {
  // Click handler for sorting
  const router = useRouter();
  const [sortedMembers, setSortedMembers] = useState<SocialPlan[]>(memberData);
  const [sortingDirection, setSortingDirection] = useState<'asc' | 'desc' | null>(null);
  const [sortedColumn, setSortedColumn] = useState<keyof SocialPlan | null>(null);
  const [page, setPage] = useState(1);
  const { showModal } = useModal();
  const { refresh } = useRefreshSSR();
  // eslint-disable-next-line no-unused-vars
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const { getScoreTitle } = useScore();
  // Click handler for sorting
  const handleHeadClick = (property: keyof SocialPlan) => {
    const newSortingDirection = sortingDirection === 'asc' ? 'desc' : 'asc';
    const newSortedMembers = [...sortedMembers].sort((a, b) => {
      if (a[property] < b[property]) return newSortingDirection === 'asc' ? -1 : 1;
      if (a[property] > b[property]) return newSortingDirection === 'asc' ? 1 : -1;
      return 0;
    });

    setSortedMembers(newSortedMembers);
    setSortingDirection(newSortingDirection);
    setSortedColumn(property);
  };
  const handleSelectedMembers = (item: SocialPlan) => {
    if (setSelectedMembers) {
      setSelectedMembers((prev) => {
        if (prev.includes(item)) {
          return prev.filter((items) => items !== item);
        }
        return [...prev, item];
      });
    }
  };

  const handleIsModalSelected = (item: SocialPlan) => {
    if (setSelectedMembers) {
      setSelectedMembers((prev) => prev.filter((items) => items.id !== item.id));
    }
    return false;
  };

  const handleDeleteMember = (item: SocialPlan) => {
    showModal('Warning', 'This process will permanently alter or delete data, settings, or configurations. Once initiated, there is no possibility of recovery or restoration. Are you sure you want to delete this member?', async () => {
      await API.MEMBERS.deleteMember(item.id);
      refresh();
    });
  };

  const handleOnEdit = (member: SocialPlan) => {
    if (setEditMember) setEditMember(member);
    if (setOpen) setOpen(true);
  };

  useEffect(() => {
    setSortedMembers(memberData);
    if (page > 1) {
      setPage(1);
    }
  }, [JSON.stringify(memberData)]);
  return (
    <TableContainer component={Paper} sx={borderlessTableContainer}>
      <Table aria-label="member table" sx={borderlessTable}>
        <TableHead>
          <TableRow>
            {tableHeaders.map(({ key, value }) => (
              <StyledTableCell
                key={key as string}
                onClick={() => handleHeadClick(key)}
              >
                <div className="grid grid-flow-col items-center auto-cols-min">
                  {value}
                  {sortedColumn === key && (
                    sortingDirection === 'asc' ? <ArrowUpwardIcon /> : <ArrowDownwardIcon />
                  )}
                </div>

              </StyledTableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {sortedMembers.slice((page - 1) * rowsPerPage, page * rowsPerPage)
            .map((member, index) => (
              <StyledTableRow key={member.id} index={index}>
                {tableHeaders.map(({ key }) => {
                  const value = member[key];
                  const isSelected = selectedMembers?.includes(member);
                  if ((key as any) === 'actions' && !select) {
                    return (
                      <TableCell key={key} style={{ padding: '8px 16px', ...borderlessTableCell }}>
                        {insideModal ? (
                          <ActionContainer
                            deleteLabel="Remove Member"
                            editLabel="Edit Member"
                            onDelete={() => {
                              handleIsModalSelected(member);
                            }}
                          />
                        )
                          : (
                            <ActionContainer
                              deleteLabel="Delete Member"
                              editLabel="Edit Member"
                              onDelete={() => {
                                handleDeleteMember(member);
                              }}
                              onEdit={() => {
                                handleOnEdit(member);
                              }}
                            />
                          )}
                      </TableCell>
                    );
                  }
                  return (
                    <TableCell key={key} style={{ padding: '8px 16px', ...borderlessTableCell }}>
                      <CustomizeMemberFields
                        key={key}
                        getScoreTitle={getScoreTitle}
                        router={router}
                        field={key}
                        record={member}
                        value={value}
                        select={select}
                      >
                        {select && (
                        <Checkbox
                          checked={isSelected}
                          onChange={() => handleSelectedMembers(member)}
                        />
                        )}
                      </CustomizeMemberFields>
                    </TableCell>
                  );
                })}
              </StyledTableRow>
            ))}
        </TableBody>
      </Table>
      <Pagination
        hideNextButton={sortedMembers.length <= rowsPerPage}
        hidePrevButton={sortedMembers.length <= rowsPerPage}
        count={Math?.ceil(sortedMembers.length / rowsPerPage)}
        page={page}
        onChange={(event, value) => setPage(value)}
        className="pt-5 align-middle"
      />
    </TableContainer>
  );
}

export default SocialPlansTable;
