import React from 'react';
import { CustomizeSocialPlanFieldsProps } from 'components/members/social-plans/social-plans-table/columns-keys';

const pBold = 'text-[14px] font-[500] m-1 p-0 text-[#001018] first-letter:uppercase';
const pSoft = 'text-[14px] font-[400] m-0 p-0 text-[#001018] first-letter:uppercase';

// eslint-disable-next-line react/function-component-definition
const CustomizeSocialPlanFields = ({
  field, record = {
    id: '',
    reciever: '',
    updatedAt: '',
    creator: '',
    status: '',
    type: '',
    org: {
      id: '',
    },
    reason: {
      id: '',
    },
    title: '',
    createdAt: '',
    items: [],
  }, value, router,
}: CustomizeSocialPlanFieldsProps) => {
  switch (field) {
    case 'title':
      return (
        // eslint-disable-next-line max-len
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
        <div
          className="cursor-pointer"
          onClick={() => {
            router?.push(`/members/social-plans/${record?.reciever}/${record.id}`);
          }}
        >
          <p key={field} className={pBold}>{value}</p>

        </div>
      );
    case 'type':
      return (
        <p key={field} className={pSoft}>{value}</p>
      );
    case 'services':
      return (
        <div className="flex items-center">
          <p className={pSoft}>{record.items.length}</p>
        </div>
      );
    default:
      return <p key={field} className={pBold}>{value}</p>;
  }
};
export default CustomizeSocialPlanFields;
