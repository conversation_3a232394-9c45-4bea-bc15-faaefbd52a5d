import { ISurveyItems } from 'types/members-response-type';
import React from 'react';
import { hexToRGB } from 'utils/helper';

interface ItimizeSurveyProps {
    labels: ISurveyItems[]
    // eslint-disable-next-line no-unused-vars
    getSurveyScore: (x:number) => {color:string, title:string};
    IconSet: any;
    Icon: React.ComponentType<any>;
}

function ItemizeSurvey({
  labels, getSurveyScore, IconSet, Icon,
}:ItimizeSurveyProps) {
  return (
    <div className="col-span-1 grid grid-cols-2 gap-3 justify-center auto-cols-min items-center">
      {labels.map((item, index) => (
        // eslint-disable-next-line react/no-array-index-key
        <div className="grid grid-cols-[50px,auto] items-center gap-2" key={item.type + index}>
          <div
            style={{
              backgroundColor: hexToRGB(getSurveyScore(item.score).color, 0.4),

            }}
            className="w-12 h-12 p-2 rounded-full grid items-center justify-center"
          >
            <Icon
              style={{
                color: getSurveyScore(item.score).color,
              }}
              component={IconSet[item.type]}
            />
          </div>
          <div>
            <p className="text-sm m-0 p-0 font-[500]">{item.title}</p>
            <p
              style={{
                color: getSurveyScore(item.score).color,
              }}
              className="text-xs m-0 p-0"
            >
              {`${getSurveyScore(item.score).title}(${item.score})`}
            </p>
          </div>
        </div>
      ))}
    </div>
  );
}

export default ItemizeSurvey;
