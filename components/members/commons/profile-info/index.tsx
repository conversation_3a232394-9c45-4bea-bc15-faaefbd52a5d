/* eslint-disable max-len */
import { <PERSON><PERSON>, Button } from '@mui/material';
import { ConstantContext } from 'contexts/constant-context/type';
import useScore from 'hooks/use-score';
import { Member } from 'types/members-response-type';
import {
  capitalizeWords,
  formatPhoneNumber,
  getKeyValue,
  getLatestImage,
} from 'utils/helper';
import SafeDate from 'components/date-time/SafeDate';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';

interface ProfileInfoProps {
  member: Member;
  constants: ConstantContext | undefined;
  handleEdit: () => void;
}

function ProfileInfo({ member, constants, handleEdit }: ProfileInfoProps) {
  const { getScoreTitle } = useScore();
  const getTextColor = (bgColor: string) => {
    // Convert hex to RGB
    const hex = bgColor.replace('#', '');
    const r = parseInt(hex.substring(0, 2), 16);
    const g = parseInt(hex.substring(2, 4), 16);
    const b = parseInt(hex.substring(4, 6), 16);

    // Calculate relative luminance (per W3C formula)
    const luminance = 0.2126 * r + 0.7152 * g + 0.0722 * b;

    return luminance < 150 ? 'text-white' : 'text-black';
  };

  return (
    <div className="flex gap-36 borderLine mb-8">
      <Avatar
        className="col-span-1 w-[240px] h-[240px]"
        style={{
          border: `4px solid ${
            getScoreTitle(parseInt(member.score, 10))?.color
          }`,
        }}
        alt="Profile Picture"
        src={getLatestImage(member.attachments, 'profile')}
        sx={{
          width: 210,
          height: 210,
          backgroundColor: '#8996A2',
        }}
      />
      <div className="flex flex-col w-full pb-5">
        <div className="flex justify-between items-center mb-3">
          <p className="font-[500] text-[32px] first-letter:uppercase m-0 whitespace-nowrap mr-[150px]">
            {capitalizeWords(`${member.firstName} ${member.lastName}`, true)}
          </p>
          <Button
            onClick={handleEdit}
            className="grey-btn rounded-md text-[#008390]"
            type="button"
            title="Edit"
          >
            <EditOutlinedIcon className="text-sm mr-2" />
            Edit Profile
          </Button>
        </div>
        <div className="flex flex-wrap gap-x-1 gap-y-1 max-w-full mb-7">
          {member?.tags?.map((tag) => (
            <div
              key={tag.key}
              className={`font-[400] text-[15px] py-[4px] px-[8px] rounded-[6px] ${getTextColor(
                tag.color,
              )} whitespace-nowrap`}
              style={{ backgroundColor: `${tag.color}` }}
            >
              {tag.name}
            </div>
          ))}
        </div>
        <div className="flex gap-[160px]">
          <div className="flex gap-5">
            <div className="text-[15px] font-[400] flex flex-col no-child-p-margin">
              <p>Member Type:</p>
              <p>Date of Birth:</p>
              <p>Gender:</p>
              <p>Gender Identity:</p>
              <p>Sexual Identity:</p>
              <p>Pronouns:</p>
              <p>Last Contact:</p>
            </div>
            <div className="text-[15px] font-[500] flex flex-col no-child-p-margin">
              <p>{getKeyValue(member.type, 'memberTypes', constants) || '-'}</p>
              <p>{member.dob ? <SafeDate date={member.dob} /> : '-'}</p>
              <p>
                {capitalizeWords(
                  getKeyValue(
                    member.gender,
                    'genderIndentity' as any,
                    constants,
                  ),
                ) || '-'}
              </p>
              <p>
                {capitalizeWords(
                  getKeyValue(
                    member.genderIdentity,
                    'genderIndentity' as any,
                    constants,
                  ),
                ) || '-'}
              </p>
              <p>
                {capitalizeWords(
                  getKeyValue(
                    member.sexualIdentity,
                    'sexualIdentities' as any,
                    constants,
                  ),
                  true,
                ) || '-'}
              </p>
              <p>
                {capitalizeWords(
                  getKeyValue(member.pronouns, 'pronouns' as any, constants),
                  true,
                ) || '-'}
              </p>
              <p>
                {member.lastContact ? (
                  <SafeDate date={member.lastContact} time />
                ) : (
                  '-'
                )}
              </p>
            </div>
          </div>
          <div className="flex gap-5">
            <div className="text-[15px] font-[400] flex flex-col no-child-p-margin">
              <p>Ethnicity:</p>
              <p>Military Status:</p>
              <p>Language:</p>
              <p>Email:</p>
              <p>Phone:</p>
              <p>Emergency Contact:</p>
            </div>
            <div className="text-[15px] font-[500] flex flex-col no-child-p-margin">
              <p>
                {member.ethnicity
                  ? capitalizeWords(member.ethnicity, true)
                  : '-'}
              </p>
              <p>
                {capitalizeWords(
                  getKeyValue(member.military, 'militaryStatus', constants),
                  true,
                ) || '-'}
              </p>
              <p>{member.lang ? capitalizeWords(member.lang, true) : '-'}</p>
              <p>{member.email || '-'}</p>
              <p>
                {member?.phones[0]?.number
                  ? formatPhoneNumber(member?.phones[0]?.number)
                  : '-'}
              </p>
              <p>
                {member?.meta?.contacts
                  ? capitalizeWords(
                    `${member?.meta?.contacts[0]?.type}, ${member?.meta?.contacts[0]?.name}, ${member?.meta?.contacts[0]?.phone}`,
                    true,
                  )
                  : '-'}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfileInfo;
