interface Org {
    id: string;
  }
interface Attachments {
    survey: {
        id: string;
    };
    household: {
        id: string;
    };
    animal: {
        id: string;
    };
    id: string;
    note: {
        id: string;
    };
    url: string;
    updatedAt: string;
    category: string;
    kind: string;
    type: string;
    user: {
        id: string;
    };
    org: {
        id: string;
    };
    createdAt: string;
    name: string;
}

interface HeadOfHouse {
    id: string;
  }
  interface Team {
    org: HeadOfHouse;
    id: string;
    name: string;
    createdAt: string;
    updatedAt: string;
  }

  interface HouseHold {
    updatedAt: string;
    headOfHouse: HeadOfHouse;
    id: string;
    createdAt: string;
    kind: string;
    type: string;
    teams: Team[];
    lastVisit: string;
    title: string;
    householdScore: string;
  }

export interface Member {
    name: string | undefined;
    image: string | undefined;
    schools: any[];
    status: string;
    score?: any;
    households: HouseHold[];
    lastAt?: any;
    sexualIdentity: string;
    genderIdentity: string;
    dob: string;
    phones: [];
    auth?: any;
    createdAt: string;
    id: string;
    lang: string;
    color: string;
    org: Org;
    gender: string;
    roles: string[];
    referredBy: string;
    attachments: Attachments[];
    pronouns: string;
    updatedAt: string;
    firstName: string;
    type: string;
    email: string;
    notes: any[];
    lastName: string;
    middleName: string;
    ethnicity: string;
    deliveryDate?: any;
    unenrolledDate?: any;
    military?: any;
    refId?: any;
    enrolledOn?: any;
    pregnancyStatus?: any;
    address?: any;
}
export interface TableHeader {
    key: keyof Member;
    value: string;
}

export interface MemberResponse {
    score?: any;
    lastName: string;
    org: Org;
    pronouns?: any;
    lang?: any;
    referredBy: string;
    address: any[];
    schools: any[];
    genderIdentity: string;
    middleName?: any;
    firstName: string;
    roles: string[];
    updatedAt: string;
    id: string;
    headOfHouse?: any;
    lastAt?: any;
    type: string;
    attachments: any[];
    status?: any;
    color: string;
    phones: any[];
    email: string;
    auth?: any;
    dob: string;
    households: any[];
    createdAt: string;
    gender: string;
    ethnicity: string;
    notes: any[];
    sexualIdentity?: any;
  }
