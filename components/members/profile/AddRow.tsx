import CustomTable from 'components/table-components/table';
import { capitalizeWords } from 'utils/helper';
import { useState, useEffect, useRef } from 'react';

function AddRow({
  items,
  setOpenAddItem,
  title,
  name,
  handleDelete,
  handleRemoveTeamPrimaryNavigator,
}: {
  items: any;
  setOpenAddItem: any;
  title: string;
  name: string;
  // eslint-disable-next-line no-unused-vars
  handleDelete: (id: any, type: string) => void;
  handleRemoveTeamPrimaryNavigator: () => void;
}) {
  const [sortedItems, setSortedItems] = useState<any[]>([]);
  const initializedRef = useRef(false);

  // 1) INITIALIZE ONCE (mount or when name changes)
  useEffect(() => {
    if (initializedRef.current) return; // only run once per mount/name
    if (name === 'medications') {
      setSortedItems([...items].sort((a, b) => {
        if (a.status === b.status) return 0;
        return a.status === 'discontinued' ? 1 : -1;
      }));
    } else {
      setSortedItems(items ?? []);
    }
    initializedRef.current = true;
  }, [name]); // deliberate: do not include `items` here

  // 2) MERGE INCOMING CHANGES WITHOUT RE-SORTING
  useEffect(() => {
    if (!initializedRef.current) return;
    if (name === 'medications') {
      setSortedItems((prev) => {
        const byId = new Map((items ?? []).map((it: any) => [it.id, it]));

        // update existing items in-place (preserve prev order)
        const updated = prev.map((p) => (byId.has(p.id) ? { ...p, ...byId.get(p.id)! } : p));

        // append any new items that parent added (preserve parent's order for new ones)
        const prevIds = new Set(prev.map((p) => p.id));
        const extras = (items ?? []).filter((it: any) => !prevIds.has(it.id));
        return extras.length ? [...updated, ...extras] : updated;
      });
    } else {
      // for non-medications we just mirror parent
      setSortedItems(items ?? []);
    }
  }, [items, name]);

  const singularTitle = () => {
    if (title === 'Diagnoses') return 'Diagnosis';
    if (title === 'Problems List') return 'Problem';
    if (title === 'Emergency Contact & Caregivers') return 'Contact';
    return title.replace(/s$/, '');
  };

  const getColumnWidths = (type: string) => {
    const defaultWidths = {
      firstColumnStaticWidth: 30,
      secondColumnStaticWidth: 50,
      thirdColumnStaticWidth: 16,
      fourthColumnStaticWidth: 4,
    };

    switch (type) {
      case 'diagnoses':
        return {
          firstColumnStaticWidth: 30,
          secondColumnStaticWidth: 50,
          thirdColumnStaticWidth: 16,
          fourthColumnStaticWidth: 4,
        };
      case 'insurances':
        return {
          firstColumnStaticWidth: 30,
          secondColumnStaticWidth: 15,
          thirdColumnStaticWidth: 15,
          fourthColumnStaticWidth: 20,
          fifthColumnStaticWidth: 16,
          sixthColumnStaticWidth: 4,
        };
      case 'contacts':
      case 'medications':
        return {
          firstColumnStaticWidth: 30,
          secondColumnStaticWidth: 30,
          thirdColumnStaticWidth: 20,
          fourthColumnStaticWidth: 16,
          fifthColumnStaticWidth: 4,
        };
      case 'teams':
        return {
          firstColumnStaticWidth: 30,
          secondColumnStaticWidth: 30,
          thirdColumnStaticWidth: 36,
          fourthColumnStaticWidth: 4,
        };
      default:
        return defaultWidths;
    }
  };

  const columnWidths = getColumnWidths(name);

  return (
    <>
      <div className="flex items-center mt-2 mt-[48px] mb-2 justify-between">
        <p className="font-[500] text-[18px] m-0 p-0">{title}</p>
        {items?.length > 0 && (
          <div className="flex items-center">
            {name === 'teams' && (
              <div className="flex items-center">
                <button
                  type="button"
                  onClick={() => {
                    handleRemoveTeamPrimaryNavigator();
                  }}
                  className="text-[#E42B57] font-[500] text-[15px] cursor-pointer hover:text-[#E42B57]/80 m-0 p-0 border-0 bg-transparent pb-[2px]"
                >
                  Remove Team
                </button>
                <span className="text-[#DCDFDF] m-0 p-0 mx-2">|</span>
              </div>
            )}
            <button
              type="button"
              onClick={() => {
                setOpenAddItem(true);
              }}
              className="text-primary font-[500] text-[15px] cursor-pointer hover:text-primary/80 m-0 p-0 border-0 bg-transparent pb-[2px]"
            >
              {name === 'teams'
                ? '+ Assign to New Team'
                : `+ Add ${capitalizeWords(singularTitle().toLowerCase())}`}
            </button>
          </div>
        )}
      </div>
      {items?.length === 0 ? (
        <div className="flex items-center gap-2">
          <p className="text-[15px] m-0 p-0">
            No
            {' '}
            {title.toLowerCase()}
            {' '}
            {name === 'team' ? 'has' : 'have'}
            {' '}
            been
            added to this care plan.
          </p>
          <button
            type="button"
            onClick={() => {
              setOpenAddItem(true);
            }}
            className="text-primary font-[500] text-[15px] cursor-pointer hover:text-primary/80 m-0 p-0 border-0 bg-transparent pb-[2px]"
          >
            + Add
            {' '}
            {capitalizeWords(singularTitle().toLowerCase())}
          </button>
        </div>
      ) : (
        <CustomTable
          variant={`profile-${name}-table`}
          source={sortedItems}
          className="mt-3"
          style={{
            border: 'solid 1px lightgray',
            borderRadius: '10px',
            borderBottom: 'none',
          }}
          headCellStyle={{
            padding: '4px 16px',
            fontSize: '13px',
            color: '#747A7A',
          }}
          tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
          fixedLayout // enables fixed layout + truncation
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...columnWidths}
          handleDelete={(id) => handleDelete(id, name)}
        />
      )}
    </>
  );
}

export default AddRow;
