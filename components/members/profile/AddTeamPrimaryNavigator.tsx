/* eslint-disable jsx-a11y/no-static-element-interactions */
import {
  Modal,
  Button,
  RadioGroup,
  FormControlLabel,
  Radio,
  Pagination,
} from '@mui/material';
import ControlledSearch from 'components/search/controlled-search';
import React, {
  useEffect, useState, useRef,
} from 'react';
import CheckIcon from '@mui/icons-material/Check';
import Close from '@mui/icons-material/Close';
import { ArrowBack, ArrowForward } from '@mui/icons-material';
import API, { ORG_ID, TOKEN } from 'api/src/lib/api';
import { getLatestImage, capitalizeWords } from 'utils/helper';
import { StyledAvatar } from 'components/table-components/styled-components';

interface AddTeamPrimaryNavigatorProps {
  open: boolean;
  setOpen: (value: boolean) => void;
  handleAddTeamPrimaryNavigator: (selectedTeam: any, selectedPrimaryNavigator: any) => void;
  team: any;
  primaryNavigator: any;
}

function AddTeamPrimaryNavigator({
  open,
  setOpen,
  handleAddTeamPrimaryNavigator,
  team,
  primaryNavigator,
}: AddTeamPrimaryNavigatorProps) {
  const backdropRef = useRef<HTMLDivElement | null>(null);
  const mouseDownOnBackdrop = useRef(false);

  const [step, setStep] = useState(1);
  const [selectedTeam, setSelectedTeam] = useState<any>(null);
  const [selectedPrimaryNavigator, setSelectedPrimaryNavigator] = useState<any>(null);

  // pagination + teams state
  const [teamsList, setTeamsList] = useState<any[]>([]);
  const [page, setPage] = useState(1);
  const per = 8;
  const [total, setTotal] = useState(0);
  const [search, setSearch] = useState('');
  const fetchTeams = async () => {
    const response: any = await API.TEAMS.fetchTeams(
      TOKEN(),
      ORG_ID(),
      per,
      page,
      'name',
      'asc',
      search,
    );
    setTeamsList(response?.items || []);
    setTotal(response?.metadata?.total || 0);
  };
  useEffect(() => {
    if (open) fetchTeams();
    else {
      setTeamsList([]);
      setPage(1);
      setSearch('');
    }
  }, [open, page, search]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(e.target.value.toLowerCase());
    setPage(1); // reset to first page when searching
  };

  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div
        ref={backdropRef}
        className="fixed inset-0 flex items-center justify-center p-4 z-50"
        onMouseDown={(e) => {
          mouseDownOnBackdrop.current = e.target === e.currentTarget;
        }}
        onMouseUp={(e) => {
          if (mouseDownOnBackdrop.current && e.target === e.currentTarget) {
            setOpen(false);
          }
        }}
      >
        <div
          className="flex flex-col h-[95vh] w-[90%] bg-white rounded-lg overflow-hidden"
          // onClick={(e) => e.stopPropagation()}
          onKeyDown={(e) => {
            if (e.key === 'Enter') {
              e.preventDefault();
              setOpen(false);
            }
          }}
          tabIndex={0}
        >
          {/* Header */}
          <div
            style={{ borderBottom: '1px solid #E0E0E0' }}
            className="h-[64px] shrink-0 items-center relative justify-center grid grid-flow-col border border-black"
          >
            <div className="grid grid-flow-row p-4 items-center text-center">
              <p className="m-0 font-[500] text-[18px]">
                Assign Team & Primary Navigator
              </p>
            </div>
            <Close
              className="absolute right-5 cursor-pointer"
              onClick={() => setOpen(false)}
            />
          </div>

          {/* Body */}
          <div className="relative flex flex-col flex-grow min-h-0 px-[40px] pt-4 pb-5 overflow-y-auto overflow-x-hidden">
            <p className="text-[15px] font-[500] text-[#747A7A] mt-1 mb-2">
              Step
              {' '}
              {step}
              {' '}
              of 2
            </p>

            <div
              className="relative flex-grow flex flex-col"
              style={{ minHeight: '300px' }}
            >
              {/* Step 1 – Search and Select Team */}
              <div
                className="absolute w-full h-full transition-all duration-200 ease flex flex-col"
                style={{
                  transform: step === 1 ? 'translateX(0%)' : 'translateX(-3%)',
                  opacity: step === 1 ? 1 : 0,
                  zIndex: step === 1 ? 2 : 1,
                }}
              >
                <p className="m-0 p-0 font-[600] text-lg">Select Team</p>
                <div className="flex gap-x-5 gap-y-3 mt-[10px] flex-wrap">
                  <div className="w-[400px] ml-[1px]">
                    <ControlledSearch
                      placeholder="Search teams"
                      onChange={handleSearch}
                      className="h-[56px]"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <RadioGroup
                    value={selectedTeam?.id || ''}
                    onChange={(_, value) => {
                      const teamObj = teamsList.find(
                        (t: any) => t.id === value,
                      );
                      setSelectedTeam(teamObj || null);
                    }}
                  >
                    {teamsList?.map((team: any) => {
                      const isSelected = team.id === selectedTeam?.id;
                      return (
                        <div
                          key={team.id}
                          className="flex items-center justify-between py-[10px]"
                          style={{ borderBottom: '1px solid #E0E0E0' }}
                        >
                          <FormControlLabel
                            value={team.id}
                            control={(
                              <Radio
                                sx={{
                                  color: 'gray',
                                  '&.Mui-checked': {
                                    color: 'transparent',
                                    '& svg': { fill: 'url(#radioGradient)' },
                                  },
                                }}
                              />
                          )}
                            label={(
                              <div className="flex items-center">
                                <span className={isSelected ? 'font-[500]' : ''}>
                                  {team.name}
                                </span>
                              </div>
                            )}
                          />

                          {/* right side content */}
                          <div className="flex items-center">
                            {team?.navigators?.map((navigator: any) => (
                              <StyledAvatar
                                key={navigator.id}
                                src={getLatestImage(
                                  navigator.attachments,
                                  'userProfile',
                                )}
                                style={{
                                  width: '30px',
                                  height: '30px',
                                  marginRight: '5px',
                                }}
                              />
                            ))}
                          </div>
                        </div>
                      );
                    })}
                  </RadioGroup>
                </div>
                {teamsList?.length > 0 && (
                  <div className="mt-4">
                    <Pagination
                      count={Math.ceil(total / per)}
                      page={page}
                      onChange={(_, value) => {
                        setPage(value);
                      }}
                      className="pt-5 align-middle"
                    />
                  </div>
                )}
              </div>

              {/* Step 2 – Search and Select Primary Navigator */}
              <div
                className="absolute w-full h-full transition-all duration-200 ease"
                style={{
                  transform: step === 2 ? 'translateX(0%)' : 'translateX(3%)',
                  opacity: step === 2 ? 1 : 0,
                  zIndex: step === 2 ? 2 : 1,
                }}
              >
                <p className="m-0 p-0 font-[600] text-lg">
                  Select Primary Navigator
                </p>
                <div className="flex gap-x-5 gap-y-3 mt-[10px] flex-wrap">
                  <div className="w-[400px] ml-[1px]">
                    <ControlledSearch
                      placeholder="Search navigators"
                      onChange={handleSearch}
                      className="h-[56px]"
                    />
                  </div>
                </div>
                <div className="mt-4">
                  <RadioGroup
                    value={selectedPrimaryNavigator?.id || ''}
                    onChange={(_, value) => {
                      const navigatorObj = selectedTeam?.navigators.find(
                        (t: any) => t.id === value,
                      );
                      setSelectedPrimaryNavigator(navigatorObj || null);
                    }}
                  >
                    {selectedTeam?.navigators?.map((navigator: any) => {
                      const isSelected = navigator.id === selectedPrimaryNavigator?.id;

                      return (
                        <div
                          key={navigator.id}
                          className="flex items-center justify-between py-[10px]"
                          style={{ borderBottom: '1px solid #E0E0E0' }}
                        >
                          <FormControlLabel
                            value={navigator.id}
                            control={(
                              <Radio
                                sx={{
                                  color: 'gray',
                                  '&.Mui-checked': {
                                    color: 'transparent',
                                    '& svg': { fill: 'url(#radioGradient)' },
                                  },
                                }}
                              />
          )}
                            label={(
                              <div className="flex items-center">
                                <StyledAvatar
                                  src={getLatestImage(navigator.attachments, 'userProfile')}
                                  style={{ width: '30px', height: '30px' }}
                                />
                                <span className={isSelected ? 'font-[500]' : ''}>
                                  {capitalizeWords(`${navigator.firstName} ${navigator.lastName}`, true)}
                                </span>
                              </div>
          )}
                          />
                        </div>
                      );
                    })}
                  </RadioGroup>

                </div>
              </div>
            </div>
          </div>

          {/* Footer */}
          <div
            id="footer-area"
            style={{
              borderTop: '1px solid #E0E0E0',
              zIndex: 2,
              display: 'flex',
            }}
            className="shrink-0 bg-white border-t border-gray-300 flex items-center justify-between p-3"
          >
            {step > 1 ? (
              <Button
                className="rounded-md m-0 px-[14px] text-[15px] flex items-center justify-center !min-w-[100px] text-[#262D2D] font-[500] bg-gradient-to-b from-[#F7F8F8] to-[#DCDFDF] hover:bg-gradient-to-b hover:from-[#F9FAFA] hover:to-[#D0D3D3]"
                onClick={() => setStep(step - 1)}
              >
                <ArrowBack className="mr-3 w-[18px] h-[18px]" />
                <p className="font-[500] m-0 p-0 pt-[1px]">Previous</p>
              </Button>
            ) : (
              <div />
            )}
            <Button
              variant="contained"
              className={`bg-[#2D62ED] text-white rounded-md !min-w-fit ${
                step === 1 && !selectedTeam && 'opacity-50'
              }`}
              disabled={step === 1 && !selectedTeam}
              onClick={() => {
                if (step === 1) {
                  setStep(step + 1);
                } else {
                  handleAddTeamPrimaryNavigator(selectedTeam, selectedPrimaryNavigator);
                  setOpen(false);
                }
              }}
            >
              <div className="grid grid-flow-col items-center gap-2">
                {step === 2 && <CheckIcon />}
                {step === 1 ? 'Continue' : 'Save'}
                {step === 1 ? <ArrowForward /> : <div />}
              </div>
            </Button>
          </div>
        </div>

        {/* Gradient Definitions */}
        <svg width="0" height="0">
          <defs>
            <linearGradient
              id="radioGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop
                offset="0%"
                style={{ stopColor: '#FD8205', stopOpacity: 1 }}
              />
              <stop
                offset="100%"
                style={{ stopColor: '#E97100', stopOpacity: 1 }}
              />
            </linearGradient>
          </defs>
        </svg>
      </div>
    </Modal>
  );
}

export default AddTeamPrimaryNavigator;
