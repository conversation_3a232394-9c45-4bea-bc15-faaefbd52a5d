/* eslint-disable no-shadow */
/* eslint-disable react/jsx-props-no-spreading */

import {
  Box, Button, Modal, Checkbox,
} from '@mui/material';
import { Check, Close } from '@mui/icons-material';
import React, { useEffect, useState } from 'react';
import { DragDropContext, Droppable, Draggable } from 'react-beautiful-dnd';
import { columns } from 'components/table-components/columns-renders';
import API from 'api/src/lib/api';
import useAuth from 'hooks/use-auth';
import { useRouter } from 'next/router';
import { useLoading } from 'contexts/loading-context/loading-context';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';

const initialKeys = [
  'firstName', 'dob', 'status', 'household', 'teams', 'score', 'gender',
  'genderIdentity', 'pronouns', 'sexualIdentity', 'ethnicity',
  'lang', 'military', 'email', 'quickNotes', 'lastContact',
  'referredBy',
];
const initialColumns = ['firstName', 'dob', 'status', 'household', 'teams', 'score'];
function ConfigureMemberColumns() {
  const [open, setOpen] = useState(false);
  const [keys, setKeys] = useState(initialKeys);
  const { user } = useAuth();
  const { showLoading } = useLoading();
  const router = useRouter();
  const [checkedItems, setCheckedItems] = useState<string[]>([]);
  const handleClose = () => {
    setOpen(false);
  };

  const onDragEnd = (result:any) => {
    if (!result.destination) return;

    const reorderedKeys = Array.from(keys);
    const [removed] = reorderedKeys.splice(result.source.index, 1);
    reorderedKeys.splice(result.destination.index, 0, removed);

    setKeys(reorderedKeys);

    // Reorder checkedItems according to new keys order
    const newCheckedItems = reorderedKeys.filter((key) => checkedItems.includes(key));
    setCheckedItems(newCheckedItems);
  };

  const handleCheckboxChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = event.target;
    setCheckedItems((prev) => (checked ? [...prev, name] : prev.filter((item) => item !== name)));
  };
  const handleOnSave = async () => {
    if (user) {
      showLoading();
      const updatedMeta:any = await API.USER.updateUser(user?.id, {
        meta: {
          ...user?.meta,
          data: {
            ...user?.meta?.data,
            'main-member-table': JSON.stringify(checkedItems),
          },
        },
      } as any);
      localStorage.setItem('member-config', JSON.stringify(updatedMeta?.meta?.data || []));
      await router.reload();
    }
  };

  useEffect(
    () => {
      if (user) {
        const config = JSON.parse(user?.meta?.data['main-member-table'] || '[]');
        setCheckedItems(config || initialColumns);
      }
    },
    [user],
  );

  return (
    <div>
      <Button
        onClick={() => setOpen(true)}
        style={{ fontWeight: 500, backgroundColor: '#F7F8F8' }}
        className="rounded-md pl-4 grey-btn text-[#E97100] font-[500]"
      >
        <EditOutlinedIcon
          style={{ width: '16px', height: '16px' }}
          className="mr-2"
        />
        <span className="p-0 m-0">Configure Columns</span>
      </Button>
      <Modal
        open={open}
        onClose={handleClose}
        sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }} // Centering modal using flexbox
      >
        <Box sx={{
          position: 'relative',
          width: 550,
          padding: 2,
          bgcolor: 'background.paper',
          boxShadow: 24,
          borderRadius: '8px',
          maxHeight: '90vh',
          overflow: 'auto',
        }}
        >
          <div className="grid grid-flow-col m-0 p-0 relative auto-cols-auto justify-center justify-items-center items-center borderLine">
            <div className="grid grid-flow-row justify-center items-center">
              <p className="m-0 p-0 text-lg text-center">Configure Columns</p>
            </div>
            <Close onClick={handleClose} className="cursor-pointer absolute top-0 right-0 float-right w-8 h-8" />
          </div>
          <div className="w-full grid grid-flow-row auto-rows-auto" style={{ maxHeight: '60vh', overflowY: 'auto' }}>
            <DragDropContext onDragEnd={onDragEnd}>
              <Droppable droppableId="keys">
                {(provided) => (
                  <div {...provided.droppableProps} ref={provided.innerRef}>
                    {keys
                      .sort((a, b) => {
                        // Get the indices of 'a' and 'b' in checkedItems
                        const indexA = checkedItems.findIndex((item) => item === a);
                        const indexB = checkedItems.findIndex((item) => item === b);

                        // If 'a' or 'b' is not in checkedItems, they should be placed last
                        const adjustedIndexA = indexA === -1 ? Infinity : indexA;
                        const adjustedIndexB = indexB === -1 ? Infinity : indexB;

                        // Compare the indices
                        return adjustedIndexA - adjustedIndexB;
                      })
                      .map((key, index) => (
                        <Draggable key={key} draggableId={key} index={index}>
                          {(provided) => (
                            <div
                              ref={provided.innerRef}
                              {...provided.draggableProps}
                              {...provided.dragHandleProps}
                              className="bg-icon-drag bg-left bg-no-repeat pl-4 rounded-md flex items-center"
                            >

                              <Checkbox
                                name={key}
                                className="text-sm"
                                checked={checkedItems.includes(key)}
                                onChange={handleCheckboxChange}
                                color="primary"
                              />
                              <p className="m-0 p-0 text-sm">{columns.find((column) => column.key === key)?.value}</p>
                            </div>
                          )}
                        </Draggable>
                      ))}
                    {provided.placeholder}
                  </div>
                )}
              </Droppable>
            </DragDropContext>
          </div>
          <div className="grid grid-flow-col auto-cols-auto h-full">
            <Button
              className="bg-[#ECEEED] border-none w-1/2 h-[40px] justify-self-start rounded-md hover:bg-[#ECEEED] hover:opacity-80 text-[#008390] mt-5"
              onClick={() => {
                setCheckedItems(initialColumns);
              }}
            >

              Restore Defaults
            </Button>
            <Button
              className="bg-primary border-none w-1/2 h-[40px] justify-self-end rounded-md hover:bg-primary hover:opacity-80 text-white mt-5"
              onClick={handleOnSave}
            >
              <Check style={{ fontSize: 18, marginRight: 4 }} />
              Save
            </Button>

          </div>
        </Box>
      </Modal>
    </div>
  );
}

export default ConfigureMemberColumns;
