import React, { useState, useEffect } from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import ArrowUpwardIcon from '@mui/icons-material/KeyboardArrowUp';
import ArrowDownwardIcon from '@mui/icons-material/KeyboardArrowDown';
import { Checkbox, Pagination } from '@mui/material';

import CustomizeServiceFields from 'components/networks/service-table/render-columns';
import ActionContainer from 'components/action-container/action-container';
import {
  StyledTableCell,
  StyledTableRow, borderlessTable,
  borderlessTableCell, borderlessTableContainer,
} from './styled-components';
import { Service, TableHeader } from './type';

interface ServiceTableProps {
insideModal?: boolean;
serviceData: Service[];
tableHeaders: TableHeader[];
select?: boolean;
selectedServices?: Service[];
setSelectedServices?: React.Dispatch<React.SetStateAction<Service[]>>;
}

function ServiceTable({
  serviceData, tableHeaders, select = false,
  setSelectedServices, selectedServices, insideModal,
}: ServiceTableProps) {
// Click handler for sorting
  const [sortedServices, setSortedServices] = useState<Service[]>(serviceData || []);
  const [sortingDirection, setSortingDirection] = useState<'asc' | 'desc' | null>(null);
  const [sortedColumn, setSortedColumn] = useState<keyof Service | null>(null);
  const [page, setPage] = useState(1);
  // eslint-disable-next-line no-unused-vars
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const handleHeadClick = (property: keyof Service) => {
    const newSortingDirection = sortingDirection === 'asc' ? 'desc' : 'asc';
    const newSortedServices = [...sortedServices].sort((a, b) => {
      if (a[property] && b[property]) {
        if (a[property]! < b[property]!) return newSortingDirection === 'asc' ? -1 : 1;
        if (a[property]! > b[property]!) return newSortingDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    setSortedServices(newSortedServices);
    setSortingDirection(newSortingDirection);
    setSortedColumn(property);
  };

  const handleSelectedServices = (item: Service) => {
    if (setSelectedServices) {
      setSelectedServices((prev) => {
        if (prev.includes(item)) {
          return prev.filter((items) => items !== item);
        }
        return [...prev, item];
      });
    }
  };
  const handleIsModalSelected = (item: Service) => {
    if (setSelectedServices) {
      setSelectedServices((prev) => prev.filter((items) => {
        if (items !== item) {
          return items;
        }
        return false;
      }));
    }
    return false;
  };

  useEffect(() => {
    setSortedServices(serviceData);
    if (page > 1) {
      setPage(1);
    }
  }, [JSON.stringify(serviceData)]);

  return (
    <TableContainer component={Paper} sx={borderlessTableContainer}>
      <Table aria-label="service table" sx={borderlessTable}>
        <TableHead>
          <TableRow>
            {tableHeaders.map(({ key, value }) => (
              <StyledTableCell
                key={key as string}
                onClick={() => handleHeadClick(key)}
              >
                <div className="grid grid-flow-col items-center auto-cols-min">
                  {value}
                  {sortedColumn === key && (!Array.isArray(value)) && (
                    sortingDirection === 'asc' ? <ArrowUpwardIcon /> : <ArrowDownwardIcon />
                  )}
                </div>
              </StyledTableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {sortedServices.slice((page - 1) * rowsPerPage, page * rowsPerPage)
            .map((service, index) => (
              // eslint-disable-next-line react/no-array-index-key
              <StyledTableRow key={index} index={index}>
                {tableHeaders.map(({ key }) => {
                  if ((key as any) === 'actions') {
                    return (
                      <TableCell key={key} style={{ padding: '8px 16px', ...borderlessTableCell }}>
                        {insideModal && (
                          <ActionContainer
                            deleteLabel="Remove Service"
                            editLabel="Edit Service"
                            onDelete={() => {
                              handleIsModalSelected(service);
                            }}
                          />
                        )}
                      </TableCell>
                    );
                  }
                  const value = service[key];
                  const isSelected = selectedServices?.includes(service);
                  return (
                    <TableCell key={key} style={{ padding: '2px 16px', ...borderlessTableCell }}>
                      <div className="grid grid-flow-col items-center justify-start">
                        <CustomizeServiceFields
                          field={key}
                          record={service}
                          value={value as any}
                          select={select}
                        >
                          {select && (
                          <Checkbox
                            checked={isSelected}
                            onChange={() => handleSelectedServices(service)}
                          />
                          )}
                        </CustomizeServiceFields>
                      </div>
                    </TableCell>
                  );
                })}
              </StyledTableRow>
            ))}
        </TableBody>
      </Table>
      <Pagination
        hideNextButton={sortedServices.length <= rowsPerPage}
        hidePrevButton={sortedServices.length <= rowsPerPage}
        count={Math.ceil(sortedServices.length / rowsPerPage)}
        page={page}
        onChange={(event, value) => setPage(value)}
        className="pt-5 align-middle"
      />
    </TableContainer>
  );
}

export default ServiceTable;
