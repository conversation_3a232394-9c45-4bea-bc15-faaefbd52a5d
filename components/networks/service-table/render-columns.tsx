/* eslint-disable react/jsx-one-expression-per-line */
import { CustomizeServiceFieldsProps } from 'components/networks/service-table/columns-keys';
import React from 'react';

const pStyle = 'text-[15px] font-[400] text-[#001018] first-letter:uppercase';

// eslint-disable-next-line react/function-component-definition
const CustomizeServiceFields: React.FC<CustomizeServiceFieldsProps> = ({
  field,
  value,
  // record,
}: CustomizeServiceFieldsProps) => {
  switch (field) {
    case 'name':
      return <p className="text-[15px]  text-[#001018] font-[500]">{value}</p>;
    case 'service':
      return <p className={pStyle}>{value}</p>;
    case 'desc':
      return <p className={pStyle}>{value}</p>;
    default:
      return <p className={pStyle}>.</p>;
  }
};
export default CustomizeServiceFields;
