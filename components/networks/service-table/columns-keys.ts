/* eslint-disable react/function-component-definition */
// import { ScoreResponse } from 'hooks/use-score';
import { Service, TableHeader } from 'components/networks/service-table/type';
import type React from 'react';

// eslint-disable-next-line import/prefer-default-export
export const serviceTableColumns = [
  { key: 'name', value: 'Name' },
  { key: 'service', value: 'Service' },
  { key: 'desc', value: 'Description' },
  { key: 'actions', value: '' },

] as TableHeader[];

export interface CustomizeServiceFieldsProps {
field: string;
children?: React.ReactNode;
record?: Service;
value: string | number;
select?: boolean;
// eslint-disable-next-line no-unused-vars
// getScoreTitle: (score: number) => ScoreResponse | null;
}
