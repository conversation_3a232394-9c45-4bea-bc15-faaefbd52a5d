import React, { useEffect, useState } from 'react';
import { Network } from 'api/src/types';
import { Member } from 'components/members/members-table/type';
import CustomTable from 'components/table-components/table';
import { Button } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import Modal from '@mui/material/Modal';
import Search from 'components/search/search';
import { Close } from '@mui/icons-material';
import API from 'api/src/lib/api';
import Cookies from 'js-cookie';

interface SelectNetworkProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  networkType?: string;
  selectedNetworks?: any[];
  setSelectNetworks?: React.Dispatch<React.SetStateAction<Network[]>>;
  member?: Member;
}

function SelectNetwork({
  open,
  setOpen,
  setSelectNetworks,
  selectedNetworks,
  networkType = '',
  member,
}: SelectNetworkProps) {
  const [networks, setNetworks] = useState<Network[]>([]);
  const [networkData, setNetworkData] = useState<Network[]>([]);
  const pageInfo = {
    per: 10,
    total: 0,
    page: 1,
  };

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.value === '') {
      setNetworkData(networks || []);
      return;
    }
    // eslint-disable-next-line max-len
    const filteredNetworks = networks.filter((network: Network) => network.name.toLowerCase().includes(e.target.value.toLowerCase()));
    setNetworkData(filteredNetworks);
  };

  // Haversine formula to calculate distance in miles
  function haversineDistanceMiles(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const toRadians = (degree: number) => (degree * Math.PI) / 180;

    const R = 3958.8; // Radius of Earth in miles

    const dLat = toRadians(lat2 - lat1);
    const dLon = toRadians(lon2 - lon1);

    const a = Math.sin(dLat / 2) ** 2
      + Math.cos(toRadians(lat1))
        * Math.cos(toRadians(lat2))
        * Math.sin(dLon / 2) ** 2;
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c; // distance in miles
  }

  useEffect(() => {
    const fetchNetworks = async () => {
      const response = (await API.NETWORKS.fetchNetworksNoPage(
        undefined,
        Cookies.get('orgID'),
        networkType,
      )) as any;

      // Remove duplicates by ID
      const seen = new Set<string>();
      const uniqueNetworks = (response || []).filter((network: any) => {
        if (seen.has(network.id)) return false;
        seen.add(network.id);
        return true;
      });

      // Calculate the distance for each network
      const withDistances = uniqueNetworks.map((network: Network) => {
        const memberLat = member?.address[0]?.lat;
        const memberLon = member?.address[0]?.lon;
        const networkLat = network?.address[0]?.lat;
        const networkLon = network?.address[0]?.lon;

        let distanceToMember = null;

        if (
          memberLat != null
          && memberLon != null
          && networkLat != null
          && networkLon != null
        ) {
          distanceToMember = Math.round(
            haversineDistanceMiles(
              memberLat,
              memberLon,
              networkLat,
              networkLon,
            ) * 10,
          ) / 10;
        }

        return {
          ...network,
          distanceToMember,
        };
      });

      setNetworks(withDistances); // Full list of networks
      setNetworkData(withDistances); // Filtered list with distance info
    };

    fetchNetworks();
  }, [networkType, member]);

  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div className="bg-white m-[4%] mx-[10%] relative mb-10 rounded-lg h-[84%] flex flex-col overflow-hidden">
        <div className="h-[54px] flex items-center justify-center border-b border-gray-300 relative">
          <p>Select Network(s) to Add to Team</p>
          <Close
            className="absolute right-5 cursor-pointer"
            onClick={() => setOpen(false)}
          />
        </div>
        <div className="py-3 flex items-center justify-center border-b border-gray-300">
          <Search placeholder="Search networks..." onChange={handleSearch} />
        </div>
        <div className="flex-grow overflow-auto mb-[59px]">
          <CustomTable
            source={networkData}
            variant="select-network-table"
            select
            selected={selectedNetworks}
            setSelected={setSelectNetworks}
            metadata={{
              list: selectedNetworks,
              setSelected: setSelectNetworks,
            }}
            tableCellStyle={{
              padding: '0px',
              paddingLeft: '13px',
            }}
            headCellStyle={{
              borderBottom: 'solid 1px #DCDFDF',
              padding: '6px 13px',
            }}
            pageInfo={pageInfo}
          />
        </div>
        <div
          id="footer-area"
          style={{
            borderTop: '1px solid #E0E0E0',
            position: 'absolute',
            width: '100%',
            zIndex: 2,
            bottom: 0,
          }}
          className="h-[60px] shrink-0 bg-white border-t border-gray-300 flex items-center justify-end px-5"
        >
          <Button
            onClick={() => {
              setOpen(false);
            }}
            variant="contained"
            className="bg-[#2D62ED] text-white rounded-md w-[122px]"
          >
            <div className="flex items-center gap-2">
              <CheckIcon />
              Save
            </div>
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default SelectNetwork;
