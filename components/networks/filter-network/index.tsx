import React, { useContext, useState } from 'react';
import { Close } from '@mui/icons-material';
import {
  Button, Dialog, DialogContent,
  TextField,
} from '@mui/material';
import SelectBox from 'components/select-box/select-box';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useFormik } from 'formik';
import API from 'api/src/lib/api';
import { makeQueryString } from 'utils/helper';
// import { useFormik } from 'formik';

interface FilterNetworkButtonProps {
    setData: React.Dispatch<React.SetStateAction<any[]>>;
}
function FilterNetworkButton({ setData }: FilterNetworkButtonProps) {
  const [open, setOpen] = useState(false);
  const [filter, setFilter] = useState<string|null>(null);
  const constants = useContext(ConstantsContext);
  const toogle = () => {
    setOpen((prev) => !prev);
  };
  const formik = useFormik({
    initialValues: {
      name: '',
      city: '',
      state: '',
      zip: '',
    },
    onSubmit: async (values) => {
      try {
        const queryString = makeQueryString(values as any);
        if (!queryString) {
          // eslint-disable-next-line no-use-before-define, max-len
          // const response:any = await API.NETWORKS.fetchNetworks(undefined, undefined, undefined, queryString);
          // setData(response);
          setFilter('');
          setOpen(false);
          return;
        }
        // eslint-disable-next-line max-len
        // const response:any = await API.NETWORKS.fetchNetworks(undefined, undefined, undefined, queryString);
        // setData(response);
        setFilter('filled');
      } catch (error) {
        console.log(error);
      } finally {
        toogle();
      }
    // make values query string and call api
    },
  });

  const clearAll = () => {
    formik.resetForm();
    formik.submitForm();
  };
  return (
    <div>
      <Button
        onClick={toogle}
        style={{
          backgroundColor: filter ? 'transperent' : '#ECEEED',
          color: 'black',
          height: '45px',
          width: '102px',
        }}
        variant={filter ? 'outlined' : 'text'}
        className="rounded-md bg-icon-filter bg-no-repeat bg-left border-primary bg-origin-content pr-3"
      >
        Filter
      </Button>
      <Dialog open={open} onClose={toogle} maxWidth="md">
        <DialogContent>
          <div className="borderLine min-w-[500px]">
            <p className="text-center m-0">Filter Networks</p>
            <Close onClick={toogle} className="cursor-pointer absolute top-5 right-2" />
          </div>
          <div className="pt-2 borderLine grid grid-cols-2  auto-cols-max gap-2">
            <div className="col-span-2">
              <p className="text-sm font-semibold">Name</p>
              <TextField
                variant="filled"
                className="w-full"
                value={formik.values.name}
                onChange={formik.handleChange}
                name="name"
                label="Network Name"
              />
            </div>
            <div>
              <p className="text-sm font-semibold">City</p>
              <TextField
                variant="filled"
                onChange={formik.handleChange}
                name="city"
                value={formik.values.city}
                label="Enter City"
                className="w-full"
              />
            </div>
            <div>
              <p className="text-sm font-semibold">State</p>
              <SelectBox
                items={constants?.states}
                defaultValue={formik.values.state}
                onChange={formik.handleChange}
                label="Select State"
                keyVal="state"
              />
            </div>
            <div>
              <p className="text-sm font-semibold">Zipcode</p>
              <TextField
                variant="filled"
                name="zip"
                value={formik.values.zip}
                onChange={formik.handleChange}
                label="Enter Zipcode"
                className="w-full"
              />
            </div>
          </div>
          <div className="pt-4 flex flex-row justify-between items-center">
            <Button
              style={{
                height: '40px',
              }}
              className="rounded-md bg-[#ECEEED] text-[#008390]"
              variant="text"
              onClick={clearAll}
            >
              Clear All
            </Button>
            <Button
              style={{
                height: '40px',
              }}
              className="rounded-md bg-primary text-white"
              variant="text"
              onClick={formik.submitForm}
            >
              Apply
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}

export default FilterNetworkButton;
