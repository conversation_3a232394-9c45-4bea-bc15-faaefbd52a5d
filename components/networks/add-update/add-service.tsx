import React, { useContext } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, TextField } from '@mui/material';
import { Close } from '@mui/icons-material';
import SelectBox from 'components/select-box/select-box';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useFormik } from 'formik';
import { createValidationSchema } from 'utils/helper';
import CheckIcon from '@mui/icons-material/Check';

interface AddNetworkServiceProps {
    open: boolean;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    setServiceRecords: React.Dispatch<React.SetStateAction<any[]>>;
}
const initialValues = {
  name: '',
  desc: '',
  service: '',
};
function AddNetworkService({
  open, setOpen, setServiceRecords,
}: AddNetworkServiceProps) {
  const constant = useContext(ConstantsContext);
  const formik = useFormik({
    initialValues,
    onSubmit: async (values) => {
      // TO-Do Profile photo
      try {
        setServiceRecords((prev:any) => [...prev, values]);
        setOpen(false);
      } catch (err) {
        console.log(err);
      }
    },
    validateOnChange: false,
    validationSchema: createValidationSchema(initialValues),
  });

  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div className="bg-white m-[5%] relative min-h-[80%] mb-10 rounded-lg overflow-hidden">
        <div
          style={{
            borderBottom: '1px solid #E0E0E0',
          }}
          className="h-[54px] items-center relative justify-center grid grid-flow-col border border-black "
        >
          <p>Add Network Service</p>
          <Close className="absolute right-5 cursor-pointer" onClick={() => setOpen(false)} />
        </div>
        <div className="grid grid-cols-2 auto-cols-max gap-8 p-5">
          <TextField id="outlined-basic" onChange={formik.handleChange} name="name" label="Service Name" variant="filled" />
          <SelectBox label="Service Type" defaultValue={formik.values.service} onChange={formik.handleChange} items={constant?.networkServices} keyVal="service" />
          <div className="col-span-2">
            <TextField
              id="outlined-multiline-static"
              label="Description"
              name="desc"
              onChange={formik.handleChange}
              sx={{ backgroundColor: '#F7F8FA' }}
              multiline
              rows={4}
              defaultValue=""
              variant="filled"
              fullWidth
            />

          </div>
        </div>
        <div
          id="footer-area"
          style={{
            borderTop: '1px solid #E0E0E0',
            width: '100%',
            zIndex: 2,
            bottom: 0,
          }}
          className="bg-white justify-end grid grid-flow-col absolute pr-5 h-[64px] items-center"
        >
          <Button
            onClick={formik.submitForm}
            variant="contained"
            className="text-white rounded-md"
          >
            <div className="grid grid-flow-col items-center gap-2">
              <CheckIcon />
              Save
            </div>
          </Button>
        </div>
      </div>
    </Modal>
  );
}
export default AddNetworkService;
