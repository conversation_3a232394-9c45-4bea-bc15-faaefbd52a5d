import React, { useContext, useEffect, useState } from 'react';
import { <PERSON><PERSON>, Modal } from '@mui/material';
import { Close } from '@mui/icons-material';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import CheckIcon from '@mui/icons-material/Check';
import Search from 'components/search/search';
import CustomTable from 'components/table-components/table';
import { useRouter } from 'next/router';

interface AddNetworkInsuranceProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectedCarriers: React.Dispatch<React.SetStateAction<any[]>>;
  selectedCarriers: any[];
}

function AddNetworkInsurance({
  open,
  setOpen,
  setSelectedCarriers,
  selectedCarriers,
}: AddNetworkInsuranceProps) {
  const router = useRouter();
  const { id } = router.query;
  const [tempSelectedCarriers, setTempSelectedCarriers] = useState<any[]>([]);
  const constants = useContext(ConstantsContext);
  const [carriers, setCarriers] = useState<any[]>([]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setCarriers(
      // eslint-disable-next-line max-len
      constants?.carriers?.filter((carrier) => carrier.title?.toLowerCase().includes(e.target.value.toLowerCase())) || [],
    );
  };

  const handleSave = () => {
    // setSelectedCarriers(tempSelectedCarriers);
    setOpen(false);
  };

  useEffect(() => {
    if (id && id === 'add') {
      setTempSelectedCarriers([]);
    } else {
      setTempSelectedCarriers(selectedCarriers);
    }

    const formattedCarriers = constants?.carriers?.map((carrier) => ({
      name: carrier.title || '',
      id: carrier.key.toUpperCase() || '',
      type: carrier.type || '',
    })) || [];
    setCarriers(formattedCarriers);
  }, [selectedCarriers, constants?.carriers, id]); // Add necessary dependencies

  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div className="bg-white my-[5%] mx-[15%] relative min-h-[80%] mb-10 rounded-lg overflow-hidden">
        <div
          style={{
            borderBottom: '1px solid #E0E0E0',
          }}
          className="h-[60px] items-center relative justify-center grid grid-flow-col"
        >
          <p className="font-[500]">Select Accepted Insurances</p>
          <Close
            className="absolute right-5 cursor-pointer"
            onClick={() => setOpen(false)}
          />
        </div>
        <div
          style={{
            borderBottom: '1px solid #E0E0E0',
          }}
          className="h-[60px] items-center relative justify-center grid grid-flow-col"
        >
          <Search
            placeholder="Search insurance carrier"
            onChange={handleSearch}
          />
        </div>

        <div
          id="footer-area"
          style={{
            borderTop: '1px solid #E0E0E0',
            width: '100%',
            zIndex: 2,
            bottom: 0,
          }}
          className="bg-white justify-end grid grid-flow-col absolute pr-5 h-[64px] items-center"
        >
          <Button
            onClick={handleSave}
            variant="contained"
            className="text-white rounded-md"
          >
            <div className="grid grid-flow-col items-center gap-2">
              <CheckIcon />
              Save
            </div>
          </Button>
        </div>
        <div className="overflow-y-auto max-h-[50vh]">
          <CustomTable
            // key={tempSelectedCarriers.length} // Ensures re-render when selected changes
            source={carriers || []}
            variant="select-insurance-table"
            headCellStyle={{
              fontSize: '13px',
              color: '#747A7A',
              paddingTop: '30px',
              paddingBottom: '4px',
            }}
            tableCellStyle={{
              fontSize: '13px',
              color: '#001018',
              padding: '0px 9px',
              border: 'none',
            }}
            select
            metadata={{
              setSelected: setSelectedCarriers,
            }}
            selected={selectedCarriers}
          />
        </div>
      </div>
    </Modal>
  );
}
export default AddNetworkInsurance;
