/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { <PERSON><PERSON>, TextField } from '@mui/material';
import AddNetworkInsurance from 'components/networks/add-update/add-insurance';
import AddNetworkService from 'components/networks/add-update/add-service';
import SelectBox from 'components/select-box/select-box';
import CustomTable from 'components/table-components/table';
import { useModal } from 'contexts/modal-context/modal-context';
import useAddNetworkLogic from 'logics/network/use-form-handler';
import React, { useEffect, useState } from 'react';
import { Network } from 'types/networks-types';
import { Service } from 'types/service-types';
import { isEmptyObject } from 'utils/helper';

interface Props {
  network: Network;
  add: boolean;
}

function NetworkAddUpdate({ network, add }: Props) {
  const {
    formik, router, setSelectedService, selectedService, constants,
  } = useAddNetworkLogic(add, network?.id);
  const { showModal } = useModal();
  const [openAddService, setOpenAddService] = useState(false);
  const [openAddInsurance, setOpenAddInsurance] = useState(false);
  const [selectedCarriers, setSelectedCarriers] = useState<any[]>([]);

  useEffect(() => {
    if (!isEmptyObject(formik.errors)) {
      showModal(
        'Missing Fields',
        `
            ${Object.keys(formik.errors).map(
    (key) => `${(formik as any).errors[key]}`,
  )}
          `,
      );
    }
  }, [JSON.stringify(formik.errors)]);

  useEffect(() => {
    if (!isEmptyObject(network)) {
      formik.setValues({
        title: network.name,
        types: network.types?.[0],
        status: network.status,
        street: network.address?.[0]?.street,
        street2: network.address?.[0]?.street2,
        city: network.address?.[0]?.city,
        state: network.address?.[0]?.state,
        zip: network.address?.[0]?.zip,
        email: network.email as string,
        website: network.website as string,
        contact: network.contact as string,
        phone:
          network?.phones?.length > 0
            ? ((network as any)?.phones[0]?.number as any)
            : '',
        carriers: network.carriers as any[],
      });
      setSelectedCarriers(network.carriers as any[]);
      setSelectedService((network as any).services || ([] as Service[]));
    }
  }, [network]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    formik.setFieldValue(e.target.name, e.target.value);
  };

  useEffect(() => {
    formik.setFieldValue('carriers', selectedCarriers);
  }, [selectedCarriers]);

  useEffect(() => {
    if (network?.carriers?.length > 0) {
      setSelectedCarriers(network.carriers as any[]);
    }
  }, []);

  return (
    <>
      <div className="col-span-3 items-center grid grid-flow-col auto-cols-auto place-content-between w-full pt-4 pb-4 pl-4 pr-5">
        <p className="text-[21px] p-0 m-0 font-[500] col-span-3">
          <span
            className="font-[300] cursor-pointer"
            onClick={() => {
              router.push('/networks');
            }}
          >
            Networks
          </span>
          {` > ${add ? 'Add Network' : `Update Network (${network?.name})`}`}
        </p>
        <Button
          onClick={formik.submitForm}
          variant="contained"
          className="rounded-md h-11  font-[500] text-sm"
        >
          <span className="font-[500]">Save</span>
        </Button>
      </div>
      <div className="pl-8 pr-10">
        <p className="text-lg font-[600] col-span-3 m-0 mt-3">
          Network Details
        </p>
        <div className="flex flex-col mt-3">
          <div className="flex flex-wrap w-full justify-between">
            <TextField
              value={formik.values.title}
              name="title"
              onChange={formik.handleChange}
              placeholder="Enter Title of Network"
              id="filled-basic"
              label="Title"
              variant="filled"
              className="lg:w-[32%] min-w-[240px] w-[48%] mb-4"
            />
            <div className="lg:w-[32%] min-w-[240px] w-[48%] mb-4">
              <SelectBox
                label="Service Type"
                defaultValue={formik.values.types}
                keyVal="types"
                onChange={handleChange}
                items={constants?.networkServices}
              />
            </div>
            <div className="lg:w-[32%] min-w-[240px] w-[48%] mb-4">
              <SelectBox
                defaultValue={formik.values.status}
                label="Status"
                keyVal="status"
                onChange={handleChange}
                items={constants?.networkStatus}
              />
            </div>
            <TextField
              value={formik.values.street}
              name="street"
              onChange={formik.handleChange}
              placeholder="Enter Street"
              id="filled-basic"
              label="Street"
              variant="filled"
              className="lg:w-[32%] min-w-[240px] w-[48%] mb-4"
            />
            <TextField
              value={formik.values.street2 || ''}
              name="street2"
              onChange={formik.handleChange}
              placeholder="Enter Street 2 "
              id="filled-basic"
              label="Street 2"
              variant="filled"
              className="lg:w-[32%] min-w-[240px] w-[48%] mb-4"
            />
            <TextField
              value={formik.values.city}
              name="city"
              onChange={formik.handleChange}
              placeholder="Enter City"
              id="filled-basic"
              label="City"
              variant="filled"
              className="lg:w-[32%] min-w-[240px] w-[48%] mb-4"
            />
            <div className="lg:w-[32%] min-w-[240px] w-[48%] mb-4">
              <SelectBox
                defaultValue={formik.values.state}
                label="States"
                keyVal="state"
                onChange={handleChange}
                items={constants?.states}
              />
            </div>
            <TextField
              value={formik.values.zip}
              name="zip"
              onChange={formik.handleChange}
              placeholder="Enter Zip Code"
              id="filled-basic"
              label="Zip Code"
              variant="filled"
              className="lg:w-[32%] min-w-[240px] w-[48%] mb-4"
            />
            <TextField
              value={formik.values.email}
              name="email"
              onChange={formik.handleChange}
              placeholder="Enter Email"
              id="filled-basic"
              label="Email"
              variant="filled"
              className="lg:w-[32%] min-w-[240px] w-[48%] mb-4"
            />
            <TextField
              value={formik.values.website}
              name="website"
              onChange={formik.handleChange}
              placeholder="Enter Website"
              id="filled-basic"
              label="Website"
              variant="filled"
              className="lg:w-[32%] min-w-[240px] w-[48%] mb-4"
            />
            <TextField
              value={formik.values.contact}
              name="contact"
              onChange={formik.handleChange}
              placeholder="Enter Point of Contact Name"
              id="filled-basic"
              label="Contact Name"
              variant="filled"
              className="lg:w-[32%] min-w-[240px] w-[48%] mb-4"
            />
            <TextField
              value={formik.values.phone}
              name="phone"
              onChange={formik.handleChange}
              placeholder="Enter Contact Phone Number"
              id="filled-basic"
              label="Contact Phone"
              variant="filled"
              className="lg:w-[32%] min-w-[240px] w-[48%] mb-4"
            />
          </div>
        </div>
        <div className="col-span-3 grid grid-flow-col auto-cols-auto text-xs items-center mt-8">
          <p className="text-lg font-[600] m-0 mb-2">Services</p>
          {selectedService.length > 0 && (
            <div className="grid grid-flow-col gap-1 auto-cols-max text-xs items-center justify-self-end">
              <span
                onClick={() => {
                  setOpenAddService(true);
                }}
                className="text-primary font-[500] cursor-pointer text-[15px] hover:text-[#ff8c20]"
              >
                <span className="text-[18px]">+ </span>
                Add Service
              </span>
            </div>
          )}
        </div>
        <div className="col-span-3 pb-5">
          {selectedService.length === 0 ? (
            <p className="text-[#646F79] text-sm m-0">
              This network does not have any services added.
              <span
                onClick={() => {
                  setOpenAddService(true);
                }}
                className="text-primary pl-2 cursor-pointer text-[15px] hover:text-[#ff8c20]"
              >
                <span className="text-[18px]">+ </span>
                Add Service
              </span>
            </p>
          ) : (
            <CustomTable
              source={selectedService}
              variant="addsection-service-table"
              style={{
                borderBottom: 'none',
                borderTop: 'none',
              }}
              headCellStyle={{
                padding: '4px 16px',
                fontSize: '13px',
                color: '#747A7A',
                borderTop: 'none',
              }}
              tableCellStyle={{
                fontSize: '15px',
              }}
              handleDelete={(item) => {
                setSelectedService((prev) => prev.filter((service) => service.id !== item.id));
              }}
            />
          )}
        </div>
        <div className="col-span-3 grid grid-flow-col auto-cols-auto text-xs items-center mt-8">
          <p className="text-lg font-[600] m-0 mb-2">Accepted Insurances</p>
          {selectedCarriers?.length > 0 && (
            <div className="grid grid-flow-col gap-1 auto-cols-max text-xs items-center justify-self-end">
              <span
                onClick={() => {
                  setOpenAddInsurance(true);
                }}
                className="text-primary font-[500] cursor-pointer text-[15px] hover:text-[#ff8c20]"
              >
                <span className="text-[18px]">+ </span>
                Add Insurance
              </span>
            </div>
          )}
        </div>
        <div className="col-span-3 pb-10">
          {selectedCarriers?.length === 0 ? (
            <p className="text-[#646F79] text-sm m-0">
              This network does not have any insurances added.
              <span
                onClick={() => {
                  setOpenAddInsurance(true);
                }}
                className="text-primary pl-2 cursor-pointer text-[15px] hover:text-[#ff8c20]"
              >
                <span className="text-[18px]">+ </span>
                Add Insurance
              </span>
            </p>
          ) : (
            <CustomTable
              source={selectedCarriers}
              variant="addsection-insurance-table"
              style={{
                border: 'solid 1px #DCDFDF',
                borderBottom: 'none',
              }}
              headCellStyle={{
                padding: '4px 16px',
                fontSize: '13px',
                color: '#747A7A',
                borderTop: 'none',
              }}
              tableCellStyle={{
                fontSize: '15px',
                padding: '0px 16px',
              }}
              handleDelete={(item) => {
                setSelectedCarriers((prev) => prev.filter((carrier) => carrier.id !== item.id));
              }}
            />
          )}
        </div>
        <AddNetworkService
          setServiceRecords={setSelectedService}
          open={openAddService}
          setOpen={setOpenAddService}
        />
        <AddNetworkInsurance
          open={openAddInsurance}
          setOpen={setOpenAddInsurance}
          setSelectedCarriers={setSelectedCarriers}
          selectedCarriers={selectedCarriers}
        />
      </div>
    </>
  );
}

export default NetworkAddUpdate;
