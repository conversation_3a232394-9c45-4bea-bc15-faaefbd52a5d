import Avatar from '@mui/material/Avatar';
import TableCell from '@mui/material/TableCell';
import TableRow from '@mui/material/TableRow';
import { styled } from '@mui/system';

export const StyledTableCell = styled(TableCell)(({ theme }) => ({
  whiteSpace: 'nowrap',
  '&.MuiTableCell-head': {
    backgroundColor: theme.palette.common.white,
    color: '#646F79',
    textTransform: 'capitalize',
    cursor: 'pointer',
  },
  padding: '8px 16px',
}));

// eslint-disable-next-line max-len
export const StyledTableRow = styled(TableRow)(({ theme, index }: { theme?: any; index: number }) => ({
  backgroundColor: index % 2 === 0 ? theme.palette.common.white : '#F7F8FA',
}));

export const StyledAvatar = styled(Avatar)({
  borderRadius: '50%',
  marginRight: '8px',
});

export const borderlessTableContainer = {
  boxShadow: 'none',
};

export const borderlessTable = {
  borderCollapse: 'collapse',
  borderSpacing: 0,
  border: 'none',
};

export const borderlessTableCell = {
  border: 'none',
};
