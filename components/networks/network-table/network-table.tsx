import React, { useState, useEffect } from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableCell from '@mui/material/TableCell';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import TableRow from '@mui/material/TableRow';
import Paper from '@mui/material/Paper';
import ArrowUpwardIcon from '@mui/icons-material/KeyboardArrowUp';
import ArrowDownwardIcon from '@mui/icons-material/KeyboardArrowDown';
import { Checkbox, Pagination } from '@mui/material';

import CustomizeNetworkFields from 'components/networks/network-table/render-columns';
import { Network } from 'api/src/types';

import ActionContainer from 'components/action-container/action-container';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useModal } from 'contexts/modal-context/modal-context';
import API from 'api/src/lib/api';
import {
  StyledTableCell,
  StyledTableRow, borderlessTable,
  borderlessTableCell, borderlessTableContainer,
} from './styled-components';
import { TableHeader } from './type';

interface NetworkTableProps {
  networkData: Network[];
  tableHeaders: TableHeader[];
  select?: boolean;
  insideModal?: boolean;
  setEditNetwork?: React.Dispatch<React.SetStateAction<Network>>;
  setOpen?: React.Dispatch<React.SetStateAction<boolean>>;
  selectedNetworks?: Network[];
  setSelectedNetworks?: React.Dispatch<React.SetStateAction<Network[]>>;
}

function NetworkTable({
  networkData, tableHeaders, select = false,
  setSelectedNetworks, selectedNetworks, setEditNetwork, setOpen, insideModal,
}: NetworkTableProps) {
  // Click handler for sorting
  const [sortedNetworks, setSortedNetworks] = useState<Network[]>(networkData || []);
  const [sortingDirection, setSortingDirection] = useState<'asc' | 'desc' | null>(null);
  const [sortedColumn, setSortedColumn] = useState<keyof Network | null>(null);
  const [page, setPage] = useState(1);
  const { refresh } = useRefreshSSR();
  const { showModal } = useModal();
  // eslint-disable-next-line no-unused-vars
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const handleHeadClick = (property: keyof Network) => {
    const newSortingDirection = sortingDirection === 'asc' ? 'desc' : 'asc';
    const newSortedNetworks = [...sortedNetworks].sort((a, b) => {
      if (a[property] && b[property]) {
        if (a[property]! < b[property]!) return newSortingDirection === 'asc' ? -1 : 1;
        if (a[property]! > b[property]!) return newSortingDirection === 'asc' ? 1 : -1;
      }
      return 0;
    });

    setSortedNetworks(newSortedNetworks);
    setSortingDirection(newSortingDirection);
    setSortedColumn(property);
  };

  const handleSelectedNetworks = (item: Network) => {
    if (setSelectedNetworks) {
      setSelectedNetworks((prev) => {
        if (prev.includes(item)) {
          return prev.filter((items) => items !== item);
        }
        return [...prev, item];
      });
    }
  };

  const handleDeleteNetwork = (item: Network) => {
    showModal('Warning', 'This process will permanently alter or delete data, settings, or configurations. Once initiated, there is no possibility of recovery or restoration. Are you sure you want to delete this network?', async () => {
      await API.NETWORKS.delete(item.id);
      refresh();
    });
  };

  const handleOnEdit = (member:Network) => {
    if (setEditNetwork) setEditNetwork(member);
    if (setOpen) setOpen(true);
  };

  useEffect(() => {
    setSortedNetworks(networkData);
    if (page > 1) {
      setPage(1);
    }
  }, [JSON.stringify(networkData)]);

  return (
    <TableContainer component={Paper} sx={borderlessTableContainer}>
      <Table aria-label="network table" sx={borderlessTable}>
        <TableHead>
          <TableRow>
            {tableHeaders.map(({ key, value }) => (
              <StyledTableCell
                key={key as string}
                onClick={() => handleHeadClick(key)}
              >
                <div className="grid grid-flow-col items-center auto-cols-min">
                  {value}
                  {sortedColumn === key && (!Array.isArray(value)) && (
                    sortingDirection === 'asc' ? <ArrowUpwardIcon /> : <ArrowDownwardIcon />
                  )}
                </div>
              </StyledTableCell>
            ))}
          </TableRow>
        </TableHead>
        <TableBody>
          {sortedNetworks.slice((page - 1) * rowsPerPage, page * rowsPerPage)
            .map((network, index) => (
              <StyledTableRow key={network.id} index={index}>
                {tableHeaders.map(({ key }) => {
                  const value = network[key];
                  const isSelected = selectedNetworks?.includes(network);
                  if ((key as any) === 'actions' && !select) {
                    return (
                      <TableCell key={key} style={{ padding: '8px 16px', ...borderlessTableCell }}>
                        {insideModal ? (
                          <ActionContainer
                            deleteLabel="Remove Network"
                            editLabel="Edit Network"
                          />
                        )
                          : (
                            <ActionContainer
                              deleteLabel="Delete Network"
                              editLabel="Edit Network"
                              onDelete={() => {
                                handleDeleteNetwork(network);
                              }}
                              onEdit={() => {
                                handleOnEdit(network);
                              }}
                            />
                          )}
                      </TableCell>
                    );
                  }
                  return (
                    <TableCell key={key} style={{ padding: '2px 16px', ...borderlessTableCell }}>
                      <div className="grid grid-flow-col items-center justify-start">
                        <CustomizeNetworkFields
                          field={key}
                          record={network}
                          value={value as any}
                          select={select}
                        >
                          {select && (
                            <Checkbox
                              checked={isSelected}
                              onChange={() => handleSelectedNetworks(network)}
                            />
                          )}
                        </CustomizeNetworkFields>
                      </div>
                    </TableCell>
                  );
                })}
              </StyledTableRow>
            ))}
        </TableBody>
      </Table>
      <Pagination
        hideNextButton={sortedNetworks.length <= rowsPerPage}
        hidePrevButton={sortedNetworks.length <= rowsPerPage}
        count={Math.ceil(sortedNetworks.length / rowsPerPage)}
        page={page}
        onChange={(event, value) => setPage(value)}
        className="pt-5 align-middle"
      />
    </TableContainer>
  );
}

export default NetworkTable;
