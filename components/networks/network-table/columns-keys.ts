/* eslint-disable react/function-component-definition */
// import { ScoreResponse } from 'hooks/use-score';
import { TableHeader } from 'components/networks/network-table/type';
import type React from 'react';

// eslint-disable-next-line import/prefer-default-export
export const networksTableColumns = [
  { key: 'name', value: 'Name' },
  { key: 'types', value: 'Type' },
  { key: 'address', value: 'Address' },
  { key: 'services', value: 'Service' },
  { key: 'updatedAt', value: 'Last Updated' },
  { key: 'actions', value: '' },

] as TableHeader[];

export interface CustomizeNetworkFieldsProps {
field: string;
children?: React.ReactNode;
record?: any;
value: string | number;
select?: boolean;
router?: any;
// eslint-disable-next-line no-unused-vars
// getScoreTitle: (score: number) => ScoreResponse | null;
}
