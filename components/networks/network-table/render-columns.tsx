/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/jsx-one-expression-per-line */
import { CustomizeNetworkFieldsProps } from 'components/networks/network-table/columns-keys';
import React from 'react';
import SafeDate from 'components/date-time/SafeDate';

const pStyle = 'text-[15px] font-[400] text-[#001018] first-letter:uppercase';

// eslint-disable-next-line react/function-component-definition
const CustomizeNavigatorFields: React.FC<CustomizeNetworkFieldsProps> = ({
  field,
  value,
  record,
  router,
  select,
  children,
}: CustomizeNetworkFieldsProps) => {
  const address = record?.address && record?.address?.length > 0 ? record?.address[0] : '';
  switch (field) {
    case 'name':

      return (
        <div
          onClick={() => {
            // eslint-disable-next-line no-unused-expressions
            router && !select && router.push(`/members/profile/${record?.id}`);
          }}
          key={field}
          className="grid grid-flow-col cursor-pointer items-center justify-start"
        >
          {children}
          <p className="text-[15px] w-44  text-[#001018] font-[500]">{value}</p>;

        </div>
      );
    case 'types':
      return <p className={pStyle}>{record?.types.join('')}</p>;
    case 'address':
      return (
        address === '' ? (
          <p className="pStyle">
            N/A
          </p>
        ) : (
          <div className="grid grid-flow-col auto-cols-max gap-1">
            <p className={pStyle}>{address?.street}</p>
            <p className={pStyle}>{address?.city}</p>
            <p className={pStyle}>{address?.state}</p>
          </div>
        )
      );
    case 'services':
      return <p className={pStyle}>{record?.services?.length || ''}</p>;
    case 'updatedAt':
      return <p className={pStyle}><SafeDate date={value as string} /></p>;

    default:
      return <p className={pStyle}>{value}</p>;
  }
};
export default CustomizeNavigatorFields;
