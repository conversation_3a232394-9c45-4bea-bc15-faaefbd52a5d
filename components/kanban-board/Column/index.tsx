import Card from 'components/kanban-board/Card';
import dynamic from 'next/dynamic';
import React, { useEffect } from 'react';

import { TaskObject } from 'types/tasks-response-type';
import { capitalizeWords } from 'utils/helper';

const Droppable = dynamic(
  () => import('react-beautiful-dnd').then((mod) => mod.Droppable),
  { ssr: false },
);
interface ColumnProps {
  title: string;
  tasks: TaskObject[];
  assessments?: any;
}

function Column({ title, tasks, assessments }: ColumnProps) {
  useEffect(() => {
    // resetServerContext();
  }, []);

  return (
    <Droppable isDropDisabled key={new Date().getTime()} droppableId={title}>
      {(provided) => (
        <div
          ref={provided.innerRef}
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...provided.droppableProps}
          className="bg-[#F7F8F8] rounded-2xl p-4 shadow-md"
          style={{
            width: '100%',
            minHeight: '200px',
          }}
        >
          <p className="text-lg font-semibold m-0 p-0 pb-[14px] pl-[7px] font-[600]">
            {capitalizeWords(title, true)}
          </p>
          {tasks?.map((task, index) => (
            <Card
              key={task.id}
              assessments={assessments}
              task={task}
              index={index}
              columnStatus={title}
            />
          ))}
          {provided.placeholder}
        </div>
      )}
    </Droppable>
  );
}

export default Column;
