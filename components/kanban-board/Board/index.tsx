import React from 'react';
import Column from 'components/kanban-board/Column';
import { TasksResponseProps } from 'types/tasks-response-type';

interface BoardProps {
  tasks: TasksResponseProps;
  assessments: any;
}

// Constant array specifying the order of the columns
const Entries = ['picked up', 'pending', 'completed'];

function Board({ tasks, assessments }: BoardProps) {
  return (
    <div className="grid grid-cols-3 auto-cols-max justify-center gap-5 rounded-lg">
      {Entries.map((status) => {
        const taskList = tasks[status as keyof TasksResponseProps];
        return (
          <Column
            key={status}
            title={status}
            assessments={assessments}
            tasks={taskList || []}
          />
        );
      })}
    </div>
  );
}

export default Board;
