/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/jsx-props-no-spreading */
import { Avatar } from '@mui/material';
import { useRouter } from 'next/router';
import { Draggable } from 'react-beautiful-dnd';
import { TaskObject } from 'types/tasks-response-type';
import {
  capitalizeWords,
  getKeyValue,
  getLatestImage,
  timeDifference,
} from 'utils/helper';
import SafeDate from 'components/date-time/SafeDate';

function Card({
  task,
  index,
  assessments,
  columnStatus,
}: {
  task: TaskObject;
  index: number;
  assessments?: any;
  columnStatus?: string;
}) {
  const dueDate = timeDifference(task.dueAtEpoc);
  const router = useRouter();
  const handleClass = () => {
    const defaultProp = {
      style: {
        backgroundColor:
          Math.sign(parseInt(dueDate, 10)) === 1 ? '#F7F8F8' : '#E42B57',
        color: Math.sign(parseInt(dueDate, 10)) === 1 ? '#000' : '#fff',
      },
      className: `absolute p-[2px] m-1 items-center bg-origin-content right-1 top-1 ${
        Math.sign(parseInt(dueDate, 10)) === 1
          ? 'bg-icon-clock'
          : 'bg-icon-clock-white'
      } bg-no-repeat bg-left  h-auto rounded-md`,
    };

    if (task.status === 'completed') {
      return {
        style: {
          color: '#008390',
        },
        className:
          'absolute  p-[2px] bg-icon-check bg-[#E3F8FA] m-1 items-center bg-origin-content right-1 top-1  bg-no-repeat bg-left  h-auto rounded-md',
      };
    }
    return defaultProp;
  };

  const assignedMemberArray: string[] = [];
  if (columnStatus === 'pending') {
    task.completedBy.forEach((assignedMember) => {
      assignedMemberArray.push(assignedMember.profile);
    });
  } else {
    const userProfileAttachment = task.assignee.profile;
    if (userProfileAttachment) {
      assignedMemberArray.push(userProfileAttachment);
    }
  }

  const handleClick = () => {
    router.push(`/tasks/${task.id}/detail`);
  };

  return (
    <Draggable isDragDisabled draggableId={task.id} index={index}>
      {(provided) => (
        <div
          ref={provided.innerRef}
          {...provided.draggableProps}
          {...provided.dragHandleProps}
          onClick={handleClick}
          style={{
            userSelect: 'none',
            padding: '8px', // Consistent padding
            maxWidth: '100%', // Max width to confine the content
            backgroundColor: 'white',
            minHeight: '74px', // Minimum height to ensure the card is clickable
            marginBottom: '12px',
            display: 'flex', // Using flexbox for better alignment
            alignItems: 'center', // Center items vertically
            borderRadius: '8px',
            position: 'relative', // Position relative for absolute positioning
            boxShadow: '0 2px 4px rgba(0,0,0,0.1)', // Optional: adding subtle shadow
            ...provided.draggableProps.style,
          }}
          className="cursor-pointer"
        >
          <Avatar
            className="z-10"
            src={
              task.receivers.length > 0
                ? getLatestImage(task.receivers[0].attachments, 'profile')
                : ''
            }
          />
          <div
            style={{
              marginLeft: '8px', // Add margin between avatar and text
              overflow: 'hidden', // Prevent overflow
              textOverflow: 'ellipsis', // Use ellipsis for text that's too long
              whiteSpace: 'nowrap', // Prevent wrapping to ensure text stays in a single line
              width: 'calc(100% - 80px)', // Subtract width of the Avatar and margin
            }}
          >
            <p
              style={{
                fontSize: '13px', // Small text size for title
                margin: '0',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
              className="font-inter font-semibold"
            >
              {capitalizeWords(task.title, true)}
            </p>
            <p
              style={{
                fontSize: '11px', // Small text size for title
                margin: '0',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
              className="font-inter font-normal"
            >
              {task.type === 'assessment'
                ? capitalizeWords(
                  getKeyValue(task.taskDetail.kind, 'assessments', {
                    assessments,
                  }),
                  true,
                )
                : capitalizeWords(task.taskDetail.type, true)}
            </p>
            <p
              style={{
                fontSize: '0.75rem', // Even smaller text size for type
                margin: '0',
                overflow: 'hidden',
                textOverflow: 'ellipsis',
                whiteSpace: 'nowrap',
              }}
              className={`${task.status === 'completed' ? 'line-through' : ''}`}
            >
              <SafeDate date={task.dueAtEpoc} time />
              <div {...handleClass()}>
                <p className="m-0 text-[11px] pt-0 font-normal pl-4 ">
                  {task.status === 'completed'
                    ? 'Done'
                    : timeDifference(task.dueAtEpoc)}
                </p>
              </div>
            </p>
          </div>
          {task.urgent && (
            <div className="absolute top-[-15px] z-0 left-0 ">
              <div className="bg-flag-bg bg-left m-0 p-0 bg-white bg-no-repeat w-8 h-6 rounded-t-l-md">
                <p className="bg-icon-flag bg-left pl-1 bg-origin-content bg-no-repeat w-4 h-4" />
              </div>
            </div>
          )}
          <div className="absolute bottom-0 right-0 p-2">
            {assignedMemberArray.length ? (
              <div className="flex">
                {assignedMemberArray.map((imageUrl) => (
                  <Avatar
                    key={imageUrl}
                    src={imageUrl}
                    sx={{
                      width: 24,
                      height: 24,
                      border: '2px solid white',
                    }}
                  />
                ))}
              </div>
            ) : <div />}
          </div>
        </div>
      )}
    </Draggable>
  );
}

export default Card;
