import Board from 'components/kanban-board/Board';
import useDragAndDrop from 'components/kanban-board/hook/use-drag-drop';
import { useEffect } from 'react';
import { DragDropContext } from 'react-beautiful-dnd';
import { TasksResponseProps } from 'types/tasks-response-type';

interface KanbanBoardProps {
  data: TasksResponseProps;
  assessments: any;
}

function KanbanBoard({ data, assessments }: KanbanBoardProps) {
  const { tasks, setTasks, onDragEnd } = useDragAndDrop(data);

  useEffect(() => {
    setTasks(data);
  }, [data]);
  return (
    <div className="pb-2">
      <DragDropContext onDragEnd={onDragEnd}>
        <Board tasks={tasks} assessments={assessments} />
      </DragDropContext>
    </div>
  );
}
export default KanbanBoard;
