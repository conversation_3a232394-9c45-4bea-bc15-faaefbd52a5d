import { useState } from 'react';
import { DropResult } from 'react-beautiful-dnd';
import { TasksResponseProps } from 'types/tasks-response-type';

const useDragAndDrop = (initialTasks: TasksResponseProps) => {
  const [tasks, setTasks] = useState<TasksResponseProps>(initialTasks);

  const onDragEnd = async (result: DropResult) => {
    const { source, destination } = result;
    // Do nothing if there is no destination or the item is dropped back to its original position
    if (!destination || (source.droppableId
      === destination.droppableId && source.index === destination.index)) {
      return;
    }

    const sourceColumn = tasks[source.droppableId as keyof TasksResponseProps];
    const destColumn = tasks[destination.droppableId as keyof TasksResponseProps];

    // If moving within the same column, handle the array manipulation differently
    if (source.droppableId === destination.droppableId) {
      const updatedTasks = Array.from(sourceColumn); // Create a copy of the tasks array
      const [reorderedItem] = updatedTasks.splice(source.index, 1);
      updatedTasks.splice(destination.index, 0, reorderedItem); // Insert it at the new position

      // Update the state with the new tasks array for this column
      setTasks((prev) => ({
        ...prev,
        [source.droppableId]: updatedTasks,
      }));
    } else {
      // Handle moving between different columns
      const sourceTasks = Array.from(sourceColumn);
      const destTasks = Array.from(destColumn);
      const [removed] = sourceTasks.splice(source.index, 1);
      destTasks.splice(destination.index, 0, removed);

      // Update the state with the new tasks arrays
      setTasks((prev) => ({
        ...prev,
        [source.droppableId]: sourceTasks,
        [destination.droppableId]: destTasks,
      }));
      // await API.TASKS.updateStatus(result.draggableId, destination.droppableId);
      // await refresh();
    }
  };

  return { tasks, onDragEnd, setTasks };
};

export default useDragAndDrop;
