import { TextField } from '@mui/material';
// import AttachmentPicker from 'components/attacment-picker/attacment-picker';
import DateSelector from 'components/date-selector/date-selector';
import TabsView from 'components/tabs-view';
import { ConstantContext } from 'contexts/constant-context/type';

function TaskDateDetails({
  constants,
  formik,
}: {
  constants: ConstantContext;
  formik: any;
}) {
  const handleDateType = (value: string) => {
    formik.setFieldValue('tmp.dateType', value);
  };

  return (
    <div className="">
      <p className="font-semibold text-sm">Date</p>
      {/* <TabsView
        tabs={constants?.taskCompletionTypes || []}
        className="grid grid-flow-col auto-cols-max gap-0"
        value={formik.values.tmp.dateType}
        callback={handleDateType}
      /> */}
      <div className="col-span-3 mt-3 min-w-[400px]">
        <DateSelector
          time // -- 6.25.25 - changed to only date for tasks
          defaultValue={formik.values.tmp.date}
          name="tmp.date"
          pickerProps={
            {
              label: 'Date of Task',
              slotProps: {
                field: {
                  disabled: formik.values.tmp.dateType === 'no_end_date',
                  clearable: true,
                  onClear: () => {
                    formik.setFieldValue('tmp.date', null);
                  },
                },
              },
            } as any
          }
          onChange={(e) => {
            const date = new Date(e.target.value).getTime();
            formik.setFieldValue('tmp.date', date);
          }}
        />
      </div>
      <div className="col-span-3 mt-3">
        <p className="font-semibold text-sm">Description</p>
        <TextField
          className="w-full"
          rows={4}
          variant="filled"
          label="Task Description"
          name="desc"
          onChange={formik.handleChange}
          value={formik.values.desc}
          multiline
        />
        {/* <div className="bg-icon-attachment bg-no-repeat bg-left pl-5 bg-origin-padding mt-3">
          <AttachmentPicker setPicture={null as any} multiple picture={[]} />
        </div> */}
      </div>
    </div>
  );
}

export default TaskDateDetails;
