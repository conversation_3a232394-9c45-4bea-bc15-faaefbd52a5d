/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { Avatar } from '@mui/material';
import React from 'react';
import { Member } from 'types/members-response-type';
import { getLatestImage, capitalizeWords } from 'utils/helper';

interface MemberSingularSelectionProps {
  setOpenMemberSelection: React.Dispatch<React.SetStateAction<boolean>>;
  selectedMembers: Member[];
  editMemberButton?: boolean; // Optional prop
}

function MemberSingularSelection({
  setOpenMemberSelection,
  selectedMembers,
  editMemberButton = true, // Default to true
}: MemberSingularSelectionProps) {
  return (
    <>
      <p className="text-lg font-semibold col-span-3">Member Details</p>
      {selectedMembers.length === 0 && (
        <div
          onClick={() => {
            setOpenMemberSelection(true);
          }}
          className="bg-[#FFF7EC] cursor-pointer col-span-3 rounded-md h-[48px] grid grid-flow-row items-center justify-center"
        >
          <p className="m-0 text-primary font-semibold text-sm">Select Member</p>
        </div>
      )}
      <div>
        {selectedMembers?.length > 0
          && selectedMembers.map((member) => (
            <div
              key={member.id}
              className="col-span-3 rounded-md grid grid-flow-col auto-cols-max gap-8 items-center justify-between"
            >
              <Avatar
                className="w-10 h-10"
                src={getLatestImage(member?.attachments, 'profile')}
              />
              <p className="text-sm font-semibold">
                {capitalizeWords(`${member?.firstName} ${member?.lastName}`, true)}
              </p>
              {editMemberButton && ( // Conditionally render the edit button
                <div
                  onClick={() => {
                    setOpenMemberSelection(true);
                  }}
                  className="bg-[#F7F8F8] cursor-pointer w-20 h-10 rounded-md grid items-center justify-center"
                >
                  <p className="m-0 text-sm font-semibold text-primary">Edit</p>
                </div>
              )}
            </div>
          ))}
      </div>
    </>
  );
}

export default MemberSingularSelection;
