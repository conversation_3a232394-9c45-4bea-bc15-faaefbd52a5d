import { Close } from '@mui/icons-material';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Button } from '@mui/material';
import SelectBox from 'components/select-box/select-box';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useFormik } from 'formik';
import { useContext, useEffect, useState } from 'react';
import { capitalizeWords, createValidationSchema } from 'utils/helper';
import CheckIcon from '@mui/icons-material/Check';
import { TaskObject } from 'types/tasks-response-type';

interface MarkTaskCompleteProps {
    task: TaskObject;
    // eslint-disable-next-line no-unused-vars
    onComplete: (values: any) => void;
    open: boolean;
    // eslint-disable-next-line no-unused-vars
    setOpen: (value: boolean) => void;
}
function MarkTaskComplete({
  task, onComplete, open, setOpen,
}: MarkTaskCompleteProps) {
  const constant = useContext(ConstantsContext);
  const initialValues = {
    reason: '',
    msg: '',
  };
  const formik = useFormik({
    initialValues,
    onSubmit: async (values) => {
      // TO-Do Profile photo
      try {
        onComplete(values);
        setOpen(false);
      } catch (err) {
        console.log(err);
      }
    },
    validateOnChange: false,
    validationSchema: createValidationSchema(initialValues),
  });

  const [reasonsByType, setReasonsByType] = useState<any[]>([]);

  useEffect(() => {
    if (task.type.includes('visit')) {
      setReasonsByType(constant?.taskCompletionReasons?.filter((reason) => reason.key.includes('visit')) || []);
    } else if (task.type.includes('contact')) {
      setReasonsByType(constant?.taskCompletionReasons?.filter((reason) => reason.key.includes('contact')) || []);
    } else {
      setReasonsByType(constant?.taskCompletionReasons?.filter((reason) => !reason.key.includes('contact') && !reason.key.includes('visit')) || []);
    }
  }, [constant?.taskCompletionReasons, task.type]);
  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div className="bg-white m-[5%] relative  min-h-[80%] mb-10 rounded-lg overflow-hidden">
        <div
          style={{
            borderBottom: '1px solid #E0E0E0',
          }}
          className="h-[64px] items-center relative justify-center grid grid-flow-col border border-black "
        >
          <div className="grid grid-flow-row p-2 items-center text-center">
            <p className="m-0 font-semibold text-md">Mark Task Complete</p>
            <p className="m-0 font-normal text-md">
              Task:
              <span className="m-0 pl-2 font-semibold text-md">
                {capitalizeWords(task.title, true)}
              </span>
            </p>

          </div>
          <Close className="absolute right-5 cursor-pointer" onClick={() => setOpen(false)} />
        </div>
        <div className="grid grid-cols-2 auto-cols-max gap-8 p-5">
          <SelectBox
            label="Reason"
            defaultValue={formik.values.reason}
            onChange={formik.handleChange}
            items={reasonsByType}
            keyVal="reason"
          />
          <div className="col-span-2">
            <TextField
              id="outlined-multiline-static"
              label="Message"
              value={formik.values.msg}
              name="msg"
              onChange={formik.handleChange}
              sx={{ backgroundColor: '#F7F8FA' }}
              multiline
              rows={4}
              defaultValue=""
              variant="filled"
              fullWidth
            />

          </div>
        </div>
        <div
          id="footer-area"
          style={{
            borderTop: '1px solid #E0E0E0',
            position: 'absolute',
            width: '100%',
            zIndex: 2,
            bottom: 0,
          }}
          className="h-[60px] shrink-0 bg-white border-t border-gray-300 flex items-center justify-end px-5"
        >
          <Button
            onClick={formik.submitForm}
            variant="contained"
            className="bg-[#2D62ED] top-2 text-white absolute right-5 rounded-md w-[122px]"
          >
            <div className="grid grid-flow-col items-center gap-2">
              <CheckIcon />
              Confirm
            </div>
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default MarkTaskComplete;
