/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { Checkbox, TextField } from '@mui/material';
import API from 'api/src/lib/api';
import AddressCollect from 'components/address-collect';
import AttachmentPicker from 'components/attachment-picker/attachment-picker';
import InnerPageHeader from 'components/inner-page-header';
import SelectMember from 'components/members/select-member/select-member';
import SelectBox from 'components/select-box/select-box';
import NavigatorSelectionView from 'components/selection-navigator-view';
import MemberSingularSelection from 'components/tasks/commons/member-singular-selection';
import TaskDateDetails from 'components/tasks/commons/task-date-details';
import AssessmentForm from 'components/tasks/forms/assessment-form';
import ContactForm from 'components/tasks/forms/contact-form';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useModal } from 'contexts/modal-context/modal-context';
import useAuth from 'hooks/use-auth';
import useTask<PERSON><PERSON><PERSON>and<PERSON> from 'logics/tasks/use-form-handler';
import { useContext, useEffect, useState } from 'react';
import { TaskObject } from 'types/tasks-response-type';
import { isEmptyObject } from 'utils/helper';

interface AddEditTaskProps {
  add: boolean;
  task?: TaskObject;
}
function AddEditTask({ add, task }: AddEditTaskProps) {
  const [openMemberSelection, setOpenMemberSelection] = useState(false);
  const constants = useContext(ConstantsContext);
  const {
    formik,
    selectedMembers,
    selectedNavigators,
    setSelectedMembers,
    setSelectedNavigators,
  } = useTaskFormHandler({
    add,
    task,
  });
  const { user } = useAuth();
  const [assessmentTypes, setAssessmentTypes] = useState([]);
  const { showModal } = useModal();
  const [attachments, setAttachments] = useState<any[]>([]);
  const handleItems = async () => {
    const request = await API.ASSESSMENT.fetchTypes(user?.org?.id || '');
    const data = request.map((item: any) => ({
      key: item.key,
      title: item.name,
    }));
    setAssessmentTypes(data);
  };

  useEffect(() => {
    if (!isEmptyObject(formik.errors)) {
      showModal(
        'Missing Fields',
        `
          ${Object.keys(formik.errors).map(
    (key) => `${JSON.stringify((formik as any).errors[key])}`,
  )}
        `,
      );
      formik.setErrors({});
    }
  }, [JSON.stringify(formik.errors)]);

  useEffect(() => {
    if (user) {
      if (assessmentTypes.length === 0) {
        handleItems();
      }
    }
  }, [user]);

  useEffect(() => {
    formik.setFieldValue('attachments', attachments);
  }, [attachments]);

  useEffect(() => {
    if (task) {
      setAttachments(
        task.attachments || [],
      );
      formik.setFieldValue('initialAttachments', task.attachments || []);
    }
  }, [task]);

  return (
    <div className="pt-[17px] grid grid-cols-3 gap-7">
      <InnerPageHeader
        title="Task"
        add={add}
        addLabel="Add Task"
        updateLabel="Update Task"
        backRoute="/tasks"
        btnName="Save"
        callback={formik.handleSubmit}
      />
      <div className="grid grid-cols-3 auto-cols-max gap-2 items-start pl-8">
        <MemberSingularSelection
          setOpenMemberSelection={setOpenMemberSelection}
          selectedMembers={selectedMembers}
        />
        <p className="text-lg font-semibold col-span-3 mt-8">Task Details</p>
        <div className="col-span-3">
          <SelectBox
            keyVal="tmp.taskType"
            label="Task Type *"
            onChange={formik.handleChange}
            defaultValue={formik.values.tmp.taskType}
            minWidth={400}
            items={constants?.taskTypes}
          />
        </div>

        {formik.values.tmp.taskType.includes('assessment') && (
          <AssessmentForm
            formik={formik as typeof formik}
            items={assessmentTypes}
          />
        )}
        {formik.values.tmp.taskType.includes('contact') && (
          <ContactForm
            formik={formik as typeof formik}
            items={constants?.contactTaskTypes as any}
          />
        )}
        {formik.values.tmp.taskType.includes('visit') && (
          <AddressCollect formik={formik} />
        )}
        {!(
          formik.values.tmp.taskType.includes('contact')
          || formik.values.tmp.taskType.includes('assessment')
          || formik.values.tmp.taskType.includes('visit')
        ) && (
          <TextField
            onChange={formik.handleChange}
            value={formik.values.title}
            name="title"
            placeholder="Enter Title"
            className="min-w-[400px]"
            id="filled-basic"
            label="Title"
            variant="filled"
          />
        )}
        <div className="col-span-3 grid grid-flow-col gap-0 items-center auto-cols-max ml-[-8px]">
          <Checkbox
            checked={!!formik.values.urgent}
            onChange={formik.handleChange}
            name="urgent"
            color="primary"
          />
          <span className="text-sm m-0 font-normal">Mark task as urgent</span>
        </div>
      </div>

      <div className="col-span-3 mx-8">
        <TaskDateDetails formik={formik} constants={constants as never} />
      </div>
      <div className="col-span-3 mx-8 mt-4">
        <AttachmentPicker
          multiple
          attachments={attachments}
          attachmentsToRemove={[]}
          setPicture={setAttachments}
          setAttachmentsToRemove={() => {
            // do nothing
          }}
          edit
        />
      </div>
      <NavigatorSelectionView
        allowAdd={false}
        selectedNavigators={selectedNavigators}
        setSelectedNavigators={setSelectedNavigators}
        title="Available For"
        className="mx-8"
      />
      {selectedNavigators.length === 1 && (
        <div className="bg-[#F7F8F8] w-1/2 col-span-3 rounded-md mx-8">
          <p className="text-[#E42B57] italic text-sm  pl-3">
            Only one navigator selected. This will auto-assign them the task.
          </p>
        </div>
      )}
      <SelectMember
        open={openMemberSelection}
        setOpen={setOpenMemberSelection}
        selectedMembers={selectedMembers}
        disableMultiSelect
        setSelectMembers={setSelectedMembers}
        title="Select Member(s) to Add to Task"
      />
    </div>
  );
}

export default AddEditTask;
