import { TextField } from '@mui/material';
import SelectBox from 'components/select-box/select-box';

interface ContactFormProps {
    formik: any;
    items: {
        key: string;
        title: string;
    }[];
}

function ContactForm({ formik, items }: ContactFormProps) {
  const renderFields = () => {
    switch (formik.values.tmp.type) {
      case 'phone':
        return (
          <TextField
            onChange={formik.handleChange}
            value={formik.values.tmp.phone}
            name="tmp.phone"
            placeholder="Enter Phone Number"
            id="filled-basic"
            label="Phone Number"
            variant="filled"
          />
        );
      case 'any':
        return (
          <TextField
            onChange={formik.handleChange}
            value={formik.values.tmp.phone}
            name="tmp.phone"
            placeholder="Enter Phone Number"
            id="filled-basic"
            label="Phone Number"
            variant="filled"
          />
        );
      case 'televisit':
        return (
          <TextField
            onChange={formik.handleChange}
            value={formik.values.tmp.remoteLink}
            name="tmp.remoteLink"
            placeholder="https://..."
            id="filled-basic"
            label="Meeting Link"
            variant="filled"
          />
        );
        // eslint-disable-next-line no-fallthrough
      default:
        return <div />;
    }
  };
  return (
    <div className="grid grid-flow-row gap-2">
      <SelectBox
        keyVal="tmp.type"
        label="Contact Type"
        onChange={(e) => {
          formik.setFieldValue('tmp.type', e.target.value);
          console.log(e.target.value);
          items?.map((item) => {
            if (item.key === e.target.value) {
              formik.setFieldValue('taskDetails.type', item.key);
              formik.setFieldValue('taskDetails.kind', item.key);
            }
            return item;
          });
        }}
        defaultValue={formik.values.tmp.type}
        minWidth={400}
        items={items}
      />
      {renderFields()}
    </div>
  );
}

export default ContactForm;
