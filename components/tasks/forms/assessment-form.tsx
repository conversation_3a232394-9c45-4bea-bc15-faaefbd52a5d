/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
// import { Checkbox } from '@mui/material';
import {
  Box, List, ListItem, ListItemButton, ListItemText, Modal,
} from '@mui/material';
import AddressCollect from 'components/address-collect';
import CustomSearch from 'components/search/search';
import { useState } from 'react';

interface AssessmentFormProps {
    formik: any;
    items: {
        key: string;
        title: string;
    }[];
    }
function AssessmentForm({ formik, items }: AssessmentFormProps) {
  const [openModal, setOpenModal] = useState(false);
  const [data, setData] = useState<any>(items);
  const [placeholder, setPlaceholder] = useState('Encounter');

  const handleSearch = (term: string) => {
    if (term === '') {
      setData(items);
    }
    // eslint-disable-next-line max-len
    const filteredData = items.filter((item) => item.title.toLowerCase().includes(term.toLowerCase()));
    setData(filteredData);
  };

  const handleSelectedType = (item:any) => {
    setOpenModal(false);
    setPlaceholder(item.title);
    formik.setFieldValue('tmp.type', item.key);
    formik.setFieldValue('taskDetails.type', item.key);
    formik.setFieldValue('taskDetails.kind', item.key);
  };

  return (
    <div className="col-span-3">
      <div
        onClick={() => setOpenModal(true)}
        style={{
          border: '1px solid #E5E7EB',
          borderRadius: '8px',
        }}
        className="bg-white p-2 h-[52px] cursor-pointer w-[96%] col-span-3 rounded-md grid items-center"
      >
        <p
          className="m-0 t truncate-text"
          style={{
            whiteSpace: 'nowrap',
            overflow: 'hidden',
            textOverflow: 'ellipsis',
          }}
        >
          {placeholder}
        </p>
      </div>
      <AddressCollect formik={formik} />
      <Modal
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',

        }}
        open={openModal}
        onClose={() => setOpenModal(false)}
      >
        <Box
          sx={{
            width: '100%', maxWidth: 660, maxHeight: 500, bgcolor: 'background.paper',
          }}
          className="overflow-scroll min-h-[290px] relative"
        >
          <div className="sticky top-0 z-10">
            <CustomSearch
              onChange={(e) => {
                handleSearch(e.target.value);
              }}
              placeholder="Search"
              width={320}
            />
          </div>
          <List>
            {
            data.map((item: any) => (
              <ListItem disablePadding key={item.key}>
                <ListItemButton onClick={() => {
                  handleSelectedType(item);
                }}
                >
                  <ListItemText primary={item.title} />
                </ListItemButton>
              </ListItem>
            ))
           }
          </List>
        </Box>
      </Modal>
    </div>
  );
}

export default AssessmentForm;
