/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { <PERSON><PERSON>, TextField } from '@mui/material';
import SelectNavigator from 'components/navigators/select-navigator/select-navigator';
import CustomTable from 'components/table-components/table';
import { useModal } from 'contexts/modal-context/modal-context';
import useAddTeamLogic from 'logics/team/use-form-handler';
import React, { useEffect, useState } from 'react';
import { Teams } from 'types/teams-types';
import { isEmptyObject } from 'utils/helper';
import CheckIcon from '@mui/icons-material/Check';

interface Props {
  team: Teams;
  add: boolean;
}

function TeamsAddUpdate({ team, add }: Props) {
  const {
    formik, router, setSelectedNavigators, selectedNavigators,
  } = useAddTeamLogic(add, team?.id);
  const { showModal } = useModal();
  const [openSelectNavigators, setOpenSelectNavigators] = useState(false);

  useEffect(() => {
    if (!isEmptyObject(formik.errors)) {
      showModal(
        'Missing Fields',
        `
            ${Object.keys(formik.errors).map(
    (key) => `${(formik as any).errors[key]}`,
  )}
          `,
      );
    }
  }, [JSON.stringify(formik.errors)]);

  useEffect(() => {
    if (!isEmptyObject(team)) {
      formik.setValues({
        title: team?.name || '',
        navigators: (team?.navigators as any) || ([] as any),
      });
      setSelectedNavigators(team?.navigators as any);
    }
  }, [team]);

  const handleSelectNavigators = () => {
    setOpenSelectNavigators((prev) => !prev);
  };

  const handleAddNavigator = () => {
    router.push('/navigators/add-edit/add');
  };

  //   const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  //     formik.setFieldValue(e.target.name, e.target.value);
  //   };
  return (
    <div className="grid grid-cols-3 gap-2 pr-5">
      <div className="col-span-3 items-center grid grid-flow-col auto-cols-auto place-content-between w-full pt-4 pl-3">
        <p className="text-[21px] p-0 m-0 font-[500] col-span-3">
          <span
            className="font-[300] cursor-pointer"
            onClick={() => {
              router.replace('/teams');
            }}
          >
            Team
            <span>{' > '}</span>
          </span>
          {add ? 'Create New Team' : `Update Team (${team?.name})`}
        </p>
        <Button
          onClick={formik.submitForm}
          variant="contained"
          className="rounded-md !min-w-[100px]"
        >
          <CheckIcon className="w-5 h-5 mr-2" />
          <span className="font-[500]">Save</span>
        </Button>
      </div>
      <p className="text-lg font-[500] col-span-3 pl-8 mb-3">Team Details</p>
      <TextField
        placeholder="Enter Team Title"
        value={formik.values.title}
        id="filled-basic"
        label="Team Title"
        name="title"
        onChange={formik.handleChange}
        variant="filled"
        className="ml-8"
      />
      <div className="col-span-3 pl-8 pr-5">
        <div className="grid grid-flow-col auto-cols-auto text-xs items-center mt-10 mb-[12px]">
          <p className="text-lg font-[500] m-0">Navigators</p>
          {selectedNavigators.length > 0 && (
            <div className="grid grid-flow-col gap-1 auto-cols-max items-center justify-self-end text-[15px]">
              <span
                onClick={() => {
                  setOpenSelectNavigators(true);
                }}
                className="text-primary font-[500] cursor-pointer hover:text-[#ff8c20]"
              >
                Select Navigators
              </span>
              {/* <span
                className=" cursor-pointer pl-1"
                onClick={handleSelectNavigators}
              >
                OR
              </span> */}
              {/* <span
                className="text-primary cursor-pointer pl-1"
                onClick={handleAddNavigator}
              >
                + Add Navigator
              </span> */}
            </div>
          )}
        </div>
        {!(selectedNavigators.length > 0) && (
          <p className="text-[#646F79] m-0 p-0">
            This team does not have any navigators added.
            <span
              className="text-primary cursor-pointer pl-1 hover:text-[#ff8c20]"
              onClick={handleSelectNavigators}
            >
              Select Navigators
            </span>
            {/* <span
              className=" cursor-pointer pl-1"
              onClick={handleSelectNavigators}
            >
              OR
            </span>
            <span
              className="text-primary cursor-pointer pl-1"
              onClick={handleAddNavigator}
            >
              + Add Navigator
            </span> */}
          </p>
        )}
        {selectedNavigators.length > 0 && (
          <CustomTable
            variant="addsection-navigator-table"
            style={{
              borderBottom: 'none',
            }}
            headCellStyle={{
              borderBottom: '1px solid #E0E0E0',
            }}
            source={selectedNavigators}
            metadata={{
              setSelected: setSelectedNavigators,
              householdTitle: team?.name,
            }}
          />
        )}
        <SelectNavigator
          open={openSelectNavigators}
          setOpen={setOpenSelectNavigators}
          selectedNavigators={selectedNavigators}
          setSelectNavigators={setSelectedNavigators}
        />
      </div>
    </div>
  );
}

export default TeamsAddUpdate;
