import type React from 'react';
import Modal from '@mui/material/Modal';
import Search from 'components/search/search';
import { useEffect, useState } from 'react';
import { Close } from '@mui/icons-material';
import API from 'api/src/lib/api';
import CheckIcon from '@mui/icons-material/Check';
import { Button } from '@mui/material';
import CustomTable from 'components/table-components/table';
import { Teams } from 'types/teams-types';

interface PageInfo {
  per: number;
  total: number;
  page: number;
}

interface SelectTeamsProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedTeams?: Teams[];
  setSelectTeams?: React.Dispatch<React.SetStateAction<Teams[]>>;
  newCarePlan?: boolean;
  // eslint-disable-next-line no-unused-vars
  handleAdd?: (type: string, fields: any) => void;
}

function SelectTeams({
  open,
  setOpen,
  selectedTeams,
  setSelectTeams,
  newCarePlan,
  handleAdd,
}: SelectTeamsProps) {
  const [teams, setTeams] = useState<Teams[]>([]);
  const [teamData, setTeamData] = useState<Teams[]>([]);
  const [pageInfo, setPageInfo] = useState<PageInfo>({ per: 10, total: 0, page: 1 });
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState('name');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSearchTerm(value);
    setPage(1);

    const response: any = await API.TEAMS.fetchTeams(
      undefined, // token (if needed)
      undefined, // orgId (if needed)
      10,
      1,
      sortBy,
      sortDirection,
      value,
    );

    setTeams(response?.items || []);
    setTeamData(response?.items || []);
    setPageInfo({
      per: response?.metadata?.per || 10,
      total: response?.metadata?.total || 0,
      page: response?.metadata?.page || 1,
    });
  };

  useEffect(() => {
    const fetchTeams = async () => {
      if (!open) {
        setPage(1);
        setSortBy('name');
        setSortDirection('asc');
        setSearchTerm('');
        setTeams([]);
        setTeamData([]);
        return;
      }

      const response: any = await API.TEAMS.fetchTeams(
        undefined,
        undefined,
        10,
        page,
        sortBy,
        sortDirection,
        searchTerm,
      );

      setTeams(response?.items || []);
      setTeamData(response?.items || []);
      setPageInfo({
        per: response?.metadata?.per,
        total: response?.metadata?.total,
        page: response?.metadata?.page,
      });
    };

    fetchTeams();
  }, [open, page, sortBy, sortDirection, searchTerm]);

  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div className="bg-white mx-[5%] my-[2%] relative mb-10 rounded-lg h-[86%] flex flex-col overflow-hidden">
        <div className="h-[54px] flex items-center justify-center border-b border-gray-300 relative">
          <p>Select Team(s)</p>
          <Close className="absolute right-5 cursor-pointer" onClick={() => setOpen(false)} />
        </div>
        <div className="py-3 flex items-center justify-center border-b border-gray-300">
          <Search
            placeholder="Search teams..."
            onChange={handleSearch}
          />
        </div>
        <div className="flex-grow overflow-auto pb-[70px]">
          <CustomTable
            source={teamData}
            variant="main-team-table"
            selected={selectedTeams}
            setSelected={setSelectTeams}
            metadata={{
              setSelected: setSelectTeams,
              list: selectedTeams,
              disableMultiSelect: true,
            }}
            select
            pageInfo={pageInfo}
            sortBy={sortBy}
            sortDirection={sortDirection}
            onPageChange={(newPage) => setPage(newPage)}
            onSortChange={(newSortBy, newDirection) => {
              setSortBy(newSortBy);
              setSortDirection(newDirection);
              setPage(1);
            }}
          />
        </div>
        <div
          className="h-[60px] shrink-0 border-t border-gray-300 flex items-center justify-end px-5"
          style={{ position: 'absolute', width: '100%', bottom: 0 }}
        >
          <Button
            onClick={() => {
              if (newCarePlan) {
                handleAdd?.('team', { team: selectedTeams });
              }
              setOpen(false);
            }}
            variant="contained"
            className="bg-[#2D62ED] text-white rounded-md w-[122px]"
          >
            <div className="flex items-center gap-2">
              <CheckIcon />
              Save
            </div>
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default SelectTeams;
