/* eslint-disable react/no-array-index-key */
import React, { useState } from 'react';
import {
  Checkbox,
  FormControl, InputLabel, MenuItem, Select,
} from '@mui/material';
import { styled } from '@mui/system';
import { AttachmentType } from 'contexts/constant-context/type';

const StyledFormControl = styled(FormControl)({
  backgroundColor: 'white',
});

const StyledInputLabel = styled(InputLabel)({
  backgroundColor: 'transparent',
  '&.Mui-focused': {
    backgroundColor: 'transparent',
  },
  '&.MuiInputLabel-shrink': {
    backgroundColor: 'transparent',
  },
});

const StyledSelect = styled(Select)({
  backgroundColor: 'white',
  borderRadius: '8px',
  border: '1px solid #D5DCE2',
  height: '56px',
  '& .MuiFilledInput-root': {
    backgroundColor: 'white',
    border: 'none',
    '&:hover': {
      backgroundColor: 'white',
    },
  },
  '& .MuiFilledInput-underline': {
    '&:before': {
      borderBottom: 'none',
    },
    '&:after': {
      borderBottom: 'none',
    },
    '&:hover:not(.Mui-disabled):before': {
      borderBottom: 'none',
    },
  },
  '&:hover': {
    backgroundColor: 'white',
  },
});

interface SelectBoxProps {
  // eslint-disable-next-line react/no-unused-prop-types, no-unused-vars
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  minWidth?: number | string;
  tags?: AttachmentType[];
  items?: {
    key: string;
    title: string;
  }[];
  keyVal: string;
  label: string;
  defaultValue?: string;

}
function SelectBoxMulti({
  onChange, label, keyVal, items = [], minWidth = '100%', defaultValue = '', tags,
}: SelectBoxProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>([...defaultValue]);

  const handleChange = (event: React.ChangeEvent<{ value:any }>) => {
    const value:any = event.target.value as string[];
    setSelectedItems(value);
    if (onChange) onChange(value);
  };

  return (
    <StyledFormControl className="w-5" variant="filled" sx={{ minWidth }}>
      <StyledInputLabel id={`select-input-label-${keyVal}`}>
        {label}
      </StyledInputLabel>
      <StyledSelect
        name={keyVal}
        multiple
        style={{
          // first letter uppercase
          textTransform: 'capitalize',
        }}
        className="first-letter:uppercase"
        value={selectedItems}
        renderValue={
          (selected:any) => {
            const renderItems: string[] = [];
            console.log(selected, 'selected');
            selected.forEach((item:string[]) => {
              renderItems.push(tags?.find((tag:any) => tag.key === item)?.title as any);
            });
            return renderItems.join(', ');
          }
        }
        labelId={`select-input-label-${keyVal}`}
        id={`select-input-${keyVal}`}
        onChange={handleChange as any}
      >
        {items.map((item) => (
          <MenuItem key={item.key} value={item.key.toLowerCase()}>
            <Checkbox checked={selectedItems.indexOf(item.key.toLowerCase()) > -1} />
            <span className="first-letter:uppercase">{item.title}</span>
          </MenuItem>
        ))}
      </StyledSelect>

    </StyledFormControl>

  );
}

export default SelectBoxMulti;
