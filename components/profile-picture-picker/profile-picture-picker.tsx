import React, { useEffect, useState, useRef } from 'react';
import { Button, Avatar } from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';

interface ProfilePicturePickerProps {
  setPicture: React.Dispatch<React.SetStateAction<string>>;
  picture: string;
}

const ProfilePicturePicker = React.forwardRef(({ setPicture, picture }:
   ProfilePicturePickerProps, ref: any) => {
  const [profilePicture, setProfilePicture] = useState('');
  const [buttonText, setButtonText] = useState('Choose Profile Photo');
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Allow parent components to trigger file input click via ref
  React.useImperativeHandle(ref, () => ({
    clickInput() {
      fileInputRef.current?.click();
    },
  }));

  const handleFileChange = (event: any) => {
    if (event.target.files && event.target.files[0]) {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        setPicture(e.target.result);
        setProfilePicture(e.target.result);
        setButtonText('Edit Photo');
      };

      reader.readAsDataURL(event.target.files[0]);
    }
  };

  useEffect(() => {
    if (picture) {
      setProfilePicture(picture);
      setButtonText('Edit Photo');
    } else {
      setProfilePicture('');
    }
  }, [picture]);

  return (
    <div className="flex items-center gap-6">
      {profilePicture ? (
        <div className="relative grid grid-flow-col">
          <Avatar
            alt="Profile Picture"
            src={profilePicture}
            sx={{
              width: 192,
              height: 192,
              backgroundColor: '#8996A2',
            }}
          />
          <Button
            variant="outlined"
            color="primary"
            component="label"
            className="z-9 hover:border-none hover:bg-[transparent] grid grid-flow-row auto-cols-max items-center"
            sx={{
              color: '#0ABF89',
              border: 'none',
              right: '-20px',
              boxShadow: 'none!important',
              borderRadius: '8px',
            }}
          >
            {buttonText === 'Edit Photo' && <EditIcon className="text-primary text-3xl" />}
            <input type="file" ref={fileInputRef} hidden onChange={handleFileChange} />
          </Button>
        </div>
      ) : (
        <div className="w-[192px] h-[192px] rounded-full bg-[#F7F8F8] relative grid items-center justify-center">
          <Button
            variant="outlined"
            color="primary"
            component="label"
            className="z-10 hover:border-none hover:bg-[transparent] grid grid-flow-row auto-cols-max items-center"
            sx={{
              color: '#0ABF89',
              border: 'none',
              boxShadow: 'none!important',
              borderRadius: '8px',
            }}
          >
            <div className="bg-icon-cam h-[78px] w-full rounded-full bg-no-repeat z-10 bg-center" />
            <p className="font-[600] m-0 p-0 text-primary">Add a Photo</p>
            <input type="file" ref={fileInputRef} hidden onChange={handleFileChange} />
          </Button>
        </div>
      )}
    </div>
  );
});

export default ProfilePicturePicker;
