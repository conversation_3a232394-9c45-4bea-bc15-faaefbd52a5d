/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { Checkbox, TextField } from '@mui/material';
import API from 'api/src/lib/api';
import AddressCollect from 'components/address-collect';
import SelectMember from 'components/members/select-member/select-member';
import SelectBox from 'components/select-box/select-box';
import NavigatorSelectionView from 'components/selection-navigator-view';
import MemberSingularSelection from 'components/tasks/commons/member-singular-selection';
import TaskDateDetails from 'components/tasks/commons/task-date-details';
import AssessmentForm from 'components/tasks/forms/assessment-form';
import ContactForm from 'components/tasks/forms/contact-form';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useModal } from 'contexts/modal-context/modal-context';
import useAuth from 'hooks/use-auth';
import useNavigatorTaskFormHandler from 'logics/navigator/use-navigator-task-form-handler';
import React, { useContext, useEffect, useState } from 'react';
import { TaskObject } from 'types/tasks-response-type';
import { isEmptyObject } from 'utils/helper';

interface AddEditTaskProps {
  add: boolean;
  task?: TaskObject;
  // eslint-disable-next-line no-unused-vars
  renderHeader?: (handleSubmit: () => void) => React.ReactNode;
}

function AddEditNavigatorTask({ add, task, renderHeader }: AddEditTaskProps) {
  const [openMemberSelection, setOpenMemberSelection] = useState(false);
  const constants = useContext(ConstantsContext);
  const {
    formik,
    selectedMembers,
    selectedNavigators,
    setSelectedMembers,
    setSelectedNavigators,
  } = useNavigatorTaskFormHandler({
    add,
    task,
  });
  const { user } = useAuth();
  const [assessmentTypes, setAssessmentTypes] = useState([]);
  const { showModal } = useModal();
  const handleItems = async () => {
    const request = await API.ASSESSMENT.fetchTypes(user?.org?.id || '');
    const data = request.map((item: any) => ({
      key: item.key,
      title: item.name,
    }));
    setAssessmentTypes(data);
  };

  useEffect(() => {
    if (!isEmptyObject(formik.errors)) {
      showModal(
        'Missing Fields',
        `
          ${Object.keys(formik.errors).map(
    (key) => `${JSON.stringify((formik as any).errors[key])}`,
  )}
        `,
      );
      formik.setErrors({});
    }
  }, [JSON.stringify(formik.errors)]);

  useEffect(() => {
    if (user) {
      if (assessmentTypes.length === 0) {
        handleItems();
      }
    }
  }, [user]);

  return (
    <div className="grid grid-cols-3 gap-7">
      {/* Render header from UpdateTask, passing handleSubmit */}
      {renderHeader && renderHeader(formik.handleSubmit)}
      <div className="grid grid-cols-3 auto-cols-max gap-2 items-start pl-8 pr-5">
        <MemberSingularSelection
          setOpenMemberSelection={setOpenMemberSelection}
          selectedMembers={selectedMembers}
        />
        <p className="text-sm font-semibold col-span-3 pl-8">Task Details</p>
        <div className="col-span-3">
          <SelectBox
            keyVal="tmp.taskType"
            label="Task Type *"
            onChange={formik.handleChange}
            defaultValue={formik.values.tmp.taskType}
            minWidth={400}
            items={constants?.taskTypes}
          />
        </div>

        {formik.values.tmp.taskType === 'assessment' && (
          <AssessmentForm
            formik={formik as typeof formik}
            items={assessmentTypes}
          />
        )}
        {formik.values.tmp.taskType === 'contact' && (
          <ContactForm
            formik={formik as typeof formik}
            items={constants?.contactTaskTypes as any}
          />
        )}
        {formik.values.tmp.taskType === 'visit' && (
          <AddressCollect formik={formik} />
        )}
        {formik.values.tmp.taskType === 'custom' && (
          <TextField
            onChange={formik.handleChange}
            value={formik.values.title}
            name="title"
            placeholder="Enter Title"
            className="min-w-[400px]"
            id="filled-basic"
            label="Title"
            variant="filled"
          />
        )}
        <div className="col-span-3 grid grid-flow-col gap-0 items-center auto-cols-max">
          <Checkbox
            checked={!!formik.values.urgent}
            onChange={formik.handleChange}
            name="urgent"
            color="primary"
          />
          <span className="text-sm m-0 font-normal">Mark task as urgent</span>
        </div>
      </div>

      <div className="col-span-3 pl-8 pr-5">
        <TaskDateDetails formik={formik} constants={constants as never} />
      </div>
      <NavigatorSelectionView
        allowAdd={false}
        selectedNavigators={selectedNavigators}
        setSelectedNavigators={setSelectedNavigators}
        title="Available For"
        className="pl-8 pr-5"
      />
      {selectedNavigators.length === 1 && (
        <div className="bg-[#F7F8F8] col-span-3 rounded-md pr-5 ml-8 w-fit">
          <p className="text-[#E42B57] italic text-sm  pl-3">
            Only one navigator selected. This will auto-assign them the task.
          </p>
        </div>
      )}
      <SelectMember
        open={openMemberSelection}
        setOpen={setOpenMemberSelection}
        selectedMembers={selectedMembers}
        disableMultiSelect
        setSelectMembers={setSelectedMembers}
        title="Select Member(s) to Add to Task"
      />
    </div>
  );
}

export default AddEditNavigatorTask;
