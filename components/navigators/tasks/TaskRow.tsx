import { Avatar } from '@mui/material';
import { useRouter } from 'next/router';
import { capitalizeWords, timeDifferenceFullDaysWord } from 'utils/helper';
import { Navigator } from 'types/navigators-types';

interface Task {
  id: string;
  creator: {
    profile: string;
  };
  title: string;
  desc: string;
  dueAtEpoc: number;
  msg: string;
  status: string;
  completedBy: Array<{ profile: string }>;
  assignee: {
    profile: string;
  };
}

interface TaskRowProps {
  task: Task;
  rowClickUrl?: string;
  navigator?: Navigator;
}

function TaskRow({ task, rowClickUrl, navigator }: TaskRowProps) {
  const router = useRouter();
  const {
    id,
    creator,
    title,
    desc,
    dueAtEpoc,
    msg,
    status,
    completedBy,
    assignee,
  } = task;

  const handleClick = (taskId: string) => {
    if (navigator) {
      sessionStorage.setItem(
        'navigatorName',
        capitalizeWords(`${navigator.firstName} ${navigator.lastName}`, true),
      );
      sessionStorage.setItem('navigatorId', navigator.id);
    }
    router.push(rowClickUrl || `/tasks/${taskId}/detail`);
  };

  const handleClass = (dueDate: number) => {
    const timeDiff = timeDifferenceFullDaysWord(dueDate);
    const style = {
      backgroundColor:
        Math.sign(parseInt(timeDiff, 10)) === 1 ? '#F7F8F8' : '#E42B57',
      color: Math.sign(parseInt(timeDiff, 10)) === 1 ? '#000' : '#fff',
    };
    const className = `absolute p-1 m-1 items-center bg-origin-content right-1 top-1 ${
      Math.sign(parseInt(timeDiff, 10)) === 1
        ? 'bg-icon-clock'
        : 'bg-icon-clock-white'
    } bg-no-repeat bg-left  h-auto rounded-md`;

    if (status === 'completed') {
      return {
        style: {
          color: '#008390',
        },
        className:
          'absolute px-1 py-0 bg-icon-check bg-[#E3F8FA] m-1 items-center bg-origin-content right-1 top-1  bg-no-repeat bg-left  h-auto rounded-md',
      };
    }
    return { style, className };
  };

  function setAssignedMembers() {
    const assignedMemberArray: string[] = [];
    completedBy.forEach((assignedMember) => {
      assignedMemberArray.push(assignedMember.profile);
    });
    return assignedMemberArray;
  }
  return (
    <div
      onClick={() => {
        handleClick(id);
      }}
      key={id}
      className="grid grid-flow-col cursor-pointer pl-10 pt-5 auto-cols-max place-content-between w-full gap-2 borderLine items-center"
    >
      <div className="grid grid-flow-col gap-2 auto-cols-max items-center relative">
        <div className="flex items-center">
          <Avatar
            src={assignee.profile}
            className="bg-primary h-10 w-10 p-0 m-0"
          />
          <div className="flex flex-col pl-3">
            <p className="p-0 m-0 font-[500] text-sm">
              {capitalizeWords(title)}
            </p>
            <p className="p-0 m-0 font-[300] text-xs">
              {capitalizeWords(desc)}
            </p>
            <p className="p-0 m-0 font-[300] text-xs">
              {new Date(dueAtEpoc * 1000).toLocaleString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: 'numeric',
                minute: '2-digit',
                hour12: true,
              })}
            </p>
          </div>
          <p className="p-0 m-0 text-sm max-w-screen-lg overflow-hidden text-ellipsis clamp-3">
            {msg}
          </p>
        </div>
      </div>
      <div className="grid-flow-col grid items-center gap-2 self-start mt-[-6px] relative">
        <div
          style={handleClass(dueAtEpoc).style}
          className={handleClass(dueAtEpoc).className}
        >
          <p className="m-0 text-[11px] pt-0 font-normal pl-4 whitespace-nowrap">
            {status === 'completed'
              ? 'Done'
              : timeDifferenceFullDaysWord(dueAtEpoc)}
          </p>
        </div>
        <div className="absolute top-0 right-0 p-2 pt-10">
          {setAssignedMembers().length ? (
            <div className="flex">
              {setAssignedMembers().map((imageUrl) => (
                <Avatar
                  key={imageUrl}
                  src={imageUrl}
                  sx={{
                    width: 24,
                    height: 24,
                    border: '2px solid white',
                  }}
                />
              ))}
            </div>
          ) : (
            <div />
          )}
        </div>
      </div>
    </div>
  );
}

export default TaskRow;
