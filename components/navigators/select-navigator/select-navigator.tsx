import React, { useEffect, useState } from 'react';
import Modal from '@mui/material/Modal';
import Search from 'components/search/search';
import { Close } from '@mui/icons-material';
import API from 'api/src/lib/api';
import CheckIcon from '@mui/icons-material/Check';
import { Button } from '@mui/material';
import { Navigator } from 'types/navigators-types';
import CustomTable from 'components/table-components/table';

interface PageInfo {
  per: number;
  total: number;
  page: number;
}

interface SelectNavigatorProps {
  open: boolean;
  modalLabel?: string;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  selectedNavigators?: Navigator[];
  setSelectNavigators?: React.Dispatch<React.SetStateAction<Navigator[]>>;
}

function SelectNavigator({
  open,
  setOpen,
  setSelectNavigators,
  selectedNavigators,
  modalLabel = 'Select Navigator(s) to Add to Team',
}: SelectNavigatorProps) {
  const [navigatorData, setNavigatorData] = useState<Navigator[]>([]);
  const [pageInfo, setPageInfo] = useState<PageInfo>({
    per: 10,
    total: 0,
    page: 1,
  });
  const [page, setPage] = useState(1);
  const [sortBy, setSortBy] = useState('firstName');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [searchTerm, setSearchTerm] = useState('');

  const handleSearch = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSearchTerm(value);
    setPage(1); // reset to page 1 on search
  };

  const fetchNavigators = async () => {
    if (!open) return;

    const response: any = await API.USER.getAllUsers(
      undefined,
      undefined,
      10,
      page,
      sortBy,
      sortDirection,
      searchTerm,
    );

    setNavigatorData(response?.items || []);
    setPageInfo({
      per: response?.metadata?.per || 10,
      total: response?.metadata?.total || 0,
      page: response?.metadata?.page || 1,
    });
  };

  useEffect(() => {
    if (open) fetchNavigators();
    else {
      setNavigatorData([]);
      setPage(1);
      setSortBy('firstName');
      setSortDirection('asc');
      setSearchTerm('');
    }
  }, [open, page, sortBy, sortDirection, searchTerm]);

  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div className="bg-white m-[4%] relative mb-10 rounded-lg h-[86%] flex flex-col overflow-hidden">
        <div className="h-[54px] flex items-center justify-center border-b border-gray-300 relative">
          <p>{modalLabel}</p>
          <Close
            className="absolute right-5 cursor-pointer"
            onClick={() => setOpen(false)}
          />
        </div>

        <div className="h-[64px] flex items-center justify-center border-b border-gray-300">
          <Search
            width={640}
            placeholder="Search unassigned navigators..."
            onChange={handleSearch}
          />
        </div>

        <div className="flex-grow overflow-auto">
          <CustomTable
            source={navigatorData}
            selected={selectedNavigators}
            setSelected={setSelectNavigators}
            variant="main-navigator-table"
            select
            metadata={{
              setSelected: setSelectNavigators,
            }}
            pageInfo={pageInfo}
            sortBy={sortBy}
            sortDirection={sortDirection}
            onPageChange={(newPage) => setPage(newPage)}
            onSortChange={(newSortBy, newDirection) => {
              setSortBy(newSortBy);
              setSortDirection(newDirection);
              setPage(1);
            }}
          />
        </div>
        <div className="h-[60px] shrink-0 bg-white border-t border-gray-300 flex items-center justify-end px-5">
          <Button
            onClick={() => {
              setOpen(false);
            }}
            variant="contained"
            className="bg-[#2D62ED] text-white rounded-md w-[122px]"
          >
            <div className="flex items-center gap-2">
              <CheckIcon />
              Save
            </div>
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default SelectNavigator;
