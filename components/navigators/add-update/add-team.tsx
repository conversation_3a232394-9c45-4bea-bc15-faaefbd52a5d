import React, { useEffect, useState } from 'react';
import { Button, Modal } from '@mui/material';
import { Close } from '@mui/icons-material';
import CheckIcon from '@mui/icons-material/Check';
import Search from 'components/search/search';
import CustomTable from 'components/table-components/table';
import { Teams } from 'types/teams-types';

interface AddNetworkInsuranceProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  setSelectedTeams: React.Dispatch<React.SetStateAction<Teams[]>>;
  selectedTeams: Teams[];
  teams: Teams[];
}

function AddNavigatorTeam({
  open,
  setOpen,
  setSelectedTeams,
  selectedTeams,
  teams,
}: AddNetworkInsuranceProps) {
  const [tempSelectedTeams, setTempSelectedTeams] = useState<Teams[]>([]);
  const [teamsList, setTeamsList] = useState<Teams[]>(teams);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTeamsList(
      // eslint-disable-next-line max-len
      teams?.filter((team) => team.name?.toLowerCase().includes(e.target.value.toLowerCase())) || [],
    );
  };

  const handleSave = () => {
    setSelectedTeams(tempSelectedTeams);
    setOpen(false);
  };

  useEffect(() => {
    setTempSelectedTeams(selectedTeams);
    setTeamsList(teams || []);
  }, [selectedTeams, teams]);

  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div className="bg-white my-[5%] mx-[15%] relative min-h-[80%] mb-10 rounded-lg overflow-hidden">
        <div
          style={{
            borderBottom: '1px solid #E0E0E0',
          }}
          className="h-[60px] items-center relative justify-center grid grid-flow-col"
        >
          <p className="font-[500]">Assign to Team</p>
          <Close
            className="absolute right-5 cursor-pointer"
            onClick={() => setOpen(false)}
          />
        </div>
        <div
          style={{
            borderBottom: '1px solid #E0E0E0',
          }}
          className="h-[60px] items-center relative justify-center grid grid-flow-col"
        >
          <Search
            placeholder="Search teams"
            onChange={handleSearch}
          />
        </div>

        <div
          id="footer-area"
          style={{
            borderTop: '1px solid #E0E0E0',
            width: '100%',
            zIndex: 2,
            bottom: 0,
          }}
          className="bg-white justify-end grid grid-flow-col absolute pr-5 h-[64px] items-center"
        >
          <Button
            onClick={handleSave}
            variant="contained"
            className="text-white rounded-md"
          >
            <div className="grid grid-flow-col items-center gap-2">
              <CheckIcon />
              Save
            </div>
          </Button>
        </div>
        <div className="overflow-y-auto max-h-[50vh]">
          <CustomTable
            source={teamsList || []}
            variant="add-navigator-team-table"
            headCellStyle={{
              fontSize: '13px',
              color: '#747A7A',
              padding: '30px 20px 4px',
            }}
            tableCellStyle={{
              fontSize: '13px',
              color: '#001018',
              padding: '0px 9px',
              border: 'none',
            }}
            select
            metadata={{
              setSelected: setTempSelectedTeams,
            }}
            selected={tempSelectedTeams}
          />
        </div>
      </div>
    </Modal>
  );
}

export default AddNavigatorTeam;
