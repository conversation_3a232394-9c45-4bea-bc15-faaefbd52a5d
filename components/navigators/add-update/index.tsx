/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { <PERSON><PERSON>, TextField } from '@mui/material';
import ProfilePicturePicker from 'components/profile-picture-picker/profile-picture-picker';
import SelectBox from 'components/select-box/select-box';
import { useModal } from 'contexts/modal-context/modal-context';
import useAddNavigatorLogic from 'logics/navigator/use-form-handler';
import React, { useEffect, useState } from 'react';
import { Navigator } from 'types/navigators-types';
import { Teams } from 'types/teams-types';
import { getLatestImage, isEmptyObject } from 'utils/helper';
import CustomTable from 'components/table-components/table';
import AddNavigatorTeam from './add-team';

interface Props {
  navigator: Navigator;
  add: boolean;
  teams: Teams[];
}

function NavigatorAddUpdate({ navigator, add, teams }: Props) {
  const {
    formik, router, setPicture, constants, picture,
  } = useAddNavigatorLogic(add, navigator?.id);

  const { showModal } = useModal();
  const [openAddTeam, setOpenAddTeam] = useState(false);

  useEffect(() => {
    if (!isEmptyObject(formik.errors)) {
      showModal('Missing Fields', Object.values(formik.errors).join('\n'));
    }
  }, [JSON.stringify(formik.errors)]);

  useEffect(() => {
    if (!isEmptyObject(navigator)) {
      formik.setValues({
        phone:
          navigator.phones.length > 0
            ? (navigator as any)?.phones
              .filter((x: any) => x?.label === 'main')
              .sort(
                (a: any, b: any) => new Date(b.createdAt).getTime()
                    - new Date(a.createdAt).getTime(),
              )[0]?.number
            : '',
        firstName: navigator.firstName,
        lastName: navigator.lastName,
        email: navigator.email,
        role: navigator.roles.length > 0 ? navigator.roles[0] : '',
        navigatorTeams: (navigator.teams as any[]) || [],
      });

      const attachment = getLatestImage(navigator?.attachments, 'userProfile');
      setPicture(attachment);
    }
  }, [navigator]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    formik.setFieldValue(e.target.name, e.target.value);
  };

  function handleEdit() {
    router.push(`/navigators/add-edit/${navigator?.id}`);
  }

  const handleSubmit = async () => {
    // const teamIds = formik.values.navigatorTeams.map((team) => team.id);
    // await formik.setFieldValue('navigatorTeams', teamIds);
    formik.submitForm();
  };

  return (
    <div className="pt-4 pl-3 pr-5">
      <div className="col-span-3 items-center grid grid-flow-col auto-cols-auto place-content-between w-full">
        <p className="text-[21px] p-0 m-0 font-[500] col-span-3">
          <span
            className="font-[300] cursor-pointer"
            onClick={() => router.replace('/navigators')}
          >
            Navigators
          </span>
          {` > ${
            add
              ? 'Add Navigator'
              : `Update Navigator (${navigator?.firstName} ${navigator?.lastName})`
          }`}
        </p>
        <Button
          onClick={handleSubmit}
          variant="contained"
          className="rounded-md h-11 font-[500] text-sm"
        >
          <span className="font-[500]">Save</span>
        </Button>
      </div>
      <p className="text-lg font-[600] col-span-3 mx-5 pt-2">
        Navigator Details
      </p>
      <div className="col-span-3 pl-5 mb-5">
        <ProfilePicturePicker picture={picture} setPicture={setPicture} />
      </div>
      <div className="flex flex-col mx-5">
        <div className="flex flex-wrap w-full justify-between">
          <TextField
            name="firstName"
            onChange={formik.handleChange}
            value={formik.values.firstName}
            placeholder="Enter First Name"
            label="First Name"
            variant="filled"
            className="md-lg:w-[32%] min-w-[200px] w-[48%] mb-4"
          />
          <TextField
            name="lastName"
            onChange={formik.handleChange}
            value={formik.values.lastName}
            placeholder="Enter Last Name"
            label="Last Name"
            variant="filled"
            className="md-lg:w-[32%] min-w-[200px] w-[48%] mb-4"
          />
          <TextField
            name="email"
            onChange={formik.handleChange}
            value={formik.values.email}
            placeholder="Enter Email"
            label="Email"
            variant="filled"
            className="md-lg:w-[32%] min-w-[200px] w-[48%] mb-4"
          />
          <TextField
            name="phone"
            onChange={formik.handleChange}
            value={formik.values.phone}
            placeholder="Enter Phone Number"
            label="Phone Number"
            variant="filled"
            className="md-lg:w-[32%] min-w-[200px] w-[48%] mb-4"
          />
          <div className="md-lg:w-[32%] min-w-[200px] w-[48%] mb-4">
            <SelectBox
              label="Role"
              keyVal="role"
              defaultValue={formik.values.role}
              onChange={handleChange}
              items={constants?.roles}
            />
          </div>
          <div className="md-lg:w-[32%] min-w-[200px] w-[48%] mb-4" />
        </div>
      </div>
      <div className="col-span-3 grid grid-flow-col auto-cols-auto text-xs items-center mx-5 justify-between">
        <p className="text-lg font-[600] col-span-3 mt-6 mb-3">
          Teams
          {' '}
          {navigator?.teams?.length > 0 && (
            <span>{`(${formik.values.navigatorTeams?.length})`}</span>
          )}
        </p>
        {navigator?.teams?.length > 0 && (
          <div className="grid grid-flow-col gap-1 auto-cols-max text-xs items-center justify-self-end">
            <span
              onClick={() => {
                setOpenAddTeam(true);
              }}
              className="text-primary font-[500] cursor-pointer text-[15px] hover:text-[#ff8c20]"
            >
              <span className="text-[18px]">+ </span>
              Assign to Team
            </span>
          </div>
        )}
      </div>
      <div className="mx-5 pb-10">
        {formik.values.navigatorTeams.length > 0 ? (
          <CustomTable
            source={formik.values.navigatorTeams as Teams[]}
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            variant="navigator-team-table-add-update"
            style={{
              border: 'solid 1px lightgray',
              borderRadius: '10px',
              borderBottom: 'none',
            }}
            headCellStyle={{
              padding: '4px 16px',
              fontSize: '13px',
              color: '#747A7A',
            }}
            tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
            handleDelete={(team) => {
              formik.setFieldValue('navigatorTeams', formik.values.navigatorTeams.filter((t) => t.id !== team.id));
            }}
          />
        ) : (
          <p className="m-0">
            This navigator is not assigned to a team.
            {' '}
            {add ? (
              <span
                onClick={() => {
                  setOpenAddTeam(true);
                }}
                className="text-primary font-[500] cursor-pointer text-[15px] hover:text-[#ff8c20]"
              >
                <span className="text-[18px]">+ </span>
                Assign to Team
              </span>
            ) : (
              <span
                onClick={() => handleEdit()}
                className="text-primary font-[500] cursor-pointer text-[15px] hover:text-[#ff8c20]"
              >
                Edit Profile to assign.
              </span>
            )}
          </p>
        )}
      </div>
      <AddNavigatorTeam
        open={openAddTeam}
        setOpen={setOpenAddTeam}
        setSelectedTeams={(teamsSelected) => formik.setFieldValue('navigatorTeams', teamsSelected)}
        selectedTeams={formik.values.navigatorTeams}
        teams={teams}
      />
    </div>
  );
}

export default NavigatorAddUpdate;
