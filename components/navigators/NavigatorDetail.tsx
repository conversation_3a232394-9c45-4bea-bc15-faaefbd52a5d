/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable max-len */
import { Avatar, Button, Switch, Table, TableBody, TableCell, TableContainer, TableHead, TableRow, Paper } from '@mui/material';
import { Navigator } from 'types/navigators-types';
import { useEffect, useState } from 'react';
import { getLatestImage, capitalizeWords } from 'utils/helper';
import EditIcon from '@mui/icons-material/Edit';
import router from 'next/router';
import TaskRow from 'components/tasks/TaskRow';
import CustomTable from 'components/table-components/table';
import { Teams } from 'types/teams-types';
import useAuth from 'hooks/use-auth';
import API from 'api/src/lib/api';

interface NavigatorDetailProps {
  navigator: Navigator;
  tasks: any;
}

function NavigatorDetail({ navigator, tasks = [] }: NavigatorDetailProps) {
  const [picture, setPicture] = useState<string | null>('');
  const [teamData, setTeamData] = useState(navigator.teams);
  const [accessPermissions, setAccessPermissions] = useState(navigator?.meta?.access || []);
  const [isUpdatingAccess, setIsUpdatingAccess] = useState(false);
  const auth = useAuth();

  const availableModules = ['tasks', 'chats', 'households', 'navigators', 'reporting', 'teams', 'networks', 'members'];

  const tabs = [
    {
      label: 'Pending',
      value: 'pending',
    },
    {
      label: 'Picked up',
      value: 'picked up',
    },
  ];
  const [activeTab, setActiveTab] = useState('picked up');
  const mainPhone = navigator?.phones?.length
    ? navigator?.phones[navigator.phones.length - 1]
    : { number: '' };
  const pickedUp = tasks['picked up'] ?? [];
  const pending = tasks.pending ?? [];
  const [openAddTeam, setOpenAddTeam] = useState(false);

  // Check if current user is admin
  const isCurrentUserAdmin = () => {
    return auth.user?.roles?.some(role => role.toLowerCase().includes('admin')) || false;
  };

  // Get description for each module
  const getModuleDescription = (module: string) => {
    const descriptions: Record<string, string> = {
      tasks: 'Manage and assign tasks to members and navigators',
      chats: 'Access chat conversations and messaging',
      households: 'View and manage household information',
      navigators: 'Manage navigators',
      reporting: 'View reports and analytics',
      teams: 'Manage teams and households',
      networks: 'Access network connections and relationships',
      members: 'View and manage member profiles and data',
    };
    return descriptions[module] || 'Access to this module';
  };

  // Handle access permission toggle
  const handleAccessToggle = async (module: string) => {
    if (!isCurrentUserAdmin()) return;

    setIsUpdatingAccess(true);
    try {
      const newAccessPermissions = accessPermissions.includes(module)
        ? accessPermissions.filter((m: string) => m !== module)
        : [...accessPermissions, module];

      await API.USER.updateUser(navigator.id, {
        meta: {
          ...navigator.meta,
          access: newAccessPermissions,
        },
      });

      setAccessPermissions(newAccessPermissions);
    } catch (error) {
      console.error('Failed to update access permissions:', error);
    } finally {
      setIsUpdatingAccess(false);
    }
  };

  useEffect(() => {
    if (navigator?.teams && navigator?.roles) {
      const updatedTeams = navigator.teams.map((team) => ({
        ...team, // Spread the existing team fields
        role: navigator.roles[0], // Add the new field "role"
      }));
      setTeamData(updatedTeams);
    }
  }, [JSON.stringify(navigator.teams), JSON.stringify(navigator.roles)]);

  useEffect(() => {
    if (navigator?.attachments) {
      setPicture(getLatestImage(navigator?.attachments, 'userProfile'));
    }
  }, [navigator]);

  useEffect(() => {
    setAccessPermissions(navigator?.meta?.access || []);
  }, [navigator?.meta?.access]);

  function handleEdit() {
    router.push(`/navigators/add-edit/${navigator?.id}`);
  }

  return (
    <>
      <div className="flex pl-8 pr-10">
        <div className="mr-16">
          <Avatar
            alt={navigator?.firstName}
            className="w-64 h-64 m-0 p-0 cursor"
            src={picture as string}
          />
        </div>
        <div className="flex flex-col w-full">
          <div className="flex justify-between items-center mb-3">
            <p className="font-[500] text-3xl first-letter:uppercase">
              {navigator?.firstName}
              {' '}
              {navigator?.lastName}
            </p>
            <Button
              onClick={() => handleEdit()}
              className="text-right bg-[#F7F8FA] rounded-md text-primary px-3"
              type="button"
              title="Edit"
            >
              <EditIcon className="text-sm mr-2" />
              Edit Profile
            </Button>
          </div>
          <div className="grid grid-cols-2 gap-x-0 gap-y-2 w-1/6">
            <div className="w-24">
              <p className="font-[500] text-xs p-0 m-0 first-letter:uppercase">
                <span className="font-[400]">Email:</span>
              </p>
            </div>
            <div className="w-48">
              <p className="font-[500] text-xs p-0 m-0 first-letter:uppercase">
                {navigator?.email}
              </p>
            </div>
            <div className="w-24">
              <p className="font-[500] text-xs p-0 m-0 first-letter:uppercase">
                <span className="font-[400]">Phone:</span>
              </p>
            </div>
            <div className="w-48">
              <p className="font-[500] text-xs p-0 m-0 first-letter:uppercase">
                {mainPhone.number}
              </p>
            </div>
          </div>
        </div>
      </div>

      {/* Access Section - Only visible to admins */}
      {isCurrentUserAdmin() && (
        <div className="mt-10 pl-8 pr-10">
          <div className="flex justify-between items-center py-4">
            <p className="m-0 p-0 font-[600]">
              Access
              {' '}
              {accessPermissions?.length > 0 && (
                <span>{`(${accessPermissions.length})`}</span>
              )}
            </p>
          </div>
          <TableContainer component={Paper} className="border border-gray-200 rounded-lg">
            <Table size="small">
              <TableHead>
                <TableRow className="bg-gray-50">
                  <TableCell className="font-semibold text-gray-700">Module</TableCell>
                  <TableCell className="font-semibold text-gray-700">Description</TableCell>
                  <TableCell align="center" className="font-semibold text-gray-700">Access</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {availableModules.map((module) => (
                  <TableRow key={module} hover>
                    <TableCell className="font-medium">
                      {capitalizeWords(module)}
                    </TableCell>
                    <TableCell className="text-gray-600 text-sm">
                      {getModuleDescription(module)}
                    </TableCell>
                    <TableCell align="center">
                      <Switch
                        checked={accessPermissions.includes(module)}
                        onChange={() => handleAccessToggle(module)}
                        disabled={isUpdatingAccess}
                        size="small"
                        color="primary"
                      />
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          {isUpdatingAccess && (
            <p className="text-sm text-gray-500 mt-2">Updating access permissions...</p>
          )}
        </div>
      )}

      <div className="mt-10 pl-8 pr-10">
        <div className="flex justify-between items-center py-4">
          <p className="m-0 p-0 font-[600]">
            Teams
            {' '}
            {navigator?.teams?.length > 0 && (
              <span>{`(${navigator?.teams?.length})`}</span>
            )}
          </p>
          {/* {navigator?.teams?.length > 0 && (
            <div className="grid grid-flow-col gap-1 auto-cols-max text-xs items-center justify-self-end">
              <span
                onClick={() => {
                  setOpenAddTeam(true);
                }}
                className="text-primary font-[500] cursor-pointer text-[15px] hover:text-[#ff8c20]"
              >
                <span className="text-[18px]">+ </span>
                Assign to Team
              </span>
            </div>
          )} */}
        </div>
        {navigator?.teams?.length > 0 ? (
          <CustomTable
            source={teamData as Teams[]}
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            variant="navigator-team-table"
            style={{
              border: 'solid 1px lightgray',
              borderRadius: '10px',
              borderBottom: 'none',
            }}
            headCellStyle={{
              padding: '4px 16px',
              fontSize: '13px',
              color: '#747A7A',
            }}
            tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
          />
        ) : (
          <p className="m-0">
            This navigator is not assigned to a team.
            {' '}
            <span
              onClick={() => handleEdit()}
              className="text-primary font-[500] cursor-pointer text-[15px] hover:text-[#ff8c20]"
            >
              Edit Profile to assign.
            </span>
          </p>
        )}
      </div>
      <div className="mt-10 pl-8 pr-10">
        <p className="m-0 p-0 font-[600] pb-3 pt-3">
          Tasks (
          {pickedUp.length + pending.length}
          )
        </p>
        <div
          className="grid grid-flow-col gap-0 pt-1 pb-2"
          style={{
            justifyContent: 'start', // Ensures items stay aligned to the start
            width: 'fit-content', // Ensures the grid only takes as much space as its content
          }}
        >
          {tabs.map((tab, index) => {
            const isActive = activeTab === tab.value;

            return (
              // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
              <div
                key={tab.value}
                onClick={() => setActiveTab(tab.value)}
                style={{
                  background: `linear-gradient(180deg, ${
                    isActive ? '#404848' : '#F7F8F8'
                  } 0%, ${isActive ? '#868B8B' : '#DCDFDF'} 100%)`,
                }}
                className={`cursor-pointer w-[144px] h-[38px] grid content-center 
                  ${index === 0 ? 'rounded-l-lg' : ''} 
                  ${index === tabs.length - 1 ? 'rounded-r-lg' : ''} 
                  ${
                    index !== 0 && index !== tabs.length - 1
                      ? 'border-l-0 border-r-0'
                      : ''
                  }`}
              >
                <p
                  className={`text-center font-[500] text-sm ${
                    isActive ? 'text-white' : 'text-[#262D2D]'
                  }`}
                >
                  {`${tab.label} (${tasks[tab.value]?.length ?? 0})`}
                </p>
              </div>
            );
          })}
        </div>

        <div className="grid grid-flow-row gap-3 placeholder-teal-100">
          {tasks[activeTab]?.map((task: any) => (
            <TaskRow
              task={task}
              rowClickUrl={`/navigators/${navigator.id}/task/${task.id}`}
              navigator={navigator}
            />
          ))}
        </div>
      </div>
    </>
  );
}

export default NavigatorDetail;
