/* eslint-disable max-len */
import { Avatar } from '@mui/material';
import { User } from 'contexts/auth-context/type';
import { useEffect, useState } from 'react';
import { getLatestImage } from 'utils/helper';

interface ProfileInfoNavigatorProps {
    member: User;
}

function ProfileInfoNavigator({ member }: ProfileInfoNavigatorProps) {
  const [picture, setPicture] = useState<string | null>('');

  useEffect(() => {
    if (member?.attachments) {
      setPicture(getLatestImage(member?.attachments, 'userProfile'));
    }
  }, [member]);

  return (
    <div
      className="grid grid-flow-col auto-cols-max gap-36 items-start borderLine"
    >
      <div>
        <Avatar
          alt={member?.firstName}
          className="w-48 h-48 m-0 p-0 cursor"
          src={picture as string}
        />
      </div>
      <div className="col-span-2 grid grid-flow-row gap-1 auto-cols-max">
        <div className="col-span-1 grid grid-flow-col gap-1 auto-cols-max">
          <p className="font-[500] text-3xl first-letter:uppercase">{member?.firstName}</p>
          <p className="font-[500] text-3xl first-letter:uppercase">{member?.lastName}</p>
        </div>
        <div
          className="grid grid-cols-2 auto-cols-auto gap-10"
        >
          <div className="grid grid-flow-row gap-1 auto-cols-max">
            <p className="font-[500] text-xs p-0 m-0  first-letter:uppercase">
              <span className="font-[400]">
                Email:
              </span>
              {' '}
              {member?.email}
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}

export default ProfileInfoNavigator;
