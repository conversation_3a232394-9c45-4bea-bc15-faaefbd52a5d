/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
import React, { useState } from 'react';

interface SeeMoreProps {
    text: string;
    maxLength?: number;
    nested?: boolean;
    children?: React.ReactNode;
    }
function SeeMore({ text, maxLength = 100, children, nested = false }: SeeMoreProps) {
  const [isExpanded, setIsExpanded] = useState(false);

  const toggleExpansion = () => {
    setIsExpanded(!isExpanded);
  };
  const RestComponent = children;
  const displayText = isExpanded ? text : `${text.length > maxLength ? `${text.substring(0, maxLength)}...` : text}`;
  return (
    <div>
      <p className="m-0 font-normal">{displayText}</p>
      { text.length >= maxLength && (
        <p
        onClick={toggleExpansion}
        className="text-blue-500 hover:underline cursor-pointer"
        >
        {isExpanded ? 'See Less' : 'See More'}
      </p>
      )}
      <p className={`m-0 ${nested && 'pl-3 pt-3'}`}>
      {RestComponent}
      </p>
    </div>
  );
}

export default SeeMore;
