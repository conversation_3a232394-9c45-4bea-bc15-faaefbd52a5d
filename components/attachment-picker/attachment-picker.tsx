import React, { useEffect, useState } from 'react';
import { Button } from '@mui/material';
import Image from 'next/image';

interface BannerPickerProps {
  setPicture: React.Dispatch<React.SetStateAction<any[]>>;
  setAttachmentsToRemove: React.Dispatch<React.SetStateAction<any[]>>;
  attachments: any[];
  attachmentsToRemove: any[];
  multiple: boolean;
  edit?: boolean;
}

function AttachmentPicker({
  attachments,
  attachmentsToRemove,
  multiple,
  setPicture,
  setAttachmentsToRemove,
  edit = false,
}: BannerPickerProps) {
  const [banners, setBanners] = useState<any[]>([]);
  const [buttonText, setButtonText] = useState('Add Attachment(s)');

  useEffect(() => {
    if (attachments?.length) {
      const formattedAttachments = attachments.map(
        (att) => (att.url ? att : { url: att }), // Ensure format consistency
      );
      setBanners(formattedAttachments);
      setButtonText('Add Attachment(s)');
    }
  }, [attachments]);

  const handleFileChange = (event: any) => {
    if (event.target.files) {
      const filesArray = Array.from(event.target.files);
      const filesBanners = filesArray.map(
        (file) => new Promise<{ url: string }>((resolve) => {
          const reader = new FileReader();
          reader.readAsDataURL(file as Blob);
          reader.onload = (e: any) => resolve({ url: e.target.result });
        }),
      );

      Promise.all(filesBanners).then((images) => {
        setBanners((prev) => [...prev, ...images]);
        setPicture((prev) => [...prev, ...images]);
        setButtonText('Add Attachment(s)');
      });

      event.target.value = ''; // Reset input
    }
  };

  const removeImage = (index: number) => {
    setBanners((prevBanners) => {
      const updatedBanners = [...prevBanners];
      const [removed] = updatedBanners.splice(index, 1);

      if (removed.id) {
        setAttachmentsToRemove((prev) => [...prev, removed]);
      }

      setPicture(updatedBanners);
      return updatedBanners;
    });
  };

  return (
    <div>
      {edit && (
        <Button variant="text" sx={{ color: '#E97100' }} component="label">
          {buttonText}
          <input
            type="file"
            hidden
            multiple={multiple}
            onChange={handleFileChange}
          />
        </Button>
      )}

      {banners?.length > 0 && (
        <div className="mt-3 flex gap-5">
          {banners.map((banner: any, index) => (
            <div
              key={`${banner?.id || 'new'}-${index}`}
              className="mt-1 grid grid-row-2 gap-2 justify-between items-center"
            >
              <Image
                alt={`attachment-${index}`}
                width={100}
                height={100}
                src={banner.url}
              />
              {edit && (
                <Button
                  variant="text"
                  sx={{ color: '#DE6F09' }}
                  onClick={() => removeImage(index)}
                >
                  Remove
                </Button>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}

export default AttachmentPicker;
