/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { Avatar } from '@mui/material';
import { User } from 'contexts/auth-context/type';
import { useState } from 'react';
import { getLatestImage } from 'utils/helper';

interface Props {
    item: User;
    // eslint-disable-next-line no-unused-vars
    handleClick: (id: string, key:string) => void;
    // eslint-disable-next-line no-unused-vars

}
function UserAvatarWithOverlay({
  item, handleClick,
}: Props) {
  const [showOverlay, setShowOverlay] = useState(false);
  const overlayStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    borderRadius: '50%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Adjust color and opacity as needed
    display: showOverlay ? 'flex' : 'none',
    justifyContent: 'center',
    alignItems: 'center',
  };
  return (
    <div
      className="relative"
      onMouseEnter={() => setShowOverlay(true)}
      onMouseLeave={() => setShowOverlay(false)}
    >
      <Avatar
        className="w-48 h-48 m-0 p-0 cursor-pointer"
        alt={item.firstName}
        src={getLatestImage(item.attachments, 'userProfile')}
      />
      <div className="relative" style={{ ...overlayStyle as any }}>
        <div className="grid grid-flow-row auto-cols-max gap-2">
          <div
            onClick={() => handleClick(item.id, 'remove')}
            className="w-[100px] cursor-pointer grid justify-center h-7  text-[9px] text-center items-center bg-white rounded-md"
          >
            <p className="cursor-pointer m-0 p-0 text-[#008390] font-[500]">Remove</p>
          </div>
          <div onClick={() => handleClick(item.id, 'new')} className="w-[100px] cursor-pointer grid justify-center h-7  text-[9px] text-center items-center bg-primary rounded-md">
            <p className="cursor-pointer m-0 p-0 text-white font-[500]">Choose New</p>
          </div>
        </div>
      </div>
    </div>
  );
}
export default UserAvatarWithOverlay;
