import type React from 'react';
import Modal from '@mui/material/Modal';
import { Close } from '@mui/icons-material';
import CheckIcon from '@mui/icons-material/Check';
import { Button, TextField } from '@mui/material';
import SelectBox from 'components/select-box/select-box';
import { FormikProps } from 'formik';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useContext } from 'react';
import { capitalizeWords } from 'utils/helper';

interface AddressFormModalProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  formik: FormikProps<any>;
  setAddressString: React.Dispatch<React.SetStateAction<string>>;
}

function AddressFormModal({
  open, setOpen, formik, setAddressString,
}: AddressFormModalProps) {
  const constant = useContext(ConstantsContext);
  return (
    <Modal open={open} onClose={() => setOpen(false)}>
      <div className="bg-white my-[5%] mx-[10%] xl:mx-[20%] relative  min-h-[80%] mb-10 rounded-lg overflow-hidden min-w-[480px]">
        <div
          style={{
            borderBottom: '1px solid #E0E0E0',
          }}
          className="h-[54px] items-center relative justify-center grid grid-flow-col  border border-black "
        >
          <p>Add Address</p>
          <Close
            className="absolute right-5 cursor-pointer"
            onClick={() => setOpen(false)}
          />
        </div>
        <div className="p-5 pb-3 h-full flex-col px-[40px]">
          <div className="flex flex-wrap gap-[17px] mb-[17px]">
            <TextField
              value={formik.values.street1}
              name="street1"
              onChange={formik.handleChange}
              placeholder="Enter Street 1"
              id="filled-basic"
              label="Street 1"
              variant="filled"
              className="min-w-[400px]"
            />
            <TextField
              value={formik.values.street2}
              name="street2"
              onChange={formik.handleChange}
              placeholder="Enter Street 2 or apt #"
              id="filled-basic"
              label="Street 2"
              variant="filled"
              className="min-w-[400px]"
            />
          </div>
          <div className="flex flex-wrap gap-[17px] mb-[17px]">
            <TextField
              value={formik.values.city}
              name="city"
              onChange={formik.handleChange}
              placeholder="Enter City"
              id="filled-basic"
              label="City"
              variant="filled"
              className="min-w-[400px]"
            />
            <div className="min-w-[400px]">
              <SelectBox
                keyVal="state"
                defaultValue={formik.values.state}
                onChange={formik.handleChange}
                items={constant?.states}
                label="State"
              />
            </div>
          </div>
          <div className="flex flex-wrap gap-[17px] mb-[17px]">
            <TextField
              value={formik.values.zip}
              name="zip"
              onChange={formik.handleChange}
              placeholder="Enter Zip Code"
              id="filled-basic"
              label="Zip Code"
              variant="filled"
              className="min-w-[400px]"
            />
          </div>
        </div>
        <div
          id="footer-area"
          style={{
            borderTop: '1px solid #E0E0E0',
            position: 'absolute',
            width: '100%',
            zIndex: 2,
            bottom: 0,
          }}
          className="h-[60px] bg-white  justify-center grid grid-flow-col  border border-black "
        >
          <Button
            onClick={() => {
              setOpen(false);
              setAddressString(
                capitalizeWords(
                  `${formik.values.street1.trim()}${formik.values.street2 ? ` ${formik.values.street2.trim()}` : ''}, ${formik.values.city.trim()}, ${formik.values.state.trim()} ${formik.values.zip.trim()}`,
                  true,
                ),
              );
            }}
            variant="contained"
            className="bg-[#2D62ED] top-2 text-white absolute right-3 rounded-md !min-w-[100px]"
          >
            <div className="grid grid-flow-col items-center gap-2">
              <CheckIcon fontSize="small" />
              Save
            </div>
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default AddressFormModal;
