import { TextField } from '@mui/material';
import SelectBox from 'components/select-box/select-box';
import TabsView from 'components/tabs-view';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useContext } from 'react';

function AddressCollect({ formik }: {formik: any}) {
  const constants = useContext(ConstantsContext);
  const handleLocationSelection = (value: string) => {
    formik.setFieldValue('remote', value !== 'location');
  };
  return (
    <div>
      <p className=" text-sm font-semibold">Location</p>
      <TabsView
        tabs={[{
          key: 'remote',
          title: 'Remote',
        }, {
          key: 'location',
          title: 'Location Based',
        }]}
        className="grid grid-flow-col auto-cols-max gap-0"
        value={formik.values.remote ? 'remote' : 'location'}
        callback={handleLocationSelection}
      />
      <div className="grid grid-flow-row auto-cols-max gap-0 pt-3 items-center">
        {/* <div className="col-span-3 grid grid-flow-col auto-cols-max items-center">
        <Checkbox size="small" className="m-0" checked={false} name="" />
        <p className="m-0 text-xs font-semibold">Use members home address</p>
      </div> */}
        { !formik.values.remote ? (
          <div className="grid grid-cols-3 w-full gap-3">
            <TextField
              name="location.street"
              placeholder="Street"
              value={formik.values.location.street}
              onChange={formik.handleChange}
              id="filled-basic"
              className="min-w-[400px]"
              label="Address"
              variant="filled"
            />
            <TextField
              name="location.street2"
              placeholder="Street 2"
              id="filled-basic"
              value={formik.values.location.street2}
              onChange={formik.handleChange}
              className="min-w-[400px]"
              label="Street 2"
              variant="filled"
            />
            <TextField
              name="location.city"
              placeholder="City"
              id="filled-basic"
              value={formik.values.location.city}
              onChange={formik.handleChange}
              className="min-w-[400px]"
              label="City"
              variant="filled"
            />
            <SelectBox
              label="States"
              onChange={formik.handleChange}
              defaultValue={formik.values.location.state}
              keyVal="location.state"
              minWidth="100%"
              items={constants?.states}
            />
            <TextField
              name="location.zip"
              placeholder="Zipcode"
              id="filled-basic"
              value={formik.values.location.zip}
              onChange={formik.handleChange}
              className="min-w-[400px]"
              label="Zipcode"
              variant="filled"
            />
          </div>
        )
          : (
            <TextField
              fullWidth
              label="Meet Link"
              variant="outlined"
              placeholder="https://..."
              className="col-span-3 min-w-[400px]"
              value={formik.values.tmp.remoteLink}
              onChange={formik.handleChange}
              name="tmp.remoteLink"
            />
          )}
      </div>
    </div>

  );
}

export default AddressCollect;
