import React, { useState } from 'react';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker, DatePickerProps } from '@mui/x-date-pickers/DatePicker';
import { styled } from '@mui/material';
import dayjs from 'dayjs';
import {
  DateTimePicker,
  DateTimePickerProps,
} from '@mui/x-date-pickers/DateTimePicker';

const StyledDatePicker = styled(DatePicker)({
  backgroundColor: 'white',
  borderRadius: '8px',
  border: '1px solid #E0E0E0',
  height: '56px',
  '& .MuiInputBase-root': {
    height: '100%', // Ensures the input takes full height of the parent
    alignItems: 'center', // Aligns the text and placeholder vertically
  },
  '& .MuiInputLabel-root': {
    transform: 'translate(14px, 16px) scale(1)', // Adjusts label position when not shrunk
  },
  '& .MuiInputLabel-shrink': {
    transform: 'translate(14px, 5px) scale(0.75)', // Adjusts label position when shrunk
  },
  '& .MuiInputBase-input': {
    marginTop: '18px', // Centers the text vertically
    marginBottom: '8px',
  },
  '& .MuiOutlinedInput-notchedOutline': {
    display: 'none', // Removes the border outline
  },
});
const StyledDateTimePicker = styled(DateTimePicker)({
  backgroundColor: 'white',
  borderRadius: '8px',
  border: '1px solid #E0E0E0',
  height: '56px',
  '& .MuiInputBase-root': {
    height: '100%', // Ensures the input takes full height of the parent
    alignItems: 'center', // Aligns the text and placeholder vertically
  },
  '& .MuiInputLabel-root': {
    transform: 'translate(14px, 15px) scale(1)', // Adjusts label position when not shrunk
  },
  '& .MuiInputLabel-shrink': {
    transform: 'translate(14px, 3px) scale(0.75)', // Adjusts label position when shrunk
  },
  '& .MuiInputBase-input': {
    marginTop: '8px', // Centers the text vertically
  },
  '& .MuiOutlinedInput-notchedOutline': {
    display: 'none', // Removes the border outline
  },
});

interface DateSelectorProps {
  // eslint-disable-next-line no-unused-vars
  onChange?: (e: any, x?: any) => void;
  name?: string;
  time?: boolean;
  range?: boolean;
  defaultValue?: string;
  pickerProps?: Partial<DatePickerProps<any>>;
  dateTimePickerProps?: Partial<DateTimePickerProps<any>>;
}
function DateSelector({
  onChange,
  defaultValue,
  name = 'dob',
  pickerProps,
  time = false,
  range = false,
  dateTimePickerProps,
}: DateSelectorProps) {
  const [minDate, setMinDate] = useState(null);
  const [firstDate, setFirstDate] = useState('2023-01-01');
  // eslint-disable-next-line no-unused-vars
  const [secondDate, setSecondDate] = useState(dayjs().format('YYYY-MM-DD'));

  const resetDates = () => {
    setMinDate(null);
    setFirstDate('2023-01-01');
    setSecondDate(new Date().toISOString().slice(0, 10));
    if (onChange) {
      onChange(new Date().toISOString().slice(0, 10), '2023-01-01');
    }
  };

  const handleRangeChange = (date: any, second: boolean) => {
    if (!second) {
      setMinDate(date);
      setSecondDate(new Date().toISOString().slice(0, 10));
      setFirstDate(date);
      return;
    }
    const formattedDate = dayjs(date).format('YYYY-MM-DD');

    if (formattedDate === 'Invalid Date') {
      return;
    }
    if (onChange) {
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      (onChange as any)(formattedDate, dayjs(firstDate).format('YYYY-MM-DD'));
    }
  };
  const handleDateChange = (date: any) => {
    if (range) {
      if (!date) {
        resetDates();
        return;
      }
      setMinDate(date);
    }
    if (date) {
      const formattedDate = time
        ? dayjs(date).format('YYYY-MM-DD hh:mm a')
        : dayjs(date).format('YYYY-MM-DD');
      if (onChange) {
        onChange({
          target: {
            name,
            value: formattedDate,
          } as any,
        } as any);
      }
    } else if (onChange) {
      onChange({
        target: {
          name,
          value: null,
        } as any,
      } as any);
    }
  };
  if (range) {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <div className="grid grid-flow-col gap-2">
          <StyledDatePicker
            className="h-10"
            value={dayjs(firstDate)}
            onChange={(date) => {
              handleRangeChange(date, false);
            }}
            slotProps={{
              field: {
                clearable: true,
                onClear: () => handleDateChange(null),
              },
            }}
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...pickerProps}
          />
          <p className="m-0 self-center"> - </p>
          <StyledDatePicker
            value={dayjs(secondDate)}
            className="h-10"
            onAccept={(date) => {
              handleRangeChange(date, true);
            }}
            minDate={minDate}
            slotProps={{
              field: {
                clearable: true,
                onClear: () => handleDateChange(null),
              },
            }}
            // eslint-disable-next-line react/jsx-props-no-spreading
            {...pickerProps}
          />
        </div>
      </LocalizationProvider>
    );
  }
  if (time) {
    return (
      <LocalizationProvider dateAdapter={AdapterDayjs}>
        <StyledDateTimePicker
          value={defaultValue ? dayjs(new Date(defaultValue)) : null}
          onAccept={handleDateChange}
          slotProps={{
            field: {
              clearable: true,
              onClear: () => handleDateChange(null),
            },
          }}
          // eslint-disable-next-line react/jsx-props-no-spreading
          {...dateTimePickerProps}
        />
      </LocalizationProvider>
    );
  }
  return (
    <LocalizationProvider dateAdapter={AdapterDayjs}>
      <StyledDatePicker
        className="w-full"
        value={defaultValue ? dayjs(defaultValue) : null}
        onChange={handleDateChange}
        slotProps={{
          field: {
            clearable: true,
            onClear: () => handleDateChange(null),
          },
        }}
        // eslint-disable-next-line react/jsx-props-no-spreading
        {...pickerProps}
      />
    </LocalizationProvider>
  );
}
export default DateSelector;
