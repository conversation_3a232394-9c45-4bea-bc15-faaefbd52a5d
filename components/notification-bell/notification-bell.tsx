import React, { useState, useEffect } from 'react';
import {
  <PERSON>ge,
  Icon<PERSON>utton,
  <PERSON>over,
  List,
  ListItem,
  Button,
  Typography,
  Divider,
  Pagination,
  Checkbox,
  Box,
  CircularProgress,
} from '@mui/material';
import {
  Check,
  Delete,
  CheckCircle,
} from '@mui/icons-material';
import useNotifications from 'hooks/use-notifications';
import { Notification } from 'types/notification-types';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';

dayjs.extend(relativeTime);

const NotificationBell: React.FC = () => {
  const [anchorEl, setAnchorEl] = useState<HTMLButtonElement | null>(null);
  const [selectedIds, setSelectedIds] = useState<string[]>([]);
  const [showOnlyUnread, setShowOnlyUnread] = useState(true);

  const {
    notifications,
    unreadCount,
    currentPage,
    totalPages,
    loading,
    fetchNotifications,
    fetchNotificationCount,
    markAsRead,
    markAllAsRead,
    deleteNotifications,
  } = useNotifications();

  const open = Boolean(anchorEl);

  // Fetch notification count on component mount
  useEffect(() => {
    fetchNotificationCount();
  }, [fetchNotificationCount]);

  const handleClick = async (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
    // Fetch notifications when opening
    await fetchNotifications(1, !showOnlyUnread);
  };

  const handleClose = () => {
    setAnchorEl(null);
    setSelectedIds([]);
  };

  const handlePageChange = async (_event: React.ChangeEvent<unknown>, page: number) => {
    await fetchNotifications(page, !showOnlyUnread);
  };

  const handleToggleFilter = async () => {
    const newFilter = !showOnlyUnread;
    setShowOnlyUnread(newFilter);
    await fetchNotifications(1, !newFilter);
  };

  const handleSelectNotification = (id: string) => {
    setSelectedIds(prev => 
      prev.includes(id) 
        ? prev.filter(selectedId => selectedId !== id)
        : [...prev, id]
    );
  };

  const handleSelectAll = () => {
    if (selectedIds.length === notifications.length) {
      setSelectedIds([]);
    } else {
      setSelectedIds(notifications.map(n => n.id));
    }
  };

  const handleMarkSelectedAsRead = async () => {
    if (selectedIds.length > 0) {
      await markAsRead(selectedIds);
      setSelectedIds([]);
    }
  };

  const handleDeleteSelected = async () => {
    if (selectedIds.length > 0) {
      await deleteNotifications(selectedIds);
      setSelectedIds([]);
    }
  };

  const handleMarkAllAsRead = async () => {
    await markAllAsRead();
    setSelectedIds([]);
  };

  const formatNotificationTime = (createdAt: string) => {
    return dayjs(createdAt).fromNow();
  };



  // Auto-refresh notifications every 30 seconds when popover is open
  // useEffect(() => {
  //   if (open) {
  //     intervalRef.current = setInterval(() => {
  //       fetchNotifications(currentPage, showOnlyUnread);
  //     }, 30000);
  //   } else {
  //     if (intervalRef.current) {
  //       clearInterval(intervalRef.current);
  //       intervalRef.current = null;
  //     }
  //   }

  //   return () => {
  //     if (intervalRef.current) {
  //       clearInterval(intervalRef.current);
  //     }
  //   };
  // }, [open, currentPage, showOnlyUnread, fetchNotifications]);

  return (
    <>
      <IconButton
        onClick={handleClick}
        className="cursor-pointer"
        aria-label="notifications"
        size="small"
      >
        <Badge
          badgeContent={unreadCount > 0 ? (unreadCount > 99 ? '99+' : unreadCount) : undefined}
          color="error"
          invisible={unreadCount === 0}
        >
          <div className="bg-icon-bell w-6 h-6 bg-no-repeat bg-center" />
        </Badge>
      </IconButton>

      <Popover
        open={open}
        anchorEl={anchorEl}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        slotProps={{
          paper: {
            sx: {
              width: 400,
              maxHeight: 550,
              mt: 1,
            },
          },
        }}
      >
        <Box sx={{ p: 2 }}>
          <div className="flex justify-between items-center mb-2">
            <Typography variant="h6" className="font-semibold">
              Notifications
            </Typography>
            <div className="flex gap-2">
              <Button
                size="small"
                onClick={handleToggleFilter}
                variant={showOnlyUnread ? "contained" : "outlined"}
                sx={{ fontSize: '0.75rem', minWidth: 'auto', px: 1 }}
              >
                {showOnlyUnread ? 'Unread' : 'All'}
              </Button>
            </div>
          </div>

          {loading ? (
            <Box display="flex" justifyContent="center" p={3}>
              <CircularProgress size={24} />
            </Box>
          ) : notifications.length === 0 ? (
            <Box textAlign="center" p={3}>
              <Typography variant="body2" color="textSecondary">
                {showOnlyUnread ? 'No unread notifications' : 'No notifications'}
              </Typography>
            </Box>
          ) : (
            <>
              {/* Bulk Actions */}
              <div className="flex justify-between items-center mb-2 p-2 bg-gray-50 rounded">
                <div className="flex items-center gap-2">
                  <Checkbox
                    size="small"
                    checked={selectedIds.length === notifications.length}
                    indeterminate={selectedIds.length > 0 && selectedIds.length < notifications.length}
                    onChange={handleSelectAll}
                  />
                  <Typography variant="caption">
                    {selectedIds.length > 0 ? `${selectedIds.length} selected` : 'Select all'}
                  </Typography>
                </div>
                
                {selectedIds.length > 0 && (
                  <div className="flex gap-1">
                    <IconButton size="small" onClick={handleMarkSelectedAsRead} title="Mark as read">
                      <Check fontSize="small" />
                    </IconButton>
                    <IconButton size="small" onClick={handleDeleteSelected} title="Delete">
                      <Delete fontSize="small" />
                    </IconButton>
                  </div>
                )}
                
                {unreadCount > 0 && (
                  <IconButton size="small" onClick={handleMarkAllAsRead} title="Mark all as read">
                    <CheckCircle fontSize="small" />
                  </IconButton>
                )}
              </div>

              <Divider />

              {/* Notifications List */}
              <List sx={{ maxHeight: 300, overflow: 'auto', p: 0 }}>
                {notifications.map((notification: Notification) => (
                  <ListItem
                    key={notification.id}
                    sx={{
                      backgroundColor: notification.read ? 'transparent' : '#FFF7EC',
                      '&:hover': { backgroundColor: '#f9fafb' },
                      py: 1.5,
                    }}
                  >
                    <div className="flex items-center gap-2">
                      {!notification.read && (
                        <div
                          className="w-2 h-2 rounded-full"
                          style={{ backgroundColor: '#FD8205' }}
                        />
                      )}
                      <Checkbox
                        size="small"
                        checked={selectedIds.includes(notification.id)}
                        onChange={() => handleSelectNotification(notification.id)}
                      />
                    </div>
                    
                    <div className="flex items-start gap-2 flex-1">                      
                      <div className="flex-1 min-w-0">
                        <Typography 
                          variant="subtitle2" 
                          className={`font-medium ${!notification.read ? 'font-semibold' : ''}`}
                          sx={{ mb: 0.5 }}
                        >
                          {notification.title}
                        </Typography>
                        <Typography 
                          variant="body2" 
                          color="textSecondary"
                          sx={{ 
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                            mb: 0.5,
                          }}
                        >
                          {notification.message}
                        </Typography>
                        <Typography variant="caption" color="textSecondary">
                          {formatNotificationTime(notification.createdAt)}
                        </Typography>
                      </div>
                    </div>
                  </ListItem>
                ))}
              </List>

              {/* Pagination */}
              {totalPages > 1 && (
                <>
                  <Divider sx={{ mt: 1 }} />
                  <Box display="flex" justifyContent="center" p={1}>
                    <Pagination
                      count={totalPages}
                      page={currentPage}
                      onChange={handlePageChange}
                      size="small"
                      color="primary"
                    />
                  </Box>
                </>
              )}
            </>
          )}
        </Box>
      </Popover>
    </>
  );
};

export default NotificationBell;
