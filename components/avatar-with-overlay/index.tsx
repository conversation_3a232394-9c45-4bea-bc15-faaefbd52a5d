/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { Avatar } from '@mui/material';
import API from 'api/src/lib/api';
import { useLoading } from 'contexts/loading-context/loading-context';
import { useState } from 'react';
import { Member } from 'types/members-response-type';
import { getLatestImage } from 'utils/helper';

interface Props {
    item: Member;
    // eslint-disable-next-line no-unused-vars
    handleClick: (id: string) => void;
    // eslint-disable-next-line no-unused-vars
    getScoreTitle: (score: number) => any;
    headId?: string;
    householdId: string;
    className?: string;
}
function AvatarWithOverlay({
  item, headId, handleClick, getScoreTitle, householdId, className,
}: Props) {
  const [showOverlay, setShowOverlay] = useState(false);
  const { showLoading } = useLoading();
  const handleHeadOfHousehold = async () => {
    showLoading();
    await API.HOUSEHOLDS.setHeadOfHousehold({
      orgID: item.org.id,
      headOfHouseID: item.id,
    }, householdId);
    window.location.reload();
  };
  const overlayStyle = {
    position: 'absolute',
    top: 0,
    left: 0,
    width: '100%',
    borderRadius: '50%',
    height: '100%',
    backgroundColor: 'rgba(0, 0, 0, 0.5)', // Adjust color and opacity as needed
    display: showOverlay ? 'flex' : 'none',
    justifyContent: 'center',
    alignItems: 'center',
  };
  return (
    <div
      className="relative"
      onMouseEnter={() => setShowOverlay(true)}
      onMouseLeave={() => setShowOverlay(false)}
    >
      {headId === item.id && <div className="absolute z-10 bg-[length:25px_25px] right-0 bg-icon-hoh bg-blea w-9 h-9 bg-no-repeat" />}
      <Avatar
        style={{
          border: `4px solid ${getScoreTitle(parseInt(item.score, 10))?.color}`,
        }}
        className={`${className} w-32 h-32 m-0 p-0 cursor-pointer`}
        alt={item.firstName}
        src={getLatestImage(item.attachments, 'profile')}
      />
      <div className="relative" style={{ ...overlayStyle as any }}>
        <div className="grid grid-flow-row auto-cols-max gap-2">
          <div
            onClick={() => handleClick(item.id)}
            className="w-[100px] cursor-pointer grid justify-center h-7  text-[9px] text-center items-center bg-white rounded-md"
          >
            <p className="cursor-pointer m-0 p-0 text-[#008390] font-[500]">View Profile</p>
          </div>
          { headId !== item.id && (
          <div onClick={handleHeadOfHousehold} className="w-[100px] cursor-pointer grid justify-center h-7  text-[9px] text-center items-center bg-primary rounded-md">
            <p className="cursor-pointer m-0 p-0 text-white font-[500]">Head of Household</p>
          </div>
          )}

        </div>
      </div>
    </div>
  );
}
export default AvatarWithOverlay;
