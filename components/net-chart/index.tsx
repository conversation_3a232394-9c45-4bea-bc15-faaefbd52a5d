import React, { useEffect, useState } from 'react';
import {
  Chart as ChartJS,
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Too<PERSON>ip,
  Legend,
} from 'chart.js';
import { Radar } from 'react-chartjs-2';
import { ISurveyItems } from 'types/members-response-type';
import useScore from 'hooks/use-score';
import { hexToRGB, isSafari } from 'utils/helper';

ChartJS.register(
  RadialLinearScale,
  PointElement,
  LineElement,
  Filler,
  Tooltip,
  Legend,
);

interface NetChartProps {
    labels: ISurveyItems[];
    memberScore: number;
}

function NetChart({ labels, memberScore = 0 }: NetChartProps) {
  const [_labels, setLabels] = useState<string[]>([]);
  const [_scores, setScores] = useState<number[]>([]);
  const { getScoreTitle, getSurveyScore } = useScore();

  useEffect(() => {
    if (labels.length > 0) {
      setLabels(labels.map((x) => x.title));
      setScores(labels.map((x) => x.score));
    }
    return () => {
      setLabels([]);
      setScores([]);
    };
  }, [labels]);

  return (

    <Radar
      options={{
        responsive: !isSafari(),
        scales: {
          r: {
            angleLines: {
              color: '#D5DCE2',
              lineWidth: 2,
              // display: false,
            },
            suggestedMin: -1,
            ticks: {
              stepSize: 1,
              maxTicksLimit: 4,
              display: false,
            },
            grid: {
              color: '#D5DCE2',
              lineWidth: 2,
            },
          },
        },
        plugins: {
          legend: {
            display: false,
          },
        },
      }}
      data={{
        labels: _labels,
        datasets: [
          {
            data: _scores,
            pointBorderColor: 'white',
            pointRadius: 4,
            pointStyle: 'circle',
            pointBackgroundColor: (x) => getSurveyScore(x?.raw as number)?.color,
            pointBorderWidth: 2,
            backgroundColor: hexToRGB(getScoreTitle(memberScore)?.color as string, 0.30),
            borderColor: getScoreTitle(memberScore)?.color as string,
            borderWidth: 2.1,
          },
        ],
      }}
    />

  );
}

export default NetChart;
