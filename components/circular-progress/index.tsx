/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { useFullReportData } from 'components/reporting-sections/context';
import React from 'react';
import { CircularProgressbarWithChildren, buildStyles } from 'react-circular-progressbar';
import 'react-circular-progressbar/dist/styles.css';
import { capitalizeWords } from 'utils/helper';
import { Segment } from 'utils/type';

interface CirclularProgressProps {
  data: Segment[];
  fullData: any;
  title: string;
  label: string;
  length?: number;
}

// Helper function to get percentage
const getPercentage = (value: number, total: number): number => (value / total) * 100;

// Helper function to convert percentage to radians
const percentageToRadians = (percentage: number): number => (percentage / 100) * 2 * Math.PI;

// Helper function to describe an arc
const describeArc = (
  x: number,
  y: number,
  radius: number,
  startAngle: number,
  endAngle: number,
): string => {
  const start = {
    x: x + radius * Math.cos(startAngle),
    y: y + radius * Math.sin(startAngle),
  };
  const end = {
    x: x + radius * Math.cos(endAngle),
    y: y + radius * Math.sin(endAngle),
  };
  const largeArcFlag = endAngle - startAngle <= Math.PI ? '0' : '1';

  return [
    'M', start.x, start.y,
    'A', radius, radius, 0, largeArcFlag, 1, end.x, end.y,
  ].join(' ');
};

function CircularProgress({
  data, label = 'Label', title = 'Here', length = 0, fullData,
}: CirclularProgressProps) {
  const total = data.reduce((acc, current) => acc + current.value, 0);
  const { showModal } = useFullReportData();
  const handleViewMore = () => {
    showModal(title, fullData);
  };
  return (
    <div className="w-full mb-16 min-h-[310px] grid gap-6">
      <p className="text-dark-text first-letter:uppercase text-sm font-[500]">{label}</p>
      {length === 0 ? (
        <p className="grid justify-center self-center italic text-sm text-dark-text p-14">
          {' '}
          No data available for
          {' '}
          {label}
        </p>
      ) : (
        <div
          className="grid grid-flow-col auto-cols-max gap-8 items-center"
        >
          <div
            style={{ width: 160, height: 160, position: 'relative' }}
          >
            <CircularProgressbarWithChildren
              value={100}
              styles={buildStyles({
                rotation: 0,
                strokeLinecap: 'butt',
                textSize: '16px',
                pathTransitionDuration: 0.5,
                pathColor: '#d6d6d6',
                textColor: '#000000',
                trailColor: '#ffffff',
              })}
            >
              <svg width="160" height="160" viewBox="0 0 100 100" className="absolute top-0 left-0">
                {data.reduce<Segment[]>((acc:any, segment, index) => {
                  const previousValue = index === 0 ? 0 : acc[index - 1].endValue;
                  const startValue = previousValue;
                  const endValue = startValue + getPercentage(segment.value, total);
                  const startAngle = percentageToRadians(startValue) - Math.PI / 2;
                  const endAngle = percentageToRadians(endValue) - Math.PI / 2;
                  acc.push({
                    ...segment, startValue, endValue, startAngle, endAngle,
                  });
                  return acc;
                }, []).map((segment:any) => (
                  <path
                    key={segment.name}
                    d={describeArc(50, 50, 45, segment.startAngle, segment.endAngle)}
                    fill="none"
                    stroke={segment.color}
                    strokeWidth="10"
                  />
                ))}
              </svg>
              <div className="absolute inset-0 flex flex-col justify-center items-center">
                <span className="text-[#747A7A] font-normal text-xs">{title}</span>
                <span className="text-xl font-semibold">{length}</span>
              </div>
            </CircularProgressbarWithChildren>
          </div>
          <div className=" w-full grid auto-cols-auto">
            {data.map((segment) => {
              const percentage = ((segment.value / total) * 100).toFixed(1);
              return (
                <div key={segment.name} className="grid grid-flow-col w-full auto-cols-auto gap-x-40 justify-between items-center mb-2">
                  <div className="flex items-center">
                    <span
                      className="inline-block w-3 h-3 rounded-xs mr-2"
                      style={{ backgroundColor: segment.color }}
                    />
                    <span className="text-sm font-[500] first-letter:uppercase">
                      { capitalizeWords((segment.name.length > 20 ? `${segment.name.substring(0, 25)}...` : segment.name).replace('_', ' '), true)}
                    </span>

                  </div>
                  <span className="ml-2 text-sm">{`${segment.value} - ${percentage}%`}</span>
                </div>
              );
            })}
          </div>
        </div>
      )}
      <div
        onClick={handleViewMore}
        className="grid  cursor-pointer  grid-flow-col auto-cols-auto justify-center rounded-md bg-[#F7F8F8] p-3 mt-2"
      >
        <p className="m-0 text-[#008390] font-[500] text-sm">View Full Details</p>
      </div>
    </div>
  );
}

export default CircularProgress;
