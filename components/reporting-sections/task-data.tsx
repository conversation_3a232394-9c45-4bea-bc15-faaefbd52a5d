import CircularProgress from 'components/circular-progress';
import { TaskData as TaskDataType } from 'types/reporting-types';
import { fromObjectToSegments } from 'utils/helper';

interface MemberDemographicsProps {
    reports: TaskDataType;
}

function TaskData({ reports }: MemberDemographicsProps) {
  return (
    <div className="grid grid-cols-3 p-2 auto-cols-max">
      <p className="font-semibold text-dark-text col-span-3">Plans</p>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Task Created</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalTasks}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Task Completed</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalTasksCompleted}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Tasks Pending</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalTasksPending}</p>
      </div>
      <div className="col-span-3" />
      <div className="col-span-3 innerSpace grid grid-cols-2 gap-3 auto-cols-auto w-full items-start">
        <CircularProgress
          title="Types"
          label="Task By Networks"
          fullData={reports.tasksByType}
          length={Object.keys(reports.tasksByType).length}
          data={fromObjectToSegments(reports.tasksByType)}
        />
        <CircularProgress
          title="Navigators"
          label="Task By Navigator"
          fullData={reports.tasksByNavigators}
          length={Object.keys(reports.tasksByNavigators).length}
          data={fromObjectToSegments(reports.tasksByNavigators)}
        />
      </div>
    </div>
  );
}

export default TaskData;
