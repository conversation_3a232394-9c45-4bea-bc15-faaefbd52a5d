import React, {
  createContext, useState, useContext, useMemo,
} from 'react';
import {
  Modal,
} from '@mui/material';
import { Close } from '@mui/icons-material';
import { fromObjectToSegments } from 'utils/helper';
import RewampTable from 'components/table-components/rewamp-table/table';
import FullReportColumns from 'components/table-components/rewamp-table/columns/full-reports';

  interface FullDataReportContextProps {
    // eslint-disable-next-line no-unused-vars
    showModal: (title: string, data:any, onConfirm?: () => void) => void;
    hideModal: () => void;
  }

const FullDataReportContext = createContext<FullDataReportContextProps | undefined>(undefined);

export const useFullReportData = () => {
  const context = useContext(FullDataReportContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export function FullDataReportProvider({ children }: any) {
  const [isOpen, setIsOpen] = useState(false);
  const [fullData, setFullData] = useState<any[]>([]);
  const [title, setTitle] = useState('');

  const showModal = (textTitle: string, data:any) => {
    setTitle(textTitle);
    // eslint-disable-next-line no-underscore-dangle
    const _fullData = fromObjectToSegments(data, true);
    setFullData(_fullData);
    setIsOpen(true);
  };

  const hideModal = () => setIsOpen(false);

  const value = useMemo(() => ({
    showModal,
    hideModal,
  }), []);

  return (
    <FullDataReportContext.Provider value={value}>
      <Modal open={isOpen} onClose={hideModal}>
        <div className="bg-white m-[5%] relative  min-h-[80%] mb-10 rounded-lg">
          <div
            style={{
              borderBottom: '1px solid #E0E0E0',
            }}
            className="h-[64px] items-center relative justify-center grid grid-flow-col border border-black "
          >
            <div className="grid grid-flow-row p-2 items-center">
              <p className="m-0 font-semibold text-md">{title}</p>
            </div>
            <Close className="absolute right-5 cursor-pointer" onClick={hideModal} />
          </div>
          <div className="grid w-full gap-8">
            <RewampTable
              data={fullData}
              columns={FullReportColumns(fullData.reduce((acc, curr) => acc + curr.value, 0))}
            />
          </div>
        </div>
      </Modal>
      {children}
    </FullDataReportContext.Provider>
  );
}
