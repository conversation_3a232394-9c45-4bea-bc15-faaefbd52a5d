import CircularProgress from 'components/circular-progress';
import { MemberDemographicsItems } from 'types/reporting-types';
import { fromObjectToSegments } from 'utils/helper';

interface MemberDemographicsProps {
    reports: MemberDemographicsItems;
}

const mockData = fromObjectToSegments({
  location: 7,
  appointment: 8,
  custom: 4,
  contact: 8,
  survey: 2,
  visit: 15,
  assessment: 9,
});
function MemberDemographics({ reports }: MemberDemographicsProps) {
  return (
    <div className="grid grid-cols-3 p-2 auto-cols-max">
      <p className="font-semibold text-dark-text col-span-3">Member Demographics</p>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Households</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.households}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Enrolled Members</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.enrolled}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Unenrolled Members</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalUnenrolled}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Pending Members</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.pending}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Insurance Captured</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalInsurances}</p>
      </div>
      <div className="col-span-3" />
      <div className="col-span-3 innerSpace grid grid-cols-2 gap-3 auto-cols-auto w-full items-start">
        <CircularProgress
          title="Types"
          label="Member Types"
          fullData={reports.membersByTypes}
          length={Object.keys(reports.membersByTypes).length}
          data={fromObjectToSegments(reports.membersByTypes)}
        />
        <CircularProgress
          title="Statuses"
          label="Member Statuses"
          fullData={Object.keys(reports.membersByTypes)}
          length={Object.keys(reports.membersByTypes).length}
          data={fromObjectToSegments(reports.membersBySubStatus).length === 0
            ? mockData : fromObjectToSegments(reports.membersBySubStatus)}
        />
        <CircularProgress
          title="Insurances"
          label="Insurance Carrier"
          length={Object.keys(reports.insurances).length}
          fullData={reports.insurances}
          data={fromObjectToSegments(reports.insurances).length === 0
            ? mockData : fromObjectToSegments(reports.insurances)}
        />
        <CircularProgress
          title="Gender"
          label="Genders"
          length={Object.keys(reports.genders).length}
          fullData={reports.genders}
          data={fromObjectToSegments(reports.genders).length === 0
            ? mockData : fromObjectToSegments(reports.genders)}
        />
        <CircularProgress
          title="Ethnicity"
          label="Ethnicities"
          length={Object.keys(reports.ethnicity).length}
          fullData={reports.ethnicity}
          data={fromObjectToSegments(reports.ethnicity).length === 0
            ? mockData : fromObjectToSegments(reports.ethnicity)}
        />
      </div>
    </div>
  );
}

export default MemberDemographics;
