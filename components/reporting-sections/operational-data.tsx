import { OperationalData as OperationalDataType } from 'types/reporting-types';

interface MemberDemographicsProps {
    reports: OperationalDataType;
}

function OperationalData({ reports }: MemberDemographicsProps) {
  return (
    <div className="grid grid-cols-3 p-2 auto-cols-max">
      <p className="font-semibold text-dark-text col-span-3">System</p>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Notes Created</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalNotes}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Attachments Uploaded</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalChats}</p>
      </div>
      {/* <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Chat Messages</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalChats}</p>
      </div> */}
      <div className="col-span-3" />
    </div>
  );
}

export default OperationalData;
