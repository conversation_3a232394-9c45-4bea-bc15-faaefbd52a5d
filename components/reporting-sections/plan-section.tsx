import CircularProgress from 'components/circular-progress';
import { PlansData } from 'types/reporting-types';
import { fromObjectToSegments } from 'utils/helper';

interface MemberDemographicsProps {
    reports: PlansData;
}

const mockData = fromObjectToSegments({
  location: 7,
  appointment: 8,
  custom: 4,
  contact: 8,
  survey: 2,
  visit: 15,
  assessment: 9,
});
function PlanSection({ reports }: MemberDemographicsProps) {
  return (
    <div className="grid grid-cols-3 p-2 auto-cols-max">
      <p className="font-semibold text-dark-text col-span-3">Plans</p>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Plans Created</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalPlans}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Active Plans</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalActivePlans}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Networks Listed</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalNetworks}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Services Added</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalServices}</p>
      </div>
      <div className="mb-5">
        <p className="font-normal text-sm text-dark-text m-0">Total Appointments Created</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalAppointments}</p>
      </div>
      <div className="col-span-3" />
      <div className="col-span-3 innerSpace grid grid-cols-2 gap-3 auto-cols-auto w-full items-start">
        <CircularProgress
          title="Categories"
          label="Network Categories"
          fullData={reports.networkCategories}
          length={Object.keys(reports.networkCategories).length}
          data={fromObjectToSegments(reports.networkCategories)}
        />
        <CircularProgress
          title="Networks"
          label="Top Referral Networks"
          length={Object.keys(reports.referralNetwork).length}
          fullData={reports.referralNetwork}
          data={fromObjectToSegments(reports.referralNetwork).length === 0
            ? mockData : fromObjectToSegments(reports.referralNetwork)}
        />
        <CircularProgress
          title="Statuses"
          label="Active Plan Appoitment Statuses"
          fullData={reports.appointmentBreakdown}
          length={Object.keys(reports.appointmentBreakdown).length}
          data={fromObjectToSegments(reports.appointmentBreakdown).length === 0
            ? mockData : fromObjectToSegments(reports.appointmentBreakdown)}
        />
      </div>
    </div>
  );
}

export default PlanSection;
