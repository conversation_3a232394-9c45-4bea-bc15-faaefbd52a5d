import CircularProgress from 'components/circular-progress';
import { Assessments } from 'types/reporting-types';
import { fromObjectToSegments } from 'utils/helper';

interface MemberDemographicsProps {
    reports: Assessments;
}

// const mockData = fromObjectToSegments({
//   location: 7,
//   appointment: 8,
//   custom: 4,
//   contact: 8,
//   survey: 2,
//   visit: 15,
//   assessment: 9,
// });
function AssessmentData({ reports }: MemberDemographicsProps) {
  return (
    <div className="grid grid-cols-3 p-2 auto-cols-max">
      <p className="font-semibold text-xl text-dark-text col-span-3">Assessments</p>
      <div className="mb-5">
        <p className="font-normal text-sm pb-2 text-dark-text m-0">Total Assessment Completed</p>
        <p className="font-semibold text-[32px] text-dark-text m-0">{reports.totalAssessments}</p>
      </div>
      <div className="col-span-3" />
      <div className="col-span-3 grid grid-cols-2 gap-3 auto-cols-auto w-full items-start">
        <CircularProgress
          title="Types"
          fullData={reports.assessmentsByTypes}
          label="Assessment by Type"
          length={Object.keys(reports.assessmentsByTypes).length}
          data={fromObjectToSegments(reports.assessmentsByTypes)}
        />
      </div>
    </div>
  );
}

export default AssessmentData;
