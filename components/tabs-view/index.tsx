/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import { AttachmentType } from 'contexts/constant-context/type';
import { useState } from 'react';
import { capitalizeWords } from 'utils/helper';

interface TabsViewProps {
  tabs: AttachmentType[];
  className?: string;
  value: string;
  callback: (tabValue: string) => void;
}

function TabsView({
  tabs, value, callback, className,
}: TabsViewProps) {
  const handleClick = (tabValue: string) => {
    if (tabValue !== value) callback(tabValue);
  };

  const checkActiveTab = (tabValue: string) => value === tabValue;

  if (tabs.length === 0) return <div />;

  return (
    <div className={className}>
      {tabs.map((tab, index) => (
        <div
          key={tab.key}
          onClick={() => handleClick(tab.key)}
          style={{
            background: !checkActiveTab(tab.key)
              ? 'linear-gradient(180deg, #F7F8F8 0%, #DCDFDF 100%)'
              : 'linear-gradient(180deg, #404848 0%, #868B8B 100%)',
          }}
          className={`cursor-pointer min-w-[144px] pl-3 pr-3 h-[38px] grid content-center
            ${index === 0 ? 'rounded-l-lg' : ''}
            ${index === tabs.length - 1 ? 'rounded-r-lg' : ''}
            ${
              index !== 0 && index !== tabs.length - 1
                ? 'border-l-0 border-r-0'
                : ''
            }`}
        >
          <p
            className={`text-center font-[500] text-[13px] ${
              checkActiveTab(tab.key) ? 'text-white' : 'text-[#262D2D]'
            }`}
          >
            {capitalizeWords(tab.title, true)}
          </p>
        </div>
      ))}
    </div>
  );
}

export default TabsView;
