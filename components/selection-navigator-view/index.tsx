/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import SelectNavigator from 'components/navigators/select-navigator/select-navigator';
import CustomTable from 'components/table-components/table';
import { Navigator } from 'types/navigators-types';
import React, { useState } from 'react';
import { useRouter } from 'next/router';

interface PersonSelectionViewProps {
    title: string;
    allowAdd?: boolean;
    selectedNavigators: Navigator[];
    setSelectedNavigators: React.Dispatch<React.SetStateAction<Navigator[]>>;
    className?: string;
}

function NavigatorSelectionView({
  selectedNavigators,
  setSelectedNavigators,
  allowAdd = true,
  title = 'Navigators',
  className = '',
}: PersonSelectionViewProps) {
  const [openSelectNavigators, setOpenSelectNavigators] = useState<boolean>(false);
  const router = useRouter();

  const handleSelectNavigators = () => {
    setOpenSelectNavigators((prev) => !prev);
  };

  const handleAddNavigator = () => {
    router.push('/tasks/add');
  };
  return (
    <div className={`col-span-3 ${className}`}>
      <div className="grid grid-flow-col auto-cols-auto text-xs items-center ">
        <p className="text-sm font-[500]">{title}</p>
        {selectedNavigators.length > 0
                        && (
                        <div className="grid grid-flow-col gap-1 auto-cols-max text-xs items-center justify-self-end">
                          <span
                            onClick={() => {
                              setOpenSelectNavigators(true);
                            }}
                            className="text-primary underline font-[500] cursor-pointer"
                          >
                            Select Navigator
                          </span>
                          {allowAdd && (
                          <>
                            <span className=" cursor-pointer pl-1" onClick={handleSelectNavigators}>OR</span>
                            <span className="text-primary underline cursor-pointer pl-1" onClick={handleAddNavigator}>+ Add Navigator</span>
                          </>
                          )}
                        </div>
                        )}
      </div>
      {!(selectedNavigators.length > 0) && (
      <p className="text-sm text-[#646F79]">
        This part does not have any navigators added.
        <span className="text-primary underline cursor-pointer pl-1" onClick={handleSelectNavigators}>Select Navigators</span>
        { allowAdd && (
        <>
          <span className=" cursor-pointer pl-1" onClick={handleSelectNavigators}>OR</span>
          <span className="text-primary underline cursor-pointer pl-1" onClick={handleAddNavigator}>+ Add Navigator</span>
        </>
        )}
      </p>
      )}
      <CustomTable
        variant="addsection-navigator-table"
        source={selectedNavigators}
        metadata={{
          setSelected: setSelectedNavigators,
        }}
        className="border-b-0"
      />
      <SelectNavigator
        open={openSelectNavigators}
        setOpen={setOpenSelectNavigators}
        selectedNavigators={selectedNavigators}
        setSelectNavigators={setSelectedNavigators}
        modalLabel="Select Navigator(s)"
      />

    </div>
  );
}

export default NavigatorSelectionView;
