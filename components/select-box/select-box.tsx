/* eslint-disable react/no-array-index-key */
import React from 'react';
import {
  FormControl, InputLabel, MenuItem, Select,
} from '@mui/material';
import { styled } from '@mui/system';

const StyledFormControl = styled(FormControl)({
  backgroundColor: 'white',
});

const StyledInputLabel = styled(InputLabel)({
  backgroundColor: 'transparent',
  '&.Mui-focused': {
    backgroundColor: 'transparent',
  },
  '&.MuiInputLabel-shrink': {
    backgroundColor: 'transparent',
  },
});

const StyledSelect = styled(Select)({
  backgroundColor: 'white',
  borderRadius: '8px',
  border: '1px solid #D5DCE2',
  height: '56px',
  '& .MuiFilledInput-root': {
    backgroundColor: 'white',
    border: 'none',
    '&:hover': {
      backgroundColor: 'white',
    },
  },
  '& .MuiFilledInput-underline': {
    '&:before': {
      borderBottom: 'none',
    },
    '&:after': {
      borderBottom: 'none',
    },
    '&:hover:not(.Mui-disabled):before': {
      borderBottom: 'none',
    },
  },
  '&:hover': {
    backgroundColor: 'white',
  },
});

interface SelectBoxProps {
  // eslint-disable-next-line react/no-unused-prop-types, no-unused-vars
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
  minWidth?: number | string;
  items?: {
    key: string;
    title: string;
  }[];
  keyVal: string;
  label: string;
  defaultValue?: string;
  labelStyle?: React.CSSProperties;

}
function SelectBox({
  onChange, label, keyVal, items = [], minWidth = '100%', defaultValue = '', labelStyle,
}: SelectBoxProps) {
  return (
    <StyledFormControl className="w-5" variant="filled" sx={{ minWidth }}>
      <StyledInputLabel id={`select-input-label-${keyVal}`}>
        {label}
      </StyledInputLabel>
      <StyledSelect
        name={keyVal}
        style={{
          // first letter uppercase
          textTransform: 'capitalize',
          fontSize: '0.9em', // Makes the text appear smaller
          ...labelStyle,
        }}
        className="first-letter:uppercase ml-[-1px]"
        value={defaultValue}
        labelId={`select-input-label-${keyVal}`}
        id={`select-input-${keyVal}`}
        onChange={(e) => {
          if (onChange) onChange(e as any);
        }}
      >
        {items?.map((item) => (
          <MenuItem key={item?.key} value={item.key?.toLowerCase()}>
            <span className="first-letter:uppercase">{item.title}</span>
          </MenuItem>
        ))}
      </StyledSelect>

    </StyledFormControl>

  );
}

export default SelectBox;
