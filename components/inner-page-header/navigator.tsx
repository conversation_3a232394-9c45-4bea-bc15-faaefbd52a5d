/* eslint-disable jsx-a11y/no-static-element-interactions */

import { Button } from '@mui/material';
import { useRouter } from 'next/router';

interface InnerPageHeaderProps {
  add?: boolean;
  title: string;
  addLabel?: string;
  updateLabel: string;
  btnName: string;
  backRoute?: string;
  // eslint-disable-next-line no-undef
  rightSide?: React.ReactNode;
  callback?: () => void;
  className?: string;
  navigatorName: string;
  navigatorId: string;
  updateUrl?: string;
}

/* eslint-disable jsx-a11y/click-events-have-key-events */
function NavigatorInnerPageHeader({
  title,
  add,
  updateLabel,
  addLabel,
  backRoute,
  btnName,
  rightSide = null,
  callback = () => null,
  className = '',
  navigatorName,
  navigatorId,
  updateUrl,
}: InnerPageHeaderProps) {
  const router = useRouter();
  function handleCrumbClick() {
    if (updateUrl) {
      router.push(updateUrl);
    }
  }
  return (
    <div
      className={`col-span-3 items-center grid grid-flow-col auto-cols-auto justify-between w-full pl-4 pt-1 ${className}`}
    >
      <p className="text-[21px] p-0 m-0 font-[500] col-span-3">
        <span
          className="font-[300] cursor-pointer"
          onClick={() => {
            router.push('/navigators');
          }}
        >
          Navigators
        </span>
        <span
          className="font-[300] cursor-pointer"
          onClick={() => {
            router.push(`/navigators/${navigatorId}`);
          }}
        >
          {` > ${navigatorName}`}
        </span>
        {/* {` > ${add ? addLabel : updateLabel}`} */}
        <span
          onClick={handleCrumbClick}
          className={updateUrl && 'font-[300] cursor-pointer'}
        >
          {` > ${updateLabel}`}
        </span>
        {add ? <span>{` > ${addLabel}`}</span> : ''}
      </p>
      {/* <p className="text-lg p-0 m-0 font-[500] col-span-3">
        <span
          className="font-[300] cursor-pointer"
          onClick={() => {
            router.replace(backRoute);
          }}
        >
          {title}
        </span>
        <span
          className="cursor-pointer font-[300]"
          onClick={() => {
            router.push(`/navigators/${navigatorId}`);
          }}
        >
          {` > ${navigatorName}`}
        </span>
        {' > Task'}
        {` > ${add ? addLabel : updateLabel}`}
      </p> */}
      {rightSide || (
        <Button
          onClick={() => {
            callback();
          }}
          style={{ minWidth: '100px' }}
          variant="contained"
          className="rounded-md h-11 bg-icon-check-white bg-no-repeat bg-origin-content bg-left p-3 font-normal text-sm"
        >
          <span className="text-center font-semibold pl-3">{btnName}</span>
        </Button>
      )}
    </div>
  );
}

export default NavigatorInnerPageHeader;
