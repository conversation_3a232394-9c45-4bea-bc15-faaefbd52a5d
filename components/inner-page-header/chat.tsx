/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable @typescript-eslint/no-explicit-any */

import { Button } from '@mui/material';
import { useRouter } from 'next/router';
import React from 'react';

interface InnerPageHeaderProps {
  add: boolean;
  title: string;
  addLabel?: string;
  updateLabel?: string;
  btnName: string;
  backRoute?: string;
  rightSide?: React.ReactNode;
  callback?: () => void;
  className?: string;
  updateUrl?: string;
  titleLink?: boolean;
  chatTitle: string; // Updated to be an array of strings
}

/* eslint-disable jsx-a11y/click-events-have-key-events */
function ChatInnerPageHeader({
  title,
  add,
  updateLabel,
  addLabel,
  btnName,
  rightSide = null,
  callback = () => null,
  className = '',
  updateUrl,
  titleLink = true,
  chatTitle,
}: InnerPageHeaderProps) {
  const router = useRouter();
  function handleCrumbClick() {
    if (updateUrl) {
      router.push(updateUrl);
    }
  }
  return (
    <div
      className={`col-span-3 items-center grid grid-flow-col auto-cols-auto justify-between w-full pl-3 ${className}`}
    >
      <p className="text-[21px] p-0 m-0 font-[500] col-span-3">
        <span
          className={`cursor-pointer ${titleLink ? 'font-[300]' : 'font-[500]'}`}
          onClick={() => {
            router.push('/chats');
          }}
        >
          {title}
        </span>
        <span
          onClick={handleCrumbClick}
          className={updateUrl && 'font-[300] cursor-pointer'}
        >
          {` > ${chatTitle}`}
        </span>
        {add ? <span>{` > ${addLabel}`}</span> : ''}
      </p>
      {rightSide || (
        <Button
          onClick={() => {
            callback();
          }}
          style={{ minWidth: '100px' }}
          variant="contained"
          className="rounded-md h-11 bg-icon-check-white bg-no-repeat bg-origin-content bg-left p-3 font-normal text-sm"
        >
          <span className="text-center font-semibold pl-3">{btnName}</span>
        </Button>
      )}
    </div>
  );
}

export default ChatInnerPageHeader;
