/* eslint-disable jsx-a11y/no-static-element-interactions */

import { Button } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import { useRouter } from 'next/router';

interface InnerPageHeaderProps {
    add: boolean;
    title: string;
    addLabel?: string;
    updateLabel?: string;
    btnName: string;
    backRoute: string;
    // eslint-disable-next-line no-undef
    rightSide?: React.ReactNode
    callback?: () => void;
    className?: string;
    titleLink?: boolean;
}

/* eslint-disable jsx-a11y/click-events-have-key-events */
function InnerPageHeader({
  title, add, updateLabel, addLabel, backRoute, btnName,
  rightSide = null,
  callback = () => null,
  className = '',
  titleLink = true,
}: InnerPageHeaderProps) {
  const router = useRouter();
  return (
    <div className={`col-span-3 items-center grid grid-flow-col auto-cols-auto place-content-between w-full ${className} pl-3 pr-5`}>
      <p className="text-[21px] p-0 m-0 font-[500] col-span-3">
        <span
          className={`${titleLink ? 'font-[300] cursor-pointer' : 'font-[600]'}`}
          onClick={() => {
            router.replace(backRoute);
          }}
        >
          {title}
        </span>
        {/* eslint-disable-next-line no-nested-ternary */}
        {add ? ` > ${addLabel}` : updateLabel ? ` > ${updateLabel}` : null}
      </p>
      {rightSide || (
      <Button
        onClick={() => {
          callback();
        }}
        style={{ minWidth: '100px' }}
        variant="contained"
        className="rounded-md h-11 p-3 font-normal text-sm"
      >
        <CheckIcon style={{ width: '18px', height: '18px' }} className="mr-2" />
        <span className="text-center font-semibold">{btnName}</span>
      </Button>
      )}
    </div>
  );
}

export default InnerPageHeader;
