{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Consent Form Configuration Schema", "description": "JSON schema for defining dynamic consent forms", "type": "object", "required": ["metadata", "header", "sections", "signature"], "properties": {"metadata": {"type": "object", "required": ["id", "version", "fileNamePrefix"], "properties": {"id": {"type": "string", "description": "Unique identifier for this consent form"}, "version": {"type": "string", "description": "Version of the consent form"}, "fileNamePrefix": {"type": "string", "description": "Prefix for generated PDF/image files"}, "description": {"type": "string", "description": "Optional description of the consent form"}}}, "header": {"type": "object", "required": ["title"], "properties": {"title": {"type": "string", "description": "Main title of the consent form"}, "subtitle": {"type": "string", "description": "Optional subtitle or description"}, "organization": {"type": "string", "description": "Organization name"}}}, "sections": {"type": "array", "description": "Array of form sections", "items": {"type": "object", "required": ["id", "title", "type"], "properties": {"id": {"type": "string", "description": "Unique identifier for this section"}, "title": {"type": "string", "description": "Section title"}, "type": {"type": "string", "enum": ["text", "list", "contact", "nested-list"], "description": "Type of content in this section"}, "content": {"description": "Section content - varies by type", "oneOf": [{"type": "string", "description": "For text type sections"}, {"type": "array", "description": "For list type sections", "items": {"oneOf": [{"type": "string"}, {"type": "object", "properties": {"text": {"type": "string"}, "subItems": {"type": "array", "items": {"type": "string"}}}}]}}, {"type": "object", "description": "For contact type sections", "properties": {"description": {"type": "string"}, "phone": {"type": "string"}, "email": {"type": "string"}, "address": {"type": "string"}}}]}, "emphasis": {"type": "boolean", "description": "Whether to emphasize this section", "default": false}, "required": {"type": "boolean", "description": "Whether this section is required", "default": true}}}}, "signature": {"type": "object", "required": ["required"], "properties": {"required": {"type": "boolean", "description": "Whether signature is required"}, "label": {"type": "string", "description": "Label for signature field", "default": "Participant Signature:"}, "participantNameLabel": {"type": "string", "description": "Label for participant name field", "default": "Participant Name:"}, "dateLabel": {"type": "string", "description": "Label for date field", "default": "Date:"}, "signatureLineText": {"type": "string", "description": "Text shown under signature line", "default": "Signature"}}}, "styling": {"type": "object", "description": "Optional styling configuration", "properties": {"primaryColor": {"type": "string", "description": "Primary color for headers and buttons", "default": "#2c5aa0"}, "fontFamily": {"type": "string", "description": "Font family for the form", "default": "<PERSON>l, sans-serif"}}}}}