import React from 'react';

// Utility function to parse simple markdown-style formatting
const parseMarkdownText = (text) => {
  if (!text) return text;

  // Replace **bold** with <strong> tags
  let parsed = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');

  // Replace \\n with actual line breaks
  parsed = parsed.replace(/\\n/g, '\n');

  return parsed;
};

// Component to render formatted text with markdown support
function FormattedText({ children, className = '' }) {
  const formattedText = parseMarkdownText(children);

  // Split by newlines and render each line
  const lines = formattedText.split('\n');

  return (
    <div className={className}>
      {lines.map((line, index) => (
        <React.Fragment key={index}>
          {index > 0 && <br />}
          <span dangerouslySetInnerHTML={{ __html: line }} />
        </React.Fragment>
      ))}
    </div>
  );
}

// Header component
export function FormHeader({ header }) {
  return (
    <header className="form-header">
      <h1>{header.title}</h1>
      {header.subtitle && (
        <div className="subtitle">
          <FormattedText>{header.subtitle}</FormattedText>
        </div>
      )}
    </header>
  );
}

// Text section component
export function TextSection({ section }) {
  return (
    <section className="form-section">
      <h2>{section.title}</h2>
      <FormattedText>{section.content}</FormattedText>
    </section>
  );
}

// List item component (handles both simple strings and nested items)
function ListItem({ item }) {
  if (typeof item === 'string') {
    return (
      <li>
        <FormattedText>{item}</FormattedText>
      </li>
    );
  }

  // Handle nested list items
  if (item.text && item.subItems) {
    return (
      <li>
        <FormattedText>{item.text}</FormattedText>
        <ul>
          {item.subItems.map((subItem, index) => (
            <li key={index}>
              <FormattedText>{subItem}</FormattedText>
            </li>
          ))}
        </ul>
      </li>
    );
  }

  return null;
}

// List section component
export function ListSection({ section }) {
  return (
    <section className="form-section">
      <h2>{section.title}</h2>
      <ul>
        {section.content.map((item, index) => (
          <ListItem key={index} item={item} />
        ))}
      </ul>
    </section>
  );
}

// Contact section component
export function ContactSection({ section }) {
  const { content } = section;

  return (
    <section className="form-section">
      <h2>{section.title}</h2>
      <div className={section.emphasis ? 'contact-box' : ''}>
        {content.description && (
          <FormattedText>{content.description}</FormattedText>
        )}
        {content.phone && (
          <p>
            📞 <strong>{content.phone}</strong>
          </p>
        )}
        {content.email && (
          <p>
            ✉️ <strong>{content.email}</strong>
          </p>
        )}
        {content.address && (
          <p>
            📍 <strong>{content.address}</strong>
          </p>
        )}
      </div>
    </section>
  );
}

// Main section renderer that delegates to appropriate component
export function SectionRenderer({ section }) {
  switch (section.type) {
    case 'text':
      return <TextSection section={section} />;
    case 'list':
    case 'nested-list':
      return <ListSection section={section} />;
    case 'contact':
      return <ContactSection section={section} />;
    default:
      console.warn(`Unknown section type: ${section.type}`);
      return <TextSection section={section} />;
  }
}

export default SectionRenderer;
