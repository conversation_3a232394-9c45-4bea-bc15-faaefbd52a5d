/* eslint-disable import/extensions */
/* eslint-disable no-alert */
/* eslint-disable no-param-reassign */
/* eslint-disable no-console */
/* eslint-disable react/prop-types */
import React, { useState, useRef } from 'react';
import SignatureCanvas from 'react-signature-canvas';
import JsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import API from 'api/src/lib/api';
import { useRouter } from 'next/router';
import { FormHeader, SectionRenderer } from './FormRenderer';

function ConsentForm({ consentData, memberId }) {
  // Provide default consent data if none provided
  const defaultConsentData = {
    metadata: { fileNamePrefix: 'Consent' },
    header: { title: 'Consent Form', subtitle: '' },
    sections: [],
    signature: {
      required: true,
      label: 'Participant Signature:',
      participantNameLabel: 'Participant Name:',
      dateLabel: 'Date:',
      signatureLineText: 'Signature',
    },
  };

  const formData = consentData || defaultConsentData;
  const [participantName, setParticipantName] = useState('');
  const [date, setDate] = useState(new Date().toISOString().split('T')[0]);
  const [isSigned, setIsSigned] = useState(false);
  const sigCanvas = useRef({});
  const formRef = useRef();
  const router = useRouter();
  const clearSignature = () => {
    sigCanvas.current.clear();
    setIsSigned(false);
  };

  const handleSignatureEnd = () => {
    setIsSigned(!sigCanvas.current.isEmpty());
  };

  const createSignatureImage = () => {
    // Create a new canvas with white background to ensure visibility
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // Set canvas size to match signature canvas
    canvas.width = 500;
    canvas.height = 200;

    // Fill with white background
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Get the original signature canvas
    const originalCanvas = sigCanvas.current.getCanvas();

    // Verify the original canvas has actual signature data
    const originalImageData = originalCanvas
      .getContext('2d')
      .getImageData(0, 0, originalCanvas.width, originalCanvas.height);
    const pixels = originalImageData.data;
    let hasNonWhitePixels = false;

    // Check for non-white pixels (signature content)
    for (let i = 0; i < pixels.length; i += 4) {
      const r = pixels[i];
      const g = pixels[i + 1];
      const b = pixels[i + 2];
      const a = pixels[i + 3];

      // If we find a pixel that's not white or transparent, we have signature content
      if (a > 0 && (r < 255 || g < 255 || b < 255)) {
        hasNonWhitePixels = true;
        break;
      }
    }

    if (!hasNonWhitePixels) {
      console.error('❌ No signature content detected in canvas');
      throw new Error('Signature canvas appears to be empty');
    }

    // Draw the signature on top of the white background
    ctx.drawImage(originalCanvas, 0, 0);

    // Get the final image data
    const signatureData = canvas.toDataURL('image/png');

    // Additional validation: check if signature data is substantial
    if (signatureData.length < 2000) {
      console.error('❌ Signature data appears too small');
      throw new Error('Generated signature image is too small to be valid');
    }

    return Promise.resolve(signatureData);
  };

  const generatePDF = async () => {
    // ✅ Validate participant name
    if (!participantName.trim()) {
      alert('Please enter participant name');
      return;
    }

    // ✅ If signature is required, ensure it has been provided
    if (formData.signature.required) {
      if (!isSigned) {
        alert('Please provide your signature');
        return;
      }
      if (sigCanvas.current.isEmpty()) {
        alert('Please provide your signature before generating PDF');
        setIsSigned(false);
        return;
      }
    }

    try {
      let signatureImageData = null;

      // ✅ Try creating signature image if required
      if (formData.signature.required) {
        try {
          signatureImageData = await createSignatureImage();
        } catch (signatureError) {
          console.error(
            '❌ Signature validation failed:',
            signatureError.message
          );
          alert(
            `Signature validation failed: ${signatureError.message}. Please ensure you have provided a clear signature.`
          );
          return;
        }
      }

      // ✅ Temporarily hide buttons during capture
      const controls = document.querySelectorAll('.form-controls');
      controls.forEach((control) => {
        control.style.display = 'none';
      });

      // ✅ Capture form as a high-resolution canvas
      const canvas = await html2canvas(formRef.current, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
      });

      // ✅ Restore hidden controls
      controls.forEach((control) => {
        control.style.display = 'block';
      });

      // ✅ Prepare a composite canvas to draw both form and signature
      const compositeCanvas = document.createElement('canvas');
      const ctx = compositeCanvas.getContext('2d');

      compositeCanvas.width = canvas.width;
      compositeCanvas.height = canvas.height;

      // ✅ Draw the form content on the composite canvas
      ctx.drawImage(canvas, 0, 0);

      // ✅ Measure form and signature DOM elements
      const formElement = formRef.current;
      const signatureElement = document.getElementById(
        'signatureCanvasContainer'
      );

      const formBox = formElement.getBoundingClientRect();
      const signatureBox = signatureElement.getBoundingClientRect();

      const canvasScale = 2; // Matches html2canvas scale

      // ✅ Calculate signature position relative to form
      const offsetLeft = (signatureBox.left - formBox.left) * canvasScale;
      const offsetTop = (signatureBox.top - formBox.top) * canvasScale - 25;

      // ✅ Signature image size (must match original dimensions)
      const signatureWidth = 500 * canvasScale;
      const signatureHeight = 200 * canvasScale;

      // ✅ Final PDF generation function
      const generateFinalPDF = () => {
        // Convert full canvas to base64 image
        const finalImageData = compositeCanvas.toDataURL('image/jpeg', 0.85);
        const pdf = new JsPDF('p', 'mm', 'a4');
        const pdfWidth = pdf.internal.pageSize.getWidth();
        const pdfHeight = pdf.internal.pageSize.getHeight();
        
        const imgWidth = compositeCanvas.width;
        const imgHeight = compositeCanvas.height;
        
        const ratio = Math.min(
          pdfWidth / (imgWidth * 0.264583),
          (pdfHeight - 10) / (imgHeight * 0.264583)
        );
        
        const finalWidth = imgWidth * 0.264583 * ratio;
        const finalHeight = imgHeight * 0.264583 * ratio;
        
        const x = (pdfWidth - finalWidth) / 2;
        const y = 0; // Top-align the form
        
        pdf.addImage(finalImageData, 'JPEG', x, y, finalWidth, finalHeight);

        // Create a filename from form metadata and input
        const fileName = `${
          consentData?.metadata?.fileNamePrefix || 'Consent'
        }_${participantName.replace(/\s+/g, '_')}_${date}.pdf`;

        // 🔽 Now create a base64 string to send to API
        const pdfBlob = pdf.output('blob');

        const reader = new FileReader();
        reader.onloadend = async () => {
          const base64String = reader.result;

          try {
            // Send to API
            await API.MEMBERS.createMemberAttachment(memberId, {
              name: fileName,
              category: 'consent',
              base64: base64String, // Full base64 string with prefix (e.g., 'data:application/pdf;base64,...')
              isConsent: true,
              type: 'attachment',
            });

            router.push(`/members/encounters/${memberId}`);
          } catch (uploadError) {
            console.error('❌ Error uploading PDF:', uploadError);
            alert('PDF was saved, but uploading failed.');
          }
        };

        reader.readAsDataURL(pdfBlob); // This triggers reader.onloadend
      };

      // ✅ Composite signature on top if required
      if (formData.signature.required && signatureImageData) {
        const signatureImg = new Image();
        signatureImg.onload = () => {
          // Optional: shrink width slightly if it's too wide
          const adjustedWidth = signatureWidth * 0.99;
          const adjustedLeft =
            offsetLeft + (signatureWidth - adjustedWidth) / 2 + 4;
          const adjustedHeight = signatureHeight * 0.99;
          ctx.drawImage(
            signatureImg,
            adjustedLeft,
            offsetTop,
            adjustedWidth,
            adjustedHeight
          );
          generateFinalPDF();
        };
        signatureImg.onerror = () => {
          console.error('❌ Failed to load signature for PDF composite');
          alert('Error adding signature to PDF. Please try again.');
        };
        signatureImg.src = signatureImageData;
      } else {
        generateFinalPDF();
      }
    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Error generating PDF. Please try again.');
    }
  };

  return (
    <div className="consent-container">
      <div ref={formRef} className="consent-form">
        {/* Dynamic header */}
        <FormHeader header={formData.header} />

        {/* Dynamic sections */}
        {formData.sections.map((section) => (
          <SectionRenderer key={section.id} section={section} />
        ))}

        {/* Participant information section - always shown */}
        <section className="signature-section">
          <div className="participant-info">
            <div className="input-group">
              <label htmlFor="participantName">
                {formData.signature.participantNameLabel}
              </label>
              <input
                type="text"
                id="participantName"
                value={participantName}
                onChange={(e) => setParticipantName(e.target.value)}
                placeholder="Enter full name"
                required
              />
            </div>
            <div className="input-group">
              <label htmlFor="date">{formData.signature.dateLabel}</label>
              <input
                type="date"
                id="date"
                value={date}
                onChange={(e) => setDate(e.target.value)}
                required
              />
            </div>
          </div>

          {/* Signature area - only shown if required */}
          {formData.signature.required && (
            <div className="signature-area">
              <div className="signature-label">{formData.signature.label}</div>
              <div
                className="signature-canvas-container"
                id="signatureCanvasContainer"
              >
                <SignatureCanvas
                  ref={sigCanvas}
                  penColor="#000000"
                  backgroundColor="#ffffff"
                  canvasProps={{
                    width: 500,
                    height: 200,
                    className: 'signature-canvas',
                    style: { backgroundColor: '#ffffff' },
                  }}
                  onEnd={handleSignatureEnd}
                  clearOnResize={false}
                />
              </div>
              <div className="signature-line">
                <span>{formData.signature.signatureLineText}</span>
              </div>
            </div>
          )}
        </section>
      </div>

      <div className="form-controls">
        {formData.signature.required && (
          <button
            type="button"
            onClick={clearSignature}
            className="btn-secondary btn"
          >
            Clear Signature
          </button>
        )}
        <button type="button" onClick={generatePDF} className="btn-primary btn">
          Save
        </button>
      </div>
    </div>
  );
}

export default ConsentForm;
