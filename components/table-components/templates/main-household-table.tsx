/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable react/destructuring-assignment */
// eslint-disable-next-line import/no-cycle
import { capitalizeWords } from 'utils/helper';
// eslint-disable-next-line import/no-cycle
import { useModal } from 'contexts/modal-context/modal-context';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import API from 'api/src/lib/api';
import ActionContainer from 'components/action-container/action-container';
// eslint-disable-next-line import/no-cycle
import { TableRenderProps } from 'components/table-components/render';
import { HouseHold } from 'types/household-types';

function MainHouseholdRender(props:TableRenderProps<HouseHold>) {
  const {
    data: {
      address, members, teams, householdScore,
    },
    value,
    getScoreTitle,
    router,
    sKey,
  } = props;
  const { showModal } = useModal();
  const { refresh } = useRefreshSSR();

  const handleOnDelete = (household: HouseHold) => {
    showModal('Warning', 'This process will permanently alter or delete data, settings, or configurations. Once initiated, there is no possibility of recovery or restoration. Are you sure to delete this household?', async () => {
      await API.HOUSEHOLDS.delete(household.id);
      refresh();
    });
  };
  const handleOnEdit = (household: HouseHold) => {
    router.push(`/households/add-edit/${household.id}`);
  };

  switch (sKey) {
    case 'title':
      return (
        <div
          onClick={
            () => handleOnEdit(props.data)
        }
          className="grid grid-flow-col cursor-pointer items-center justify-start"
        >
          <p className="font-[500] pr-1">{`${value} `}</p>
        </div>
      );
    case 'address':
      return <div className="font-[300]">{address?.length > 0 ? `${address[0].street}, ${address[0]?.street2 || ''} ${capitalizeWords(address[0].city)}, ${capitalizeWords(address[0].state)}, ${capitalizeWords(address[0].zip)} ` : ''}</div>;

    case 'members':
      return <div className="font-[300] text-center">{members?.length ? members.length : ''}</div>;
    // case 'lastVisit':
    case 'teams':
      if (!teams) return <p />;
      return (
        <div className="font-[300] grid grid-flow-col auto-cols-max gap-1">
          {teams?.length > 0 ? teams?.map((team, i) => (
            <span className="pr-1 first-letter:uppercase font-[400] text-sm underline" key={team.name}>
              {team.name}
              {i < members.length - 1 && ','}
            </span>
          )) : <p className="p-0 m-0">Unassigned</p>}

        </div>
      );
    //   return <div className="font-[300]">{dateFormat(lastVisit || '')}</div>;
    case 'householdScore':
      // eslint-disable-next-line no-case-declarations
      const score = getScoreTitle(householdScore) || { title: '', color: '' };
      return <div style={{ color: score.color }} className="font-[300]">{score.title}</div>;

    case 'actions':
      return (
        <ActionContainer
          onEdit={() => handleOnEdit(props.data)}
          onDelete={() => handleOnDelete(props.data)}
          deleteLabel="Delete Household"
          editLabel="Edit Household"
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default MainHouseholdRender;
