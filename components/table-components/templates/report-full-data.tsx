/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px]  p-0 m-0  text-[#001018] first-letter:uppercase';

function ReportFullDataTable(props:TableRenderProps<any>) {
  const { sKey, value } = props;

  switch (sKey) {
    case 'name':
      return <p className={pStyle}>{value}</p>;

    case 'value':
      return <p className={pStyle}>{value}</p>;
    case 'percentage':
      return <p className={pStyle}>{value}</p>;
    default:
      return <p className={pStyle}>{value}</p>;
  }
}

export default ReportFullDataTable;
