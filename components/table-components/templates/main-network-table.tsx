/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import { Network } from 'types/networks-types';
import { useModal } from 'contexts/modal-context/modal-context';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import API from 'api/src/lib/api';
import { useRouter } from 'next/router';
import SafeDate from 'components/date-time/SafeDate';
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px]  p-0 m-0  text-[#001018] first-letter:uppercase';
let constants: string | boolean | null;
function MainNetworkTable(props:TableRenderProps<Network>) {
  const { showModal } = useModal();
  const { refresh } = useRefreshSSR();
  const router = useRouter();
  const {
    data: {
      name, types, address, services, updatedAt,
    },
    select,
    children,
    value,
    sKey,
  } = props;

  const handleOnDelete = (network:Network) => {
    showModal('Warning', 'Are you sure to delete this network?', async () => {
      await API.NETWORKS.delete(network.id);
      refresh();
    });
  };

  // const handleOnEdit = (team:Teams) => {
  //   router.push(`/teams/add-edit/${team.id}`);
  // };

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'title':
      return (
        <div
          className={`grid grid-flow-col  items-center justify-start ${!select ? 'cursor-pointer' : ''}`}
        >
          {children}
          <p
            onClick={
          () => {
            if (!select) {
              router.push(`/networks/add-edit/${props?.data.id || 'add'}`);
            }
          }
        }
            className=" w-full  text-[#001018] font-[500]"
          >
            {name}

          </p>
        </div>
      );
    case 'types':
      return <p className={pStyle}>{types.join(', ')}</p>;
    case 'address':
      return (
        address.length === 0 ? (
          <p className="pStyle">
            N/A
          </p>
        ) : (
          <div className="grid grid-flow-col auto-cols-max gap-1">
            <p className={pStyle}>{address[0]?.street}</p>
            <p className={pStyle}>{address[0]?.street2}</p>
            <p className={pStyle}>{address[0]?.city}</p>
            <p className={pStyle}>{address[0]?.state}</p>
            <p className={pStyle}>{address[0]?.zip}</p>

          </div>
        )
      );
    case 'services':
      return (
        <p className={pStyle}>
          {services.map((service) => service.name).join(', ')}
        </p>
      );
    case 'updatedAt':
      return <p className={pStyle}><SafeDate date={updatedAt as string} /></p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Network"
          editLabel="Edit Network"
          onDelete={() => {
            // eslint-disable-next-line react/destructuring-assignment
            handleOnDelete(props.data);
          }}
          onEdit={() => {
            // eslint-disable-next-line react/destructuring-assignment
            // handleOnEdit(props.data);
            router.push(`/networks/add-edit/${props?.data.id || 'add'}`);
          }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default MainNetworkTable;
