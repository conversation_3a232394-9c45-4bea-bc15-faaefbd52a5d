/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { capitalizeWords } from 'utils/helper';
import ActionContainer from 'components/action-container/action-container';
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px]  p-0 m-0  text-[#001018] first-letter:uppercase';
let constants: string | boolean | null;
function SocialPlanNetworkTable(props: TableRenderProps<any>) {
  const {
    data: {
      network, name, types, address, services, distanceToMember,
    },
    value,
    sKey,
  } = props;

  const handleRemoveItem = (id: string, hereTypes: string) => {
    const newList: any = (props?.metadata as any)?.list.map((item: any) => {
      if (item.type === hereTypes) {
        return {
          ...item,
          network: item.network.filter((x: any) => x.id !== id),
        };
      }
      return item;
    });
    // eslint-disable-next-line no-unused-expressions
    props?.metadata?.setSelected && props?.metadata?.setSelected(newList);
  };

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'title':
      return (
        <p className="text-[13px] p-0 m-0  text-[#001018] font-[500]">
          {network?.name || name}
        </p>
      );
    case 'types':
      return <p className={pStyle}>{types.join(', ')}</p>;
    case 'address':
      return (
        <p className="text-[13px] p-0 m-0  text-[#001018] font-[500]">
          {`${capitalizeWords(address?.[0]?.street, true)}, ${capitalizeWords(address?.[0]?.city, true)}, ${capitalizeWords(address?.[0]?.state, true)} ${address?.[0]?.zip}` || ''}
        </p>
      );
    case 'distance':
      return <p className={pStyle}>{ distanceToMember ? `${distanceToMember} mi` : '-'}</p>;
    case 'services':
      return <p className={pStyle}>{services?.length || '-'}</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Remove Network"
          // editLabel="Edit Member"
          onDelete={() => {
            // eslint-disable-next-line react/destructuring-assignment
            handleRemoveItem(props.data.id, props?.metadata?.sKey as string);
          }}
        />
      );
    default:
      return <p className={pStyle}>{value}</p>;
  }
}

export default SocialPlanNetworkTable;
