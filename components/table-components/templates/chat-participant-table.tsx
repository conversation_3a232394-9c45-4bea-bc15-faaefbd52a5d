/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import { useModal } from 'contexts/modal-context/modal-context';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useState, useEffect, useContext } from 'react';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { Participant } from 'types/chats-types';
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px]  p-0 m-0  text-[#001018] first-letter:uppercase';
function ChatParticipantTable(props: TableRenderProps<Participant>) {
  const { showModal } = useModal();
  const { refresh } = useRefreshSSR();
  const {
    data: {
      firstName, lastName, roles, teams,
    },
    children,
    value,
    sKey,
    router,
  } = props;
  const constants = useContext(ConstantsContext);

  const [formattedRole, setFormattedRole] = useState('');

  // const handleOnDelete = (team: Teams) => {
  //   showModal('Warning', 'Are you sure to delete this team?', async () => {
  //     await API.TEAMS.removeTeam({ teamId: team.id });
  //     refresh();
  //   });
  // };

  // const handleOnEdit = (team: Teams) => {
  //   router.push(`/teams/add-edit/${team.id}`);
  // };

  useEffect(() => {
    const roleTitle = constants?.roles?.find((role) => roles.includes(role.key))?.title;
    setFormattedRole(roleTitle || '');
  }, [roles, constants?.roles]);
  switch (sKey) {
    case 'name':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1 text-[15px]"
          >
            {`${firstName} ${lastName} `}
          </p>
        </div>
      );
    case 'role':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}

          <p
            className="font-[500] pr-1 text-[15px]"
          >
            {`${formattedRole} `}
          </p>
        </div>
      );
    case 'teams':
      return (
        <div className="grid grid-flow-col gap-1 auto-cols-max">
          {teams && teams?.length > 0 ? (
            teams?.map((team, index) => (
              <p className={`${pStyle} overflow-hidden whitespace-nowrap overflow-ellipsis text-[15px]`}>
                {team?.name?.length > 0 ? (
                  <span>
                    {team.name}
                    {index + 1 !== teams?.length && ','}
                  </span>
                ) : (
                  <span className="text-[15px] text-[#DE6F09] first-letter:uppercase">
                    Unassigned
                  </span>
                )}
              </p>
            ))
          ) : (
            <p className="text-[15px] text-[#DE6F09] first-letter:uppercase">
              Unassigned
            </p>
          )}
        </div>
      );
    case 'actions':
      return (
        <ActionContainer deleteLabel="" editLabel="" />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default ChatParticipantTable;
