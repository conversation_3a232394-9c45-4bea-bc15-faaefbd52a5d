/* eslint-disable max-len */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import { capitalizeWords } from 'utils/helper';
import API from 'api/src/lib/api';
import router from 'next/router';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import SafeDate from 'components/date-time/SafeDate';
import { useModal } from 'contexts/modal-context/modal-context';
import { TableRenderProps } from '../../render';

const pStyle = 'text-[15px] p-0 m-0 py-3 text-[#262D2D] font-[500]';
function InsurancesTable(props: TableRenderProps<any>) {
  const {
    data: {
      planName, planType, policyNumber, startDate, endDate, id, userId, status,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;
  const { refresh } = useRefreshSSR();
  const { showModal } = useModal();
  const handleDeleteInsurance = async (id: string) => {
    showModal('Warning', 'Are you sure you want to remove this insurance?', async () => {
      const response = await API.MEMBERS.deleteMemberInsurance(id);
      if (response) {
        refresh();
      }
    });
  };

  switch (sKey) {
    case 'carrier':
      return (
        <div
          className="grid grid-flow-col cursor-pointer items-center justify-start w-full"
          onClick={() => router.push(`/members/insurance/${userId}/detail/${id}`)}
        >
          {children}
          <p className={pStyle}>{planName}</p>
        </div>
      );
    case 'planType':
      return <p className={pStyle}>{capitalizeWords(planType, true)}</p>;
    case 'policyNumber':
      return <p className={pStyle}>{policyNumber}</p>;
    case 'startAndEndDate':
      return (
        <p className={pStyle}>
          <SafeDate date={startDate} />
          {' '}
          -
          {' '}
          {endDate ? <SafeDate date={endDate} /> : 'N/A'}
        </p>
      );
    case 'status':
      return <p className={pStyle}>{capitalizeWords(status, true) || '-'}</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Insurance"
          onDelete={() => handleDeleteInsurance(id)}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default InsurancesTable;
