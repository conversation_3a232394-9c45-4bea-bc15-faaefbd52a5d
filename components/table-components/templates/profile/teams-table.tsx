/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import { capitalizeWords, getLatestImage } from 'utils/helper';
import { StyledAvatar } from 'components/members/social-plans/social-plans-table/styled-components';
import { useRouter } from 'next/router';
import { TableRenderProps } from '../../render';

function TeamsTable(props: TableRenderProps<any>) {
  const {
    data: {
      name, navigators, primaryNavigator, id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  const router = useRouter();

  const primaryNavigatorProfile = getLatestImage(
    primaryNavigator?.attachments,
    'userProfile',
  );

  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
          onClick={() => {
            // router.push(`/members/care-plans/${reciever}/${id}`);
          }}
        >
          {children}
          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1"
          >
            {capitalizeWords(name, true)}
          </p>
        </div>
      );
    case 'navigators':
      return (
        <div className="flex items-center">
          {navigators?.map((item: any) => (
            <StyledAvatar
              src={getLatestImage(item?.attachments, 'userProfile')}
              alt={`${item?.firstName} ${item?.lastName}`}
              style={{
                width: '32px',
                height: '32px',
                marginRight: '5px',
              }}
              key={item.id}
              onClick={() => {
                router.push(`/navigators/${item.id}`);
              }}
              className="cursor-pointer"
            />
          ))}
        </div>
      );
    case 'primaryNavigator':
      return (
        <div
          className="flex items-center cursor-pointer"
          onClick={() => {
            router.push(`/navigators/${primaryNavigator?.id}`);
          }}
        >
          {primaryNavigator && (
          <StyledAvatar
            src={primaryNavigatorProfile}
            alt={`${primaryNavigator?.firstName} ${primaryNavigator?.lastName}`}
            style={{
              width: '32px',
              height: '32px',
            }}
          />
          )}
          <p className="p-0 m-0">
            {primaryNavigator ? `${primaryNavigator?.firstName} ${primaryNavigator?.lastName}`
              : 'Unassigned'}
          </p>
        </div>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Team"
          onDelete={() => handleDelete && handleDelete(id, 'teams')}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default TeamsTable;
