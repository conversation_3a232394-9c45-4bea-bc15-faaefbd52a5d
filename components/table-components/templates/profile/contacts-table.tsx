/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import { capitalizeWords } from 'utils/helper';
import { useContext } from 'react';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { TableRenderProps } from '../../render';

function ContactsTable(props: TableRenderProps<any>) {
  const {
    data: {
      fullName, role, relationship, phone, id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;
  const constants = useContext(ConstantsContext);
  switch (sKey) {
    case 'name':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
          onClick={() => {
            // router.push(`/members/care-plans/${reciever}/${id}`);
          }}
        >
          {children}
          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1"
          >
            {capitalizeWords(fullName, true)}
          </p>
        </div>
      );
    case 'type':
      return <p>{constants?.associatedPersonRole?.find((type: any) => type.key === role)?.title || '-'}</p>;
    case 'relationship':
      return <p>{relationship || '-'}</p>;
    case 'phone':
      return <p>{phone || '-'}</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Contact"
          onDelete={() => handleDelete && handleDelete(id, 'contacts')}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default ContactsTable;
