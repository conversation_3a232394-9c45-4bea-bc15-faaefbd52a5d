/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import { capitalizeWords } from 'utils/helper';
import { TableRenderProps } from '../../render';

function MedicationsTable(props: TableRenderProps<any>) {
  const {
    data: {
      medicationName, adherenceNotes, rxNormCode, id, status,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  const statusColor = status === 'discontinued' ? '#747A7A' : 'dark-text';

  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
          onClick={() => {
            // router.push(`/members/care-plans/${reciever}/${id}`);
          }}
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1 overflow-hidden text-ellipsis whitespace-nowrap"
          >
            {capitalizeWords(medicationName, true)}
          </p>
        </div>
      );
    case 'description':
      return <p>{adherenceNotes?.length > 0 ? capitalizeWords(adherenceNotes) : '-'}</p>;
    case 'code':
      return <p>{rxNormCode?.length > 0 ? rxNormCode : '-'}</p>;
    case 'status':
      return <p style={{ color: statusColor }} className="font-[500]">{status?.length > 0 ? capitalizeWords(status) : '-'}</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Discontinue Medication"
          onDelete={() => handleDelete && handleDelete(id, 'medications')} // waiting on relaxing of PUT /medications/{id}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default MedicationsTable;
