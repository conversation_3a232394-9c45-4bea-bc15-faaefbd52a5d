/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import { capitalizeWords } from 'utils/helper';
import { TableRenderProps } from '../../render';

function ProblemsListTable(props: TableRenderProps<any>) {
  const {
    data: {
      problem, id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
          onClick={() => {
            // router.push(`/members/care-plans/${reciever}/${id}`);
          }}
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1"
          >
            {capitalizeWords(problem?.title, true)}
          </p>
        </div>
      );
    case 'description':
      return <p>{problem?.description?.length > 0 ? capitalizeWords(problem?.description) : '-'}</p>;
    case 'type':
      return <p>{problem?.type || '-'}</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Problem"
          onDelete={() => handleDelete && handleDelete(id, 'problems')}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default ProblemsListTable;
