/* eslint-disable no-nested-ternary */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import {
  calculateAge, capitalizeWords, getKeyValue,
} from 'utils/helper';
import { Member } from 'components/members/members-table/type';
import { StyledAvatar } from 'components/table-components/styled-components';
import ActionContainer from 'components/action-container/action-container';
import { useModal } from 'contexts/modal-context/modal-context';
import API from 'api/src/lib/api';
import SafeDate from 'components/date-time/SafeDate';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { TableRenderProps } from '../render';

const pStyle = 'text-[14px]   text-[#001018] first-letter:uppercase';
let constants: string | boolean | null;

function MainMemberRender(props:TableRenderProps<Member>) {
  // console.log(props.data.id === '384710B4-2413-4ABB-BF4D-6DE6F2B2638B' && console.log(props));
  const { showModal } = useModal();
  const { refresh } = useRefreshSSR();
  const handleDeleteMember = (item: Member) => {
    showModal('Warning', 'This process will permanently alter or delete data, settings, or configurations. Once initiated, there is no possibility of recovery or restoration. Are you sure you want to delete this member?', async () => {
      await API.MEMBERS.deleteMember(item.id);
      await refresh();
    });
  };

  const handleEdit = (item: Member) => {
    // eslint-disable-next-line no-use-before-define
    router.push(`/members/add-edit/${item.id}`);
  };
  const {
    data: {
      firstName, id, lastName, attachments, households, enrolledOn, unenrolledDate,
    },
    value,
    getScoreTitle,
    metadata,
    children,
    router,
    select,
    sKey,
  } = props;

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'firstName':
      // eslint-disable-next-line no-case-declarations
      const filteredAttachments = attachments?.filter((item) => item.kind === 'profile').sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
      const profile = filteredAttachments?.[0];
      return (
        // eslint-disable-next-line max-len
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
        <div
          onClick={() => {
            // eslint-disable-next-line no-unused-expressions
            router && !select && router.push(`/members/profile/${id}`);
          }}
          key={value}
          className="grid grid-flow-col cursor-pointer items-center justify-start relative"
        >
          {metadata?.headId === id && <div className="absolute left-4 top-1 z-10 bg-icon-hoh w-5 h-5 bg-[length:15px_15px] bg-no-repeat" />}
          {children}
          <StyledAvatar className="w-[28px] h-[28px]" alt={firstName} src={profile?.url || ''} />
          <p className={`${pStyle} font-[500] pr-1`}>{`${firstName} `}</p>
          <p className={`${pStyle}  font-[500]`}>{`${lastName} `}</p>
        </div>
      );
    case 'dob':
      return (
        <p key={value} className={`${pStyle}`}>
          <SafeDate date={value as string} time />
          {' '}
          (
          {calculateAge(value as string)}
          )
        </p>
      );
    case 'household':
      return <p key={value} className={`${pStyle}`}>{households ? `${households[0]?.title || 'N/A'}` : 'N/A'}</p>;
    case 'teams':
      return (
        <div key={value} className="grid grid-flow-col gap-1">
          {households?.length > 0 ? households[0]?.teams?.map((team) => (
            <p className={pStyle}>
              {team?.name?.length > 0 ? <span className="underline">{team.name}</span>
                : <span className="text-[15px] text-[#DE6F09] first-letter:uppercase">Unassigned</span>}
            </p>
          )) : <p className="text-[15px] text-[#DE6F09] first-letter:uppercase">Unassigned</p>}
        </div>
      );
    case 'ethnicity':
      return (
        <p key={value} className="first-letter:uppercase">
          {value}
        </p>
      );
    case 'military':
      return (
        <p key={value} className="first-letter:uppercase">
          {capitalizeWords(getKeyValue(value, 'militaryStatus', constants), true)}
        </p>
      );
    case 'pronouns':
      return (
        <p key={value} className="first-letter:uppercase">
          {capitalizeWords(getKeyValue(value, 'pronouns', constants), true)}
        </p>
      );
    case 'lang':
      return (
        <p key={value} className="first-letter:uppercase">
          {capitalizeWords(getKeyValue(value, 'langauges', constants), true)}
        </p>
      );
    case 'sexualIdentity':
      return (
        <p key={value} className="first-letter:uppercase">
          {capitalizeWords(getKeyValue(value, 'sexualIdentities', constants), true) || 'N/A'}
        </p>
      );
    case 'gender':

      return (
        <p key={value} className="first-letter:uppercase">
          {capitalizeWords(getKeyValue(value, 'genderIndentity', constants), true)}
        </p>
      );
    case 'genderIdentity':

      return (
        <p key={value} className="first-letter:uppercase">
          {capitalizeWords(getKeyValue(value, 'genderIndentity', constants), true)}
        </p>
      );
    case 'lastAt':
      return <p key={value} className={pStyle}>{!value ? '-' : <SafeDate date={value as string} time />}</p>;
    case 'lastContact':
      return <p key={value} className={pStyle}>{!value ? '-' : <SafeDate date={value as string} time />}</p>;
    case 'score':
      return <p key={value} className={`${pStyle} text-[${getScoreTitle(value as number)?.color}]`}>{getScoreTitle(value as number)?.title}</p>;
    case 'status':
      return (
        <p key={value} className={pStyle}>
          {getKeyValue(value, 'staffStatus', constants)}
          {' '}
          {unenrolledDate ? (
            <>
              -
              {' '}
              <SafeDate date={unenrolledDate} />
            </>
          ) : enrolledOn ? (
            <>
              -
              {' '}
              <SafeDate date={enrolledOn} />
            </>
          ) : ''}
        </p>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Member"
          editLabel="Edit Member"
          onDelete={() => {
            // eslint-disable-next-line react/destructuring-assignment
            handleDeleteMember(props.data);
          }}
          onEdit={() => {
            // eslint-disable-next-line react/destructuring-assignment
            handleEdit(props.data);
          }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default MainMemberRender;
