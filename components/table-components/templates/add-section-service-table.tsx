/* eslint-disable no-nested-ternary */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */

import ActionContainer from 'components/action-container/action-container';
import { Service } from 'types/service-types';
import { capitalizeWords } from 'utils/helper';
import { TableRenderProps } from '../render';

let constants: string | boolean | null;

function AddSectionServiceTable(props: TableRenderProps<Service>) {
  const deleteService = (item: Service) => {
    if (props.handleDelete) {
      props.handleDelete(item);
    }
  };
  const {
    // eslint-disable-next-line no-empty-pattern
    data: {},
    value,

    sKey,
  } = props;

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col cursor-pointer items-center justify-start"
        >
          <p className="font-[500] pr-1">
            {capitalizeWords(`${props.data.name}`, true)}
          </p>
        </div>
      );
    case 'desc':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          <p className="font-[500] pr-1">
            {capitalizeWords(`${props.data.desc}`)}
          </p>
        </div>
      );
    case 'type':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          <p className="font-[500] pr-1">
            {capitalizeWords(`${props.data.service}`, true)}
          </p>
        </div>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Remove Service"
          editLabel="Edit Service"
          onDelete={() => {
            // eslint-disable-next-line react/destructuring-assignment
            deleteService(props.data);
          }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default AddSectionServiceTable;
