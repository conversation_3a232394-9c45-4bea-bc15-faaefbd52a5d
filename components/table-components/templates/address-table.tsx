/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px]  p-0 m-0  text-[#001018] first-letter:uppercase';
function AddressTable(props: TableRenderProps<any>) {
  const {
    data: { address },
    children,
    value,
    sKey,
  } = props;

  switch (sKey) {
    case 'address':
      return (
        <div
          key={address}
          className="grid grid-flow-col cursor-pointer items-center justify-start w-full cursor-default"
        >
          {children}
          <p className="font-[500] pr-1 text-[15px] truncate">{address}</p>
        </div>
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default AddressTable;
