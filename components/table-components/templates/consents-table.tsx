/* eslint-disable max-len */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
// import ActionContainer from 'components/action-container/action-container';
// import useRefreshSSR from 'hooks/use-refresh-ssr';
import SafeDate from 'components/date-time/SafeDate';
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px]  p-0 m-0  text-[#001018] first-letter:uppercase';
function ConsentsTable(props: TableRenderProps<any>) {
  const {
    data: { name, createdAt, url },
    children,
    value,
    sKey,
    // handleDelete,
  } = props;
  // const { refresh } = useRefreshSSR();
  // const handleOnEdit = (team: Teams) => {
  //   router.push(`/teams/add-edit/${team.id}`);
  // };

  switch (sKey) {
    case 'name':
      return (
        <div
          key={name}
          onClick={() => window.open(url, '_blank')}
          className="grid grid-flow-col cursor-pointer items-center justify-start w-full"
        >
          {children}
          <p className="font-[500] pr-1 text-[15px] truncate">{name}</p>
        </div>
      );
    case 'createdAt':
      return (
        <p className={pStyle}>
          <SafeDate date={createdAt} />
        </p>
      );
    // case 'actions':
    //   return (
    //     <div className="flex justify-center items-center w-[50px]">
    //       <ActionContainer
    //         deleteLabel="?"
    //         onDelete={handleDelete ? () => handleDelete(name) : undefined}
    //         editLabel=""
    //       />
    //     </div>
    //   );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default ConsentsTable;
