/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { StyledAvatar } from 'components/table-components/styled-components';
import ActionContainer from 'components/action-container/action-container';
import { capitalizeWords, getLatestImage, dateFormat } from 'utils/helper';
import { SocialPlan } from 'types/social-plans-types';
import { useEffect, useState } from 'react';
import API from 'api/src/lib/api';
import Cookies from 'js-cookie';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useModal } from 'contexts/modal-context/modal-context';
import router from 'next/router';
import { TableRenderProps } from '../render';

function SocialPlanTable(props: TableRenderProps<SocialPlan>) {
  const {
    data: {
      title, createdAt, creator, items, status, id, reciever,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  const [navigator, setNavigator] = useState<any>(null);
  const [token, setToken] = useState<string | null>(null);
  const [orgId, setOrgId] = useState<string | null>(null);
  const { refresh } = useRefreshSSR();
  const { showModal } = useModal();
  useEffect(() => {
    // Get token and orgId from cookies
    const storedToken = Cookies.get('token');
    const storedOrgId = Cookies.get('orgID');

    setToken(storedToken || null);
    setOrgId(storedOrgId || null);
  }, []);

  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
          onClick={() => {
            router.push(`/members/social-plans/${reciever}/${id}`);
          }}
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1"
          >
            {`${title} `}
          </p>
        </div>
      );
    case 'createdAt':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1"
          >
            {dateFormat(createdAt)}
          </p>
        </div>
      );
    case 'creator':
      return (
        <div className="flex items-center">
          <StyledAvatar
            style={{
              width: '24px',
              height: '24px',
            }}
            src={getLatestImage(navigator?.attachments, 'userProfile')}
          />
          <p>
            {navigator?.firstName}
            {' '}
            {navigator?.lastName}
          </p>
        </div>
      );
    case 'items':
      return (
        <p>
          {items.length}
        </p>
      );
    case 'status':
      return (
        <p className={`${status === 'active' ? 'text-[#008390] font-[600]' : 'text-red-500'}`}>
          {capitalizeWords(status)}
        </p>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="End Social Plan"
          // editLabel="Edit Social Plan"
          onDelete={() => handleDelete && handleDelete(id)}
          // onEdit={() => {
          //   // eslint-disable-next-line react/destructuring-assignment
          //   handleOnEdit(props.data);
          // }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default SocialPlanTable;
