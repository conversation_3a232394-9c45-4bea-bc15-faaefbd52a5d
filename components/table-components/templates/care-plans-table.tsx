/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import { capitalizeWords } from 'utils/helper';
import router from 'next/router';
import SafeDate from 'components/date-time/SafeDate';
import { TableRenderProps } from '../render';

function CarePlansTable(props: TableRenderProps<any>) {
  const {
    data: { carePlan, goalsCount, interventionsCount },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  const statusColors = {
    active: '#008390',
    complete: '#6E6E6E',
  };

  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
          onClick={() => {
            router.push(
              `/members/care-plans/${carePlan?.member?.id}/view/${carePlan?.id}`,
            );
          }}
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1 truncate text-ellipsis whitespace-nowrap"
          >
            {`${carePlan?.title || ''} `}
          </p>
        </div>
      );
    case 'carePlanStartDate':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1"
          >
            <SafeDate date={carePlan?.startDate || carePlan?.createdAt || '-'} />
          </p>
        </div>
      );
    case 'lastReviewed':
      return (
        <p>
          <SafeDate date={carePlan?.lastReviewed || '-'} showDaysFromCurrent />
        </p>
      );
    case 'goals':
      return <p>{goalsCount ?? '-'}</p>;
    case 'interventions':
      return <p>{interventionsCount ?? '-'}</p>;
    case 'status':
      return (
        <p
          className="font-[600]"
          style={{
            color:
              statusColors[carePlan?.status as keyof typeof statusColors]
              || '#E42B57',
          }}
        >
          {capitalizeWords(carePlan?.status)}
        </p>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Care Plan"
          // editLabel="Edit Social Plan"
          onDelete={() => handleDelete && handleDelete(carePlan?.id)}
          // onEdit={() => {
          //   // eslint-disable-next-line react/destructuring-assignment
          //   handleOnEdit(props.data);
          // }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default CarePlansTable;
