/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import SafeDate from 'components/date-time/SafeDate';
import { Avatar } from '@mui/material';
import { capitalizeWords } from 'utils/helper';
import { TableRenderProps } from '../../render';

function NetworkAppointmentsTable(props: TableRenderProps<any>) {
  const {
    data: {
      networkName,
      providerName,
      appointmentDateTime,
      appointmentNote,
      authorProfile,
      id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  switch (sKey) {
    case 'networkName':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
          onClick={() => {
            // router.push(`/members/care-plans/${reciever}/${id}`);
          }}
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1"
          >
            {capitalizeWords(networkName, true)}
          </p>
        </div>
      );
    case 'author':
      return (
        <div
          key={value}
          className="cursor-pointer items-center justify-start flex"
        >
          {children}
          {authorProfile && (
            <Avatar
              src={authorProfile}
              className="w-8 h-8 rounded-full mr-2"
              alt="author profile"
            />
          )}
          <p className="font-[500] pr-1">
            {capitalizeWords(providerName, true)}
          </p>
        </div>
      );
    case 'date':
      return (
        <p>
          <SafeDate date={appointmentDateTime} time />
        </p>
      );
    case 'note':
      return <p>{appointmentNote}</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Appointment"
          onDelete={() => handleDelete && handleDelete(id)}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default NetworkAppointmentsTable;
