/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import SafeDate from 'components/date-time/SafeDate';
import { TableRenderProps } from '../../render';

function FollowUpsTable(props: TableRenderProps<any>) {
  const {
    data: {
      title, author, date, note, outcome, id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
          onClick={() => {
            // router.push(`/members/care-plans/${reciever}/${id}`);
          }}
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1"
          >
            {`${title} `}
          </p>
        </div>
      );
    case 'author':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}

          <p
            className="font-[500] pr-1"
          >
            {author}
          </p>
        </div>
      );
    case 'date':
      return (
        <p>
          <SafeDate date={date} />
        </p>
      );
    case 'note':
      return (
        <p className="truncate text-ellipsis">
          {note}
        </p>
      );
    case 'status':
      return (
        <p className="truncate text-ellipsis">
          {outcome}
        </p>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Review"
          onDelete={() => handleDelete && handleDelete(id)}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default FollowUpsTable;
