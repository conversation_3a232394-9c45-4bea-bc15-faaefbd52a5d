/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
// import router from 'next/router';
// import SafeDate from 'components/date-time/SafeDate';
import { capitalizeWords } from 'utils/helper';
import { TableRenderProps } from '../../render';

function ProblemsTable(props: TableRenderProps<any>) {
  const {
    data: {
      title, icdCode, dateIdentified, clinicalNote, id, type, description,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
          onClick={() => {
            // router.push(`/members/care-plans/${reciever}/${id}`);
          }}
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1"
          >
            {title}
          </p>
        </div>
      );
    case 'code':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}
          <p className="font-[500] pr-1">{icdCode?.toUpperCase() || capitalizeWords(type) || '-'}</p>
        </div>
      );
    case 'dateIdentified':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}

          <p className="font-[500] pr-1">{dateIdentified}</p>
        </div>
      );
    case 'clinicalNote':
      return (
        description.toLowerCase().startsWith('http') ? <a href={description} target="_blank" rel="noopener noreferrer">{description}</a> : <p>{clinicalNote || description}</p>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Problem"
          onDelete={() => handleDelete && handleDelete(id)}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default ProblemsTable;
