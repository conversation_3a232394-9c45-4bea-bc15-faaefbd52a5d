/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import SafeDate from 'components/date-time/SafeDate';
import { TableRenderProps } from '../../render';

function GoalsTable(props: TableRenderProps<any>) {
  const {
    data: {
      title, goalType, targetDate, goalObjective, id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
          onClick={() => {
            // router.push(`/members/care-plans/${reciever}/${id}`);
          }}
        >
          {children}

          <p
            // onClick={() => {
            //   // eslint-disable-next-line react/destructuring-assignment
            //   handleOnEdit(props.data);
            // }}
            className="font-[500] pr-1"
          >
            {`${title} `}
          </p>
        </div>
      );
    case 'goalType':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}

          <p
            className="font-[500] pr-1"
          >
            {goalType}
          </p>
        </div>
      );
    case 'targetDate':
      return (
        <p>
          <SafeDate date={targetDate} />
        </p>
      );
    case 'goalObjective':
      return (
        <p>
          {goalObjective}
        </p>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Goal"
          onDelete={() => handleDelete && handleDelete(id)}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default GoalsTable;
