/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { capitalizeWords } from 'utils/helper';
import ActionContainer from 'components/action-container/action-container';
import { TableRenderProps } from '../../render';

const pStyle = 'first-letter:uppercase';
function SelectMedicationTable(props: TableRenderProps<any>) {
  const {
    data: {
      displayName, isGeneric, rxcui,
    },
    value,
    sKey,
    children,
  } = props;

  switch (sKey) {
    case 'name':
      return (
        <div className="grid grid-flow-col items-center justify-start relative">
          {children}
          <p>{capitalizeWords(displayName, true)}</p>
        </div>
      );
    case 'generic':
      return (
        <p>{isGeneric ? 'Yes' : 'No'}</p>
      );
    case 'code':
      return (
        <p className="">
          {rxcui}
        </p>
      );
    default:
      return <p className={pStyle}>{value}</p>;
  }
}

export default SelectMedicationTable;
