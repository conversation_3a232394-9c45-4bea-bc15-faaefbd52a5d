/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { capitalizeWords } from 'utils/helper';
import ActionContainer from 'components/action-container/action-container';
import { TableRenderProps } from '../../render';

const pStyle = 'first-letter:uppercase';
let constants: string | boolean | null;
function SelectNetworkAppointmentsTable(props: TableRenderProps<any>) {
  const {
    data: {
      name, address, services, types,
    },
    value,
    sKey,
    children,
  } = props;

  const handleRemoveItem = (id: string, hereTypes: string) => {
    const newList: any = (props?.metadata as any)?.list.map((item: any) => {
      if (item.type === hereTypes) {
        return {
          ...item,
          network: item.network.filter((x: any) => x.id !== id),
        };
      }
      return item;
    });
    // eslint-disable-next-line no-unused-expressions
    props?.metadata?.setSelected && props?.metadata?.setSelected(newList);
  };

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'title':
      return (
        <div className="grid grid-flow-col cursor-pointer items-center justify-start relative">
          {children}
          <p className="cursor-pointer">{name}</p>
        </div>
      );
    case 'type':
      return (
        <p className={pStyle}>
          {/* {capitalizeWords(
            [...new Set(services?.map((service: any) => service.type))].join(
              ', ',
            ),
            true,
          )} */}
          {types?.map((type: any) => capitalizeWords(type, true)).join(', ')}
        </p>
      );
    case 'address':
      return (
        <p className="">
          { address.length > 0 ? `${capitalizeWords(address?.[0]?.street, true)}, ${capitalizeWords(
            address?.[0]?.city,
            true,
          )}, ${capitalizeWords(address?.[0]?.state, true)} ${
            address?.[0]?.zip
          }` : '-'}
        </p>
      );
    case 'services':
      return <p className={pStyle}>{services?.length || '-'}</p>;
    case 'distance':
      return <p className={pStyle}>-</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Remove Network"
          // editLabel="Edit Member"
          onDelete={() => {
            // eslint-disable-next-line react/destructuring-assignment
            handleRemoveItem(props.data.id, props?.metadata?.sKey as string);
          }}
        />
      );
    default:
      return <p className={pStyle}>{value}</p>;
  }
}

export default SelectNetworkAppointmentsTable;
