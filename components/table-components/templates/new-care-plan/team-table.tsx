/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { StyledAvatar } from 'components/table-components/styled-components';
import { Teams } from 'types/teams-types';
import { getLatestImage } from 'utils/helper';
import ActionContainer from 'components/action-container/action-container';
import { TableRenderProps } from '../../render';

const pStyle = 'text-[13px]  p-0 m-0  text-[#001018] first-letter:uppercase';
let constants: string | boolean | null;
function TeamTable(props: TableRenderProps<Teams>) {
  const {
    data: {
      name, navigators, households, id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  const primaryNavigator = navigators?.find(
    (item) => item.roles?.includes('team_lead') || item.roles?.includes('role_admin'),
  );

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}

          <p className="font-[500] pr-1">{`${name} `}</p>
        </div>
      );
    case 'navigators':
      return (
        <div className="grid grid-flow-col auto-cols-min">
          {navigators?.map((item) => (
            <StyledAvatar
              style={{
                width: '30px',
                height: '30px',
              }}
              src={getLatestImage(item?.attachments, 'userProfile')}
              key={item.id}
            />
          ))}
        </div>
      );
    case 'primaryNavigator':
      return (
        <div className="flex items-center">
          {' '}
          <StyledAvatar
            style={{
              width: '30px',
              height: '30px',
            }}
            src={getLatestImage(
              primaryNavigator?.attachments || [],
              'userProfile',
            )}
            key={primaryNavigator?.id}
          />
          <p className={pStyle}>
            {primaryNavigator?.firstName}
            {' '}
            {primaryNavigator?.lastName}
          </p>
        </div>
      );
    case 'households':
      return <p className={pStyle}>{households?.length}</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Team"
          onDelete={() => handleDelete && handleDelete(id)}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default TeamTable;
