/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { capitalizeWords } from 'utils/helper';
import ActionContainer from 'components/action-container/action-container';
import { TableRenderProps } from '../../render';

const pStyle = 'first-letter:uppercase';
let constants: string | boolean | null;
function SelectDiagnosisTable(props: TableRenderProps<any>) {
  const {
    data: {
      title, id, theCode,
    },
    value,
    sKey,
    children,
  } = props;

  const handleRemoveItem = (id: string, hereTypes: string) => {
    const newList: any = (props?.metadata as any)?.list.map((item: any) => {
      if (item.type === hereTypes) {
        return {
          ...item,
          network: item.network.filter((x: any) => x.id !== id),
        };
      }
      return item;
    });
    // eslint-disable-next-line no-unused-expressions
    props?.metadata?.setSelected && props?.metadata?.setSelected(newList);
  };

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'title':
      return (
        <div className="grid grid-flow-col items-center justify-start relative">
          {children}
          <p>{title}</p>
        </div>
      );
    case 'description':
      return (
        <a href={id} target="_blank" rel="noreferrer">
          {id}
        </a>
      );
    case 'code':
      return (
        <p className="">
          {theCode}
        </p>
      );
    default:
      return <p className={pStyle}>{value}</p>;
  }
}

export default SelectDiagnosisTable;
