/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { StyledAvatar } from 'components/table-components/styled-components';
import ActionContainer from 'components/action-container/action-container';
import { useModal } from 'contexts/modal-context/modal-context';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import API from 'api/src/lib/api';
import { Teams } from 'types/teams-types';
import { getLatestImage } from 'utils/helper';
import { useState, useEffect } from 'react';
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px]  p-0 m-0  text-[#001018] first-letter:uppercase';
let constants: string | boolean | null;
function NavigatorTeamTable(props: TableRenderProps<Teams>) {
  const { showModal } = useModal();
  const { refresh } = useRefreshSSR();
  const {
    data: {
      name, navigators, households, role,
    },
    children,
    value,
    sKey,
    router,
  } = props;

  const [formattedRole, setFormattedRole] = useState('');

  const handleOnDelete = (team: Teams) => {
    showModal('Warning', 'Are you sure to delete this team?', async () => {
      await API.TEAMS.removeTeam({ teamId: team.id });
      refresh();
    });
  };

  const handleOnEdit = (team: Teams) => {
    router.push(`/teams/add-edit/${team.id}`);
  };

  useEffect(() => {
    if (role === 'role_admin') {
      setFormattedRole('Admin');
    }
  }, [role]);

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'title':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}

          <p
            onClick={() => {
              // eslint-disable-next-line react/destructuring-assignment
              handleOnEdit(props.data);
            }}
            className="font-[500] pr-1"
          >
            {`${name} `}
          </p>
        </div>
      );
    case 'role':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          {children}

          <p
            onClick={() => {
              // eslint-disable-next-line react/destructuring-assignment
              handleOnEdit(props.data);
            }}
            className="font-[500] pr-1"
          >
            {`${formattedRole} `}
          </p>
        </div>
      );

    case 'navigators':
      return (
        <div className="grid grid-flow-col auto-cols-min gap-1">
          {navigators?.map((item) => (
            <StyledAvatar
              style={{
                width: '24px',
                height: '24px',
              }}
              src={getLatestImage(item?.attachments, 'userProfile')}
              key={item.id}
            />
          ))}
        </div>
      );
    case 'households':
      return <p className={pStyle}>{households?.length}</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Team"
          editLabel="Edit Team"
          // onDelete={() => {
          //   // eslint-disable-next-line react/destructuring-assignment
          //   handleOnDelete(props.data);
          // }}
          onEdit={() => {
            // eslint-disable-next-line react/destructuring-assignment
            handleOnEdit(props.data);
          }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default NavigatorTeamTable;
