/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import { StyledAvatar } from 'components/table-components/styled-components';
import { Teams } from 'types/teams-types';
import router from 'next/router';
import ActionContainer from 'components/action-container/action-container';
import { TableRenderProps } from '../render';

const pStyle = 'text-[15px] font-[500] p-0 py-3 m-0  text-[#001018] first-letter:uppercase';
let constants: string | boolean | null;
function AddSectionTeamTable(props: TableRenderProps<Teams>) {
  const {
    data: {
      name, navigators, households, id,
    },
    children,
    value,
    sKey,
    handleDelete,
  } = props;

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'title':
      return (
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events
        <div
          className="grid grid-flow-col auto-cols-max items-center cursor-pointer"
          onClick={() => {
            router.push(`/teams/add-edit/${id}`);
          }}
        >
          {children}
          <p className={pStyle}>{name}</p>
        </div>
      );
    case 'navigators':
      return (
        <div className="grid grid-flow-col auto-cols-min gap-1">
          {navigators?.map((item) => (
            <StyledAvatar
              style={{
                width: '24px',
                height: '24px',
              }}
              src={item.profile}
              key={item.id}
            />
          ))}
        </div>
      );
    case 'households':
      return <p className={pStyle}>{households?.length}</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Remove from Team"
          onDelete={() => {
            // eslint-disable-next-line react/destructuring-assignment
            handleDelete?.(props.data);
          }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default AddSectionTeamTable;
