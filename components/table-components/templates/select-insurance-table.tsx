/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */

import { Carrier } from 'types/service-types';
import { TableRenderProps } from '../render';

function SelectInsuranceTable(props: TableRenderProps<any>) {
  const {
    data: { name, id, type },
    value,
    metadata,
    children,
    router,
    select,
    sKey,
  } = props;

  switch (sKey) {
    case 'name':
      return (
        // eslint-disable-next-line max-len
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
        <div
          // onClick={() => {
          // }}
          key={value}
          className="grid grid-flow-col cursor-pointer items-center justify-start relative"
        >
          {children}

          <p className="font-[500] pr-1">{`${name}`}</p>
        </div>
      );
    case 'type':
      return (
        <div
          key={value}
          className="grid grid-flow-col cursor-pointer items-center justify-start relative"
        >
          <p className="font-[500] pr-1">{`${type || ''}`}</p>
        </div>
      );
    // case 'actions':
    //   return (
    //     <ActionContainer
    //       deleteLabel="Delete Member"
    //       editLabel="Edit Member"
    //       onDelete={() => {
    //         // eslint-disable-next-line react/destructuring-assignment
    //         handleDeleteMember(props.data);
    //       }}
    //       onEdit={() => {
    //         // eslint-disable-next-line react/destructuring-assignment
    //         handleEdit(props.data);
    //       }}
    //     />
    //   );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default SelectInsuranceTable;
