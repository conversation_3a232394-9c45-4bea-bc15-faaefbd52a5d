/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import API from 'api/src/lib/api';
import { useModal } from 'contexts/modal-context/modal-context';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import UpdateNetworkStatus from 'components/members/social-plans/update-status';
import { useState } from 'react';
import Cookies from 'js-cookie';
import { TableRenderProps } from '../render';

const pStyle = 'text-[15px]  p-0 m-0  text-[#262D2D] first-letter:uppercase';
let constants: string | boolean | null;
function SocialPlanNetworkItemTable(props: TableRenderProps<any>) {
  const {
    data: {
      network, status, desc, carepackage, id,
    },
    value,
    sKey,
  } = props;
  const { showModal } = useModal();
  const { refresh } = useRefreshSSR();
  const [openUpdateStatus, setOpenUpdateStatus] = useState(false);
  const [selectedItem, setSelectedItem] = useState<any>(null);
  const handleDeleteNetwork = () => {
    showModal(
      'Warning',
      'Are you sure you want to remove this network from the social plan?',
      async () => {
        await API.SOCIALPLANS.removeItemSocialPlan(
          carepackage?.id as string,
          id as string,
        );
        refresh();
      },
    );
  };

  const handleOnEdit = (item: any) => {
    setSelectedItem(item);
    setOpenUpdateStatus(true);
  };

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }

  return (
    <>
      {openUpdateStatus && selectedItem && (
        <UpdateNetworkStatus
          socialPlanId={selectedItem.carepackage.id}
          navigatorId={Cookies.get('userID') as string}
          network={selectedItem}
          open={openUpdateStatus}
          refresh={refresh}
          setOpen={setOpenUpdateStatus}
        />
      )}

      {(() => {
        switch (sKey) {
          case 'title':
            return <p className={pStyle}>{network?.name || '-'}</p>;
          case 'status':
            return <p className={pStyle}>{status || '-'}</p>;
          case 'statusNote':
            return <p className={pStyle}>{desc || '-'}</p>;
          case 'actions':
            return (
              <ActionContainer
                deleteLabel="Remove From Social Plan"
                editLabel="Update Status"
                onDelete={handleDeleteNetwork}
                onEdit={() => handleOnEdit(props.data)}
              />
            );
          default:
            return <p className={pStyle}>{value}</p>;
        }
      })()}
    </>
  );
}

export default SocialPlanNetworkItemTable;
