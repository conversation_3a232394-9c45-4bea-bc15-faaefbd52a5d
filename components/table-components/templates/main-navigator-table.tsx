/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */

import { StyledAvatar } from 'components/table-components/styled-components';
import ActionContainer from 'components/action-container/action-container';
import { useModal } from 'contexts/modal-context/modal-context';
import API from 'api/src/lib/api';

import useRefreshSSR from 'hooks/use-refresh-ssr';
import { Navigator } from 'types/navigators-types';
import { getLatestImage } from 'utils/helper';
import { TableRenderProps } from '../render';

const pStyle = 'text-[14px]   text-[#001018] first-letter:uppercase';
let constants: string | boolean | null;

function MainNavigatorRender(props:TableRenderProps<Navigator>) {
  const { showModal } = useModal();
  const { refresh } = useRefreshSSR();
  const handleDeleteMember = (item: Navigator) => {
    showModal('Warning', 'Are you sure you want to delete this Navigator?', async () => {
      await API.USER.delete(item.id);
      await refresh();
    });
  };

  const handleEdit = (item: Navigator) => {
    // eslint-disable-next-line no-use-before-define
    router.push(`/navigators/add-edit/${item.id}`);
  };
  const {
    data: {
      firstName, id, lastName, attachments, teams,
    },
    value,
    metadata,
    children,
    router,
    select,
    sKey,
  } = props;

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'name':
      // eslint-disable-next-line no-case-declarations

      const profile = getLatestImage(attachments, 'userProfile');
      return (
        // eslint-disable-next-line max-len
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
        <div
          onClick={() => {
            // eslint-disable-next-line no-unused-expressions
            router && !select && router.push(`/navigators/${id}`);
          }}
          key={value}
          className="grid grid-flow-col cursor-pointer items-center justify-start relative"
        >
          {metadata?.headId === id && <div className="absolute left-4 top-1 z-10 bg-icon-hoh w-5 h-5 bg-[length:15px_15px] bg-no-repeat" />}
          {children}
          <StyledAvatar className="w-[28px] h-[28px]" alt={firstName} src={profile || ''} />
          <p className={`${pStyle} font-[500] pr-1`}>{`${firstName} `}</p>
          <p className={`${pStyle}  font-[500]`}>{`${lastName} `}</p>
        </div>
      );
    case 'gender':
      return <p key={value} className={pStyle}>{value || 'N/A'}</p>;
    case 'teams':
      return (
        <div className="grid grid-flow-col gap-1 auto-cols-max">
          {teams && teams?.length > 0 ? teams?.map((team) => (
            <p className={pStyle}>
              {team?.name?.length > 0 ? (
                <span className="underline">
                  {team.name}
                  ,
                </span>
              )
                : <span className="text-[15px] text-[#DE6F09] first-letter:uppercase">Unassigned</span>}
            </p>
          )) : <p className="text-[15px] text-[#DE6F09] first-letter:uppercase">Unassigned</p>}
        </div>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Delete Navigator"
          editLabel="Edit Navigator"
          onDelete={() => {
            // eslint-disable-next-line react/destructuring-assignment
            handleDeleteMember(props.data);
          }}
          onEdit={() => {
            // eslint-disable-next-line react/destructuring-assignment
            handleEdit(props.data);
          }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default MainNavigatorRender;
