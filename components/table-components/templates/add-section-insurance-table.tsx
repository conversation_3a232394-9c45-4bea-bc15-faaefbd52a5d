/* eslint-disable no-nested-ternary */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */

import ActionContainer from 'components/action-container/action-container';
import { Carrier } from 'types/service-types';
import { capitalizeWords } from 'utils/helper';
import { TableRenderProps } from '../render';

function AddSectionInsuranceTable(props: TableRenderProps<any>) {
  const deleteInsurance = (item: any) => {
    if (props.handleDelete) {
      props.handleDelete(item);
    }
  };
  const {
    data: { name, type },
    value,
    sKey,
  } = props;

  switch (sKey) {
    case 'name':
      return (
        <div
          key={value}
          className="grid grid-flow-col cursor-pointer items-center justify-start"
        >
          <p className="font-[500] pr-1">
            {capitalizeWords(`${name}`, true)}
          </p>
        </div>
      );
    case 'type':
      return (
        <div
          key={value}
          className="grid grid-flow-col  cursor-pointer  items-center justify-start"
        >
          <p className="font-[500] pr-1">
            {capitalizeWords(`${type || ''}`, true)}
          </p>
        </div>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Remove Insurance"
          editLabel="Edit Service"
          onDelete={() => {
            // eslint-disable-next-line react/destructuring-assignment
            deleteInsurance(props.data);
          }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default AddSectionInsuranceTable;
