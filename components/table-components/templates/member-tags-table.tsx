/* eslint-disable max-len */
/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */
import ActionContainer from 'components/action-container/action-container';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { MemberTags } from 'types/members-response-type';
import SafeDate from 'components/date-time/SafeDate';
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px]  p-0 m-0  text-[#001018] first-letter:uppercase';
function MemberTagsTable(props: TableRenderProps<MemberTags>) {
  const {
    data: {
      name, color, updatedAt, id,
    },
    children,
    value,
    sKey,
    router,
    handleDelete,
  } = props;
  const { refresh } = useRefreshSSR();
  // const handleOnEdit = (team: Teams) => {
  //   router.push(`/teams/add-edit/${team.id}`);
  // };

  switch (sKey) {
    case 'title':
      return (
        <div
          key={name}
          className="grid grid-flow-col cursor-pointer items-center justify-start w-full"
        >
          {children}
          <div className="w-3 h-3 rounded-full mr-3" style={{ backgroundColor: `${color}` }} />
          <p className="font-[500] pr-1 text-[15px] truncate">{name}</p>
        </div>
      );
    case 'updatedAt':
      return <p className={pStyle}><SafeDate date={updatedAt} /></p>;
    case 'actions':
      return (
        <div className="flex justify-center items-center w-[50px]">
          <ActionContainer
            deleteLabel="Remove Tag"
            onDelete={handleDelete ? () => handleDelete({ id, name }) : undefined}
            editLabel=""
          />
        </div>
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default MemberTagsTable;
