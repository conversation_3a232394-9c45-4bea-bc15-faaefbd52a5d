/* eslint-disable no-nested-ternary */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable no-case-declarations */
// eslint-disable-next-line import/no-cycle
import {
  capitalizeWords, getKeyValue, getLatestImage,
} from 'utils/helper';
import SafeDate from 'components/date-time/SafeDate';
// eslint-disable-next-line import/no-cycle

import { Member } from 'components/members/members-table/type';
import { StyledAvatar } from 'components/table-components/styled-components';
import ActionContainer from 'components/action-container/action-container';
import API from 'api/src/lib/api';
// eslint-disable-next-line import/no-cycle
import { TableRenderProps } from '../render';

const pStyle = 'text-[15px]  py-2 m-0  text-[#001018] first-letter:uppercase';
let constants: string | boolean | null;
function AddSectionMemberTable(props:TableRenderProps<Member>) {
  const handleHeadOfHousehold = async (item: Member) => {
    if (props.metadata?.householdId) {
      await API.HOUSEHOLDS.setHeadOfHousehold({
        orgID: item.org.id,
        headOfHouseID: item.id,
      }, props.metadata?.householdId);
      window.location.reload();
    }
  };

  const handleIsModalSelected = (item: Member) => {
    if (props.metadata?.setSelected) {
      props.metadata?.setSelected((prev) => prev.filter((items) => items.id !== item.id));
    }
    return false;
  };

  const {
    data: {
      firstName, id, lastName, attachments, households,
    },
    value,
    getScoreTitle,
    metadata,
    children,
    router,
    select,
    sKey,
  } = props;

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'firstName':
      // eslint-disable-next-line no-case-declarations
      const profile = getLatestImage(attachments as any, 'profile');
      return (
        // eslint-disable-next-line max-len
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
        <div
          onClick={() => {
            // eslint-disable-next-line no-unused-expressions
            router && !select && router.push(`/members/profile/${id}`);
          }}
          key={value}
          className="grid grid-flow-col cursor-pointer p-1 items-center justify-start relative"
        >
          {metadata?.headId === id && <div className="absolute left-4 top-0 z-10 bg-icon-hoh w-5 h-5 bg-[length:12px_12px] bg-no-repeat" />}
          {children}
          <StyledAvatar className="w-[32px] h-[32px]" alt={firstName} src={profile || ''} />
          <p className={`${pStyle} font-[500] pr-1`}>{`${firstName} `}</p>
          <p className={`${pStyle}  font-[500]`}>{`${lastName} `}</p>
        </div>
      );
    case 'dob':
      return <p key={value} className={pStyle}><SafeDate date={value as string} /></p>;
    case 'household':
      return (
        <p key={value} className={`${pStyle}`}>
          {metadata?.householdTitle ? metadata?.householdTitle
            : households ? `${households[0]?.title || 'N/A'}` : 'N/A'}
        </p>
      );
    case 'teams':
      return (
        <div key={value} className="grid grid-flow-col gap-1">
          {households?.length > 0 ? households[0]?.teams?.map((team) => (
            <p className={pStyle}>
              {team?.name?.length > 0 ? <span className="underline">{team.name}</span>
                : <span className="text-[14px] text-[#DE6F09] first-letter:uppercase">Unassigned</span>}
            </p>
          )) : <p className="text-[14px] text-[#DE6F09] first-letter:uppercase">Unassigned</p>}
        </div>
      );
    case 'genderIdentity':
      return (
        <p key={value} className="first-letter:uppercase p-0 m-0">
          {capitalizeWords(getKeyValue(value, 'genderIndentity', constants), true)}
        </p>
      );
    case 'ethnicity':
      return <p key={value} className={pStyle}>{!value ? '-' : capitalizeWords(value)}</p>;
    case 'score':
      return <p key={value} className={`${pStyle} text-[${getScoreTitle(value as number)?.color}]`}>{getScoreTitle(value as number)?.title}</p>;
    case 'type':
      return <p key={value} className={pStyle}>{getKeyValue(value, 'memberTypes', constants)}</p>;
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Remove Member"
          editLabel="Edit Member"
          makeHead
          onAction={() => {
            handleHeadOfHousehold(props.data);
          }}
          onDelete={() => {
            handleIsModalSelected(props.data);
          }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default AddSectionMemberTable;
