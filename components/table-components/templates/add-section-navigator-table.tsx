/* eslint-disable no-nested-ternary */
/* eslint-disable react/destructuring-assignment */
/* eslint-disable import/no-cycle */
/* eslint-disable no-case-declarations */

import { StyledAvatar } from 'components/table-components/styled-components';
import ActionContainer from 'components/action-container/action-container';
import { Navigator } from 'types/navigators-types';
import { getLatestImage } from 'utils/helper';
import { TableRenderProps } from '../render';

const pStyle = 'text-[13px]   text-[#001018] first-letter:uppercase';
let constants: string | boolean | null;

function AddSectionNavigatorTable(props:TableRenderProps<Navigator>) {
  const handleIsModalSelected = (item: Navigator) => {
    if (props.metadata?.setSelected) {
      props.metadata?.setSelected((prev) => prev.filter((items) => items.id !== item.id));
    }
    return false;
  };
  const {
    data: {
      firstName, id, lastName, attachments, teams,
    },
    value,
    metadata,
    children,
    router,
    select,
    sKey,
  } = props;

  if (!constants && typeof window !== 'undefined') {
    const storage = window && window?.sessionStorage.getItem('constants');
    if (storage) {
      constants = JSON.parse(storage || '');
    } else {
      constants = null;
    }
  }
  switch (sKey) {
    case 'name':
      // eslint-disable-next-line no-case-declarations
      const profile = getLatestImage(attachments, 'userProfile');
      return (
        // eslint-disable-next-line max-len
        // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
        <div
          onClick={() => {
            // eslint-disable-next-line no-unused-expressions
            router && !select && router.push(`/navigators/${id}`);
          }}
          key={value}
          className="grid grid-flow-col cursor-pointer items-center justify-start relative"
        >
          {metadata?.headId === id && <div className="absolute left-4 top-1 z-10 bg-icon-hoh w-5 h-5 bg-[length:15px_15px] bg-no-repeat" />}
          {children}
          <StyledAvatar className="w-[24px] h-[24px]" alt={firstName} src={profile || ''} />
          <p className={`${pStyle} font-[500] pr-1`}>{`${firstName} `}</p>
          <p className={`${pStyle}  font-[500]`}>{`${lastName} `}</p>
        </div>
      );
    case 'gender':
      return <p key={value} className={pStyle}>{value || 'N/A'}</p>;
    case 'teams':
      return (
        <div className="grid grid-flow-col gap-1 auto-cols-max">
          {metadata?.householdTitle ? metadata?.householdTitle
            : teams && teams?.length > 0 ? teams?.map((team) => (
              <p className={pStyle}>
                {team?.name?.length > 0 ? (
                  <span className="underline">
                    {team.name}
                    ,
                  </span>
                )
                  : <span className="text-[15px] text-[#DE6F09] first-letter:uppercase">Unassigned</span>}
              </p>
            )) : <p className="text-[15px] text-[#DE6F09] first-letter:uppercase">Unassigned</p>}
        </div>
      );
    case 'actions':
      return (
        <ActionContainer
          deleteLabel="Remove Navigator"
          editLabel="Edit Navigator"
          onDelete={() => {
            // eslint-disable-next-line react/destructuring-assignment
            handleIsModalSelected(props.data);
          }}
        />
      );
    default:
      return Array.isArray(value) ? value.join(', ') : value;
  }
}

export default AddSectionNavigatorTable;
