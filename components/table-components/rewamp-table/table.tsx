import { Grid } from 'gridjs-react';

function RewampTable({ columns, data }: any) {
  return (
    <Grid
      data={data}
      columns={columns}
      pagination={data?.length > 10 ? {
        limit: 10,
        nextButton: false,
        prevButton: false,
        summary: false,
      } : false}
      className={{
        table: 'border-none border-collapse',
        thead: 'border-[0.3px] border-gray-300 text-xs text-left border-solid border-r-0 border-l-0 p-2',
        tbody: 'text-xs font-semibold',
        th: 'font-[500] font-[Inter] first-letter:uppercase p-2',
        td: 'first-letter:uppercase p-2',
      }}
    />
  );
}

export default RewampTable;
