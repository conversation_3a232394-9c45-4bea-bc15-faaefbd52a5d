/* eslint-disable react/jsx-one-expression-per-line */
import { _ } from 'gridjs-react';

function SortIcon() {
  return (
    <svg width="12" height="13" viewBox="0 0 12 13" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path d="M8.5 4.5L6 2L3.5 4.5M8.5 8.5L6 11L3.5 8.5" stroke="#747A7A" strokeLinecap="round" strokeLinejoin="round" />
    </svg>
  );
}

const FullReportColumns = (totalValue = 0) => [
  {
    id: 'name',
    width: 900,
    name: _(<div className="flex flex-row gap-1 items-center"> Title <SortIcon /> </div>),
    sort: {
      enabled: true,
    },
  },
  {
    id: 'value',
    width: '40%',

    name: _(<div className="flex flex-row gap-1 items-center"> Total Count <SortIcon /> </div>),
    sort: {
      enabled: true,
    },
    formatter: (cell:any) => _(<i className="font-normal">{cell}</i>),
  },
  {
    id: 'percentage',
    name: _(<div className="flex flex-row gap-1 items-center">  Percentage of Total  <SortIcon /> </div>),
    width: '40%',
    sort: {
      enabled: true,
    },
    formatter: (cell:any, row:any) => {
      const value = row.cells[1]?.data; // Get the value from the 'value' column
      const percentage = ((value / totalValue) * 100).toFixed(2);
      return _(<span className="font-normal">{percentage}%</span>);
    },
  },
];
export default FullReportColumns;
