// styleVariants.js or at the top of your component file
const mainStyle = {
  whiteSpace: 'nowrap',
  backgroundColor: '#ffffff', // Example color
  color: '#646F79',
  border: 'none',
  boxShadow: 'none',
  fontWeight: '500',
  fontSize: '13px',
  fontStyle: 'normal',
  textTransform: 'capitalize',
  cursor: 'pointer',
  // padding: '16px 13px', // Example padding
};

export const cellStyles = {
  'main-household': {
    ...mainStyle,
    padding: '16px 13px', // Example padding
  },
  'main-team-table': {
    ...mainStyle,
    padding: '15px 13px', // Example padding
  },
  'main-network-table': {
    ...mainStyle,
    padding: '15px 13px', // Example padding
  },
  'main-navigator-table': {
    ...mainStyle,
    padding: '15px 13px', // Example padding
  },
  'main-member-table': {
    ...mainStyle,
    // padding: '16px 32px', // Example padding
  },
  'addsection-member-table': {
    ...mainStyle,
    padding: '4px 11px', // Example padding
    // padding: '16px 32px', // Example padding
  },
  'addsection-team-table': {
    ...mainStyle,
    padding: '16px 11px', // Example padding
    // padding: '16px 32px', // Example padding
  },
  'report-full-data': {
    ...mainStyle,
    padding: '4px 11px', // Example padding
    // padding: '16px 32px', // Example padding
  },
  'addsection-navigator-table': {
    ...mainStyle,
    padding: '4px 11px', // Example padding
    // padding: '16px 32px', // Example padding
  },
  'addsection-network-table': {
    ...mainStyle,
    padding: '4px 11px', // Example padding
    // padding: '16px 32px', // Example padding
  },
  'social-plan-network-table': {
    ...mainStyle,
    padding: '4px 11px', // Example padding
    // padding: '16px 32px', // Example padding
  },
  'social-plan-view-network-table': {
    ...mainStyle,
    padding: '4px 11px', // Example padding
    // padding: '16px 32px', // Example padding
  },
  'main-household-body': {
    padding: '0px 16px',
    border: 'none',
  },
  'main-team-table-body': {
    padding: '0px 16px',
    border: 'none',
  },
  'social-plan-network-table-body': {
    padding: '8px 11px',
    fontSize: '13px',
    border: 'none',
    borderBottom: '1px solid #E5E5E5',
  },
  'social-plan-view-network-table-body': {
    padding: '8px 11px',
    fontSize: '13px',
    border: 'none',
    borderBottom: '1px solid #E5E5E5',
  },
  'main-network-table-body': {
    padding: '0px 16px',
    border: 'none',
  },
  'main-navigator-table-body': {
    padding: '0px 16px',
    border: 'none',
  },
  'main-member-table-body': {
    padding: '0px 16px',
    border: 'none',
  },
  'addsection-member-table-body': {
    padding: '0px 11px',
    fontSize: '13px',
    border: 'none',
    borderBottom: '1px solid #E5E5E5',
  },
  'addsection-team-table-body': {
    padding: '5px 11px',
    fontSize: '13px',
    border: 'none',
    borderBottom: '1px solid #E5E5E5',
  },
  'addsection-navigator-table-body': {
    padding: '0px 11px',
    fontSize: '13px',
    border: 'none',
    borderBottom: '1px solid #E5E5E5',
  },
  'addsection-service-table-body': {
    padding: '0px 16px',
    fontSize: '13px',
    border: 'none',
    borderBottom: '1px solid #E5E5E5',
  },
  // More cell variants...
} as any;

export const rowStyles = {
  'main-member-table': {
    border: 'none',
  },
  default: {
    border: 'none',
    // Default row styles...
  },
  // More row variants...
} as any;

export const avatarStyles = {
  small: {
    width: 24,
    height: 24,
    marginRight: 8,
  },
  large: {
    width: 40,
    height: 40,
    marginRight: 8,
  },
  // More avatar variants...
} as any;
