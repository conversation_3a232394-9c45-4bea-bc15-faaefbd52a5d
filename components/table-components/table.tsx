/* eslint-disable no-unused-vars */
/* eslint-disable import/named */
import React, { useState, useEffect } from 'react';
import Table from '@mui/material/Table';
import TableBody from '@mui/material/TableBody';
import TableContainer from '@mui/material/TableContainer';
import TableHead from '@mui/material/TableHead';
import Paper from '@mui/material/Paper';
import { Checkbox, Pagination } from '@mui/material';
import UnfoldMoreIcon from '@mui/icons-material/UnfoldMore';

import useScore from 'hooks/use-score';
import { useRouter } from 'next/router';
import {
  StyledTableCell,
  StyledTableRow,
  borderlessTable,
  borderlessTableContainer,
} from 'components/table-components/styled-components';
import { TableMetadata, TableRender } from 'components/table-components/render';
import { columnsRender } from 'components/table-components/columns-renders';

interface PageInfo {
  per: number;
  total: number;
  page: number;
}

interface CustomTableProps {
  source: any[];
  select?: boolean;
  extra?: TableMetadata;
  variant: string;
  selected?: any[];
  metadata?: TableMetadata;
  setSelected?: (item: any) => void;
  style?: object;
  headCellStyle?: object;
  tableCellStyle?: object;
  handleDelete?: (item: any) => void;
  handleEdit?: (item: any) => void;
  firstColumnStaticWidth?: number;
  secondColumnStaticWidth?: number;
  thirdColumnStaticWidth?: number;
  fourthColumnStaticWidth?: number;
  fifthColumnStaticWidth?: number;
  pageInfo?: PageInfo;
  onPageChange?: (newPage: number) => void;
  sortBy?: string;
  sortDirection?: 'asc' | 'desc';
  onSortChange?: (sortBy: string, sortDirection: 'asc' | 'desc') => void;
  className?: string;
  fixedLayout?: boolean;
  rowIdField?: string; // Optional override for row identifier field (e.g., 'rxcui')
}

function CustomTable({
  select = false,
  variant,
  source,
  extra,
  selected,
  setSelected,
  metadata,
  style,
  pageInfo,
  headCellStyle,
  tableCellStyle,
  handleDelete,
  handleEdit,
  onPageChange,
  firstColumnStaticWidth,
  secondColumnStaticWidth,
  thirdColumnStaticWidth,
  fourthColumnStaticWidth,
  fifthColumnStaticWidth,
  sortBy,
  sortDirection,
  onSortChange,
  className,
  fixedLayout = false,
  rowIdField = 'id',
}: CustomTableProps) {
  const router = useRouter();
  const [sortedData, setSortedData] = useState<any[]>([]);
  const [config, setConfig] = useState<any[]>([]);
  const [sortingDirection, setSortingDirection] = useState<
    'asc' | 'desc' | null
  >(sortDirection || null);
  const [sortedColumn, setSortedColumn] = useState<string | null>(
    sortBy || null,
  );
  const page = pageInfo?.page || 1;
  const rowsPerPage = pageInfo?.per || 10;
  const totalItems = pageInfo?.total !== undefined ? pageInfo.total : sortedData.length;
  const { getScoreTitle } = useScore();

  useEffect(() => {
    if (typeof window !== 'undefined') {
      // Access localStorage only on the client side
      // eslint-disable-next-line no-underscore-dangle
      const _config = window.localStorage.getItem('member-config');
      setConfig(JSON.parse(_config || '[]'));
    }
  }, [variant]);

  // Initialize sortedData when source changes
  useEffect(() => {
    if (Array.isArray(source)) {
      setSortedData([...source]); // safe copy
    } else {
      setSortedData([]); // fallback to empty
    }
  }, [source]);

  // Handle sorting
  const handleHeadClick = (property: string) => {
    const newDirection = sortedColumn === property && sortingDirection === 'asc' ? 'desc' : 'asc';

    setSortedColumn(property);
    setSortingDirection(newDirection);
    onSortChange?.(property, newDirection);
  };

  const handleSelected = (item: any) => {
    if (metadata?.setSelected) {
      metadata.setSelected((prev) => {
        const itemExists = prev.some((x) => x[rowIdField] === item[rowIdField]);

        // Remove the item if it already exists in the selection
        if (itemExists) {
          return prev.filter((items) => items[rowIdField] !== item[rowIdField]);
        }

        // Add the item only if multi-select is allowed or no items are selected yet
        if (!metadata.disableMultiSelect || prev.length === 0) {
          return [...prev, item];
        }

        // If multi-select is disabled and there is already an item, replace it
        return [item];
      });
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function handleRowClick(item: any) {
    if (variant === 'navigator-team-table') {
      router.push(`/teams/add-edit/${item.id}`);
    }
    if (variant === 'chat-participant-table') {
      router.push(`/navigators/${item.id}`);
    }
    // if (variant === 'member-insurances-table') {
    //   router.push(`/members/insurance/${item.id}`);
    // // ---- need to get member id from the table, item.id is the insurance id
    // }
    // if (variant === 'social-plan-network-table') {
    //   router.push(`/members/social-plans/${item.id}`);
    // }
  }

  return (
    <>
      <TableContainer
        component={Paper}
        sx={borderlessTableContainer(variant) as any}
        style={style}
        className={className}
      >
        <Table
          aria-label="table"
          sx={{
            ...(borderlessTable(variant) as any),
            ...(fixedLayout && { tableLayout: 'fixed' }), // key step
            width: '100%',
          }}
        >
          <TableHead>
            <StyledTableRow variant={variant}>
              {columnsRender(variant, config).map(
                ({ key, value }: any, columnIndex: number) => (
                  <StyledTableCell
                    key={key}
                    cellType={variant}
                    onClick={() => handleHeadClick(key)}
                    style={{
                      ...headCellStyle,
                      ...(fixedLayout
                        && columnIndex === 0
                        && firstColumnStaticWidth && {
                        width: `${firstColumnStaticWidth}%`,
                      }),
                      ...(fixedLayout
                        && columnIndex === 1
                        && secondColumnStaticWidth && {
                        width: `${secondColumnStaticWidth}%`,
                      }),
                      ...(fixedLayout
                        && columnIndex === 2
                        && thirdColumnStaticWidth && {
                        width: `${thirdColumnStaticWidth}%`,
                      }),
                      ...(fixedLayout
                        && columnIndex === 3
                        && fourthColumnStaticWidth && {
                        width: `${fourthColumnStaticWidth}%`,
                      }),
                      ...(fixedLayout
                        && columnIndex === 4
                        && fifthColumnStaticWidth && {
                        width: `${fifthColumnStaticWidth}%`,
                      }),
                    }}
                    className={`${key === 'actions' && 'w-[50px]'} cursor-pointer`}
                  >
                    <div className="grid grid-flow-col items-center auto-cols-min whitespace-nowrap">
                      {value}
                      {key !== 'actions' && (
                        <UnfoldMoreIcon style={{ fontSize: 14 }} />
                      )}
                    </div>
                  </StyledTableCell>
                ),
              )}
            </StyledTableRow>
          </TableHead>
          <TableBody>
            {sortedData.map((item, index) => (
              <StyledTableRow
                variant={variant}
                // eslint-disable-next-line react/no-array-index-key
                key={`${item.id}-${index}`}
                onClick={() => handleRowClick(item)}
              >
                {columnsRender(variant, config).map(
                  ({ key }: any, columnIndex: number) => {
                    const value = item[key as keyof typeof item];
                    return (
                      <StyledTableCell
                        key={key}
                        cellType={`${variant}-body`}
                        style={{
                          ...tableCellStyle,
                          ...(select
                            && columnIndex === 0 && { paddingLeft: '4px' }),
                          ...(firstColumnStaticWidth
                            && columnIndex === 0 && {
                            width: `${firstColumnStaticWidth}%`,
                          }),
                          ...(secondColumnStaticWidth
                            && columnIndex === 1 && {
                            width: `${secondColumnStaticWidth}%`,
                          }),
                          ...(thirdColumnStaticWidth
                            && columnIndex === 2 && {
                            width: `${thirdColumnStaticWidth}%`,
                          }),
                          ...(fourthColumnStaticWidth
                            && columnIndex === 3 && {
                            width: `${fourthColumnStaticWidth}%`,
                          }),
                          ...(fifthColumnStaticWidth
                            && columnIndex === 4 && {
                            width: `${fifthColumnStaticWidth}%`,
                          }),
                          ...(fixedLayout && {
                            whiteSpace: 'nowrap',
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                          }),
                        }}
                      >
                        {TableRender({
                          sKey: key,
                          value: Array.isArray(value) ? '' : value,
                          data: item,
                          getScoreTitle,
                          variant,
                          router,
                          metadata,
                          select,
                          handleDelete,
                          children: select && (
                            <Checkbox
                              checked={selected?.some((x) => x[rowIdField] === item[rowIdField])}
                              onChange={() => handleSelected(item)}
                            />
                          ),
                        })}
                      </StyledTableCell>
                    );
                  },
                )}
              </StyledTableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {totalItems > rowsPerPage && (
        <Pagination
          count={Math.ceil(totalItems / rowsPerPage)}
          page={page}
          onChange={(_, value) => onPageChange?.(value)}
          className="pt-5 align-middle"
        />
      )}
    </>
  );
}

export default CustomTable;
