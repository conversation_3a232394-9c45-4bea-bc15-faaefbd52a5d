/* eslint-disable import/no-cycle */
/* eslint-disable react/jsx-props-no-spreading */
// eslint-disable-next-line import/no-cycle
import MainHouseholdRender from 'components/table-components/templates/main-household-table';
import AddSectionMemberTable from 'components/table-components/templates/add-section-member';
import AddSectionTeamTable from 'components/table-components/templates/add-section-team';
import React from 'react';
import MainTeamTable from 'components/table-components/templates/main-team-table';
import MainNavigatorRender from 'components/table-components/templates/main-navigator-table';
import AddSectionNavigatorTable from 'components/table-components/templates/add-section-navigator-table';
import MainNetworkTable from 'components/table-components/templates/main-network-table';
import SocialPlanNetworkTable from 'components/table-components/templates/social-plan-network-table';
import AddSectionServiceTable from 'components/table-components/templates/add-section-service-table';
import ReportFullDataTable from 'components/table-components/templates/report-full-data';
import NavigatorTeamTable from 'components/table-components/templates/navigator-team-table';
import MemberTagsTable from 'components/table-components/templates/member-tags-table';
import AddressTable from 'components/table-components/templates/address-table';
import MainMemberRender from './templates/main-member-table';
import ChatParticipantTable from './templates/chat-participant-table';
import SelectInsuranceTable from './templates/select-insurance-table';
import AddSectionInsuranceTable from './templates/add-section-insurance-table';
import AddNavigatorTeamTable from './templates/add-navigator-team-table';
import NavigatorTeamTableAddUpdate from './templates/navigator-team-table-add-update';
import MemberInsurancesTable from './templates/member-insurances-table';
import SocialPlanTable from './templates/social-plan-table';
import SelectNetworkTable from './templates/select-network-table';
import SocialPlanNetworkItemTable from './templates/social-plan-network-item-table';
import ConsentsTable from './templates/consents-table';
import CarePlansTable from './templates/care-plans-table';
import GoalsTable from './templates/new-care-plan/goals-table';
import InterventionsTable from './templates/new-care-plan/interventions-table';
import ReviewsTable from './templates/new-care-plan/reviews-table';
import FollowUpsTable from './templates/new-care-plan/follow-ups-table';
import NotesTable from './templates/new-care-plan/notes-table';
import TeamTable from './templates/new-care-plan/team-table';
import ProblemsTable from './templates/new-care-plan/problems-table';
import NetworkAppointmentsTable from './templates/new-care-plan/network-appointments-table';
import SelectNetworkAppointmentsTable from './templates/new-care-plan/select-network-appointments-table';
import SelectDiagnosisTable from './templates/new-care-plan/select-diagnosis-table';
import SelectMedicationTable from './templates/new-care-plan/select-medication-table';
import ProfileDiagnosesTable from './templates/profile/diagnoses-table';
import ProfileMedicationsTable from './templates/profile/medications-table';
import ProfileProblemsTable from './templates/profile/problems-table';
import InsurancesTable from './templates/profile/insurances-table';
import ContactsTable from './templates/profile/contacts-table';
import TeamsTable from './templates/profile/teams-table';

export interface TableMetadata {
  householdId?: string;
  householdTitle?: string;
  sKey?: string;
  headId?: string;
  disableMultiSelect?: boolean;
  list?: any[];
  setSelected?: React.Dispatch<React.SetStateAction<any[]>>;
}

export interface TableRenderProps<T> {
  data: T;
  getScoreTitle: any;
  value: any;
  sKey: any;
  variant: string;
  router?: any;
  select?: boolean;
  metadata?: TableMetadata;
  children?: any;
  handleDelete?: (item: any, type?: string) => void;
}

export function TableRender(props: TableRenderProps<any>) {
  // eslint-disable-next-line react/destructuring-assignment
  switch (props.variant) {
    case 'main-household':
      return <MainHouseholdRender {...props} />;
    case 'report-full-data':
      return <ReportFullDataTable {...props} />;
    case 'main-member-table':
      return <MainMemberRender {...props} />;
    case 'addsection-member-table':
      return <AddSectionMemberTable {...props} />;
    case 'addsection-team-table':
      return <AddSectionTeamTable {...props} />;
    case 'main-team-table':
      return <MainTeamTable {...props} />;
    case 'main-navigator-table':
      return <MainNavigatorRender {...props} />;
    case 'main-network-table':
      return <MainNetworkTable {...props} />;
    case 'addsection-navigator-table':
      return <AddSectionNavigatorTable {...props} />;
    case 'addsection-service-table':
      return <AddSectionServiceTable {...props} />;
    case 'social-plan-network-table':
      return <SocialPlanNetworkTable {...props} />;
    case 'navigator-team-table':
      return <NavigatorTeamTable {...props} />;
    case 'chat-participant-table':
      return <ChatParticipantTable {...props} />;
    case 'member-tags-table':
      return <MemberTagsTable {...props} />;
    case 'select-insurance-table':
      return <SelectInsuranceTable {...props} />;
    case 'addsection-insurance-table':
      return <AddSectionInsuranceTable {...props} />;
    case 'add-navigator-team-table':
      return <AddNavigatorTeamTable {...props} />;
    case 'navigator-team-table-add-update':
      return <NavigatorTeamTableAddUpdate {...props} />;
    case 'member-insurances-table':
      return <MemberInsurancesTable {...props} />;
    case 'social-plan-table':
      return <SocialPlanTable {...props} />;
    case 'select-network-table':
      return <SelectNetworkTable {...props} />;
    case 'social-plan-network-item-table':
      return <SocialPlanNetworkItemTable {...props} />;
    case 'address-table':
      return <AddressTable {...props} />;
    case 'consents-table':
      return <ConsentsTable {...props} />;
    case 'care-plans-table':
      return <CarePlansTable {...props} />;
    case 'goals-table':
      return <GoalsTable {...props} />;
    case 'interventions-table':
      return <InterventionsTable {...props} />;
    case 'reviews-table':
      return <ReviewsTable {...props} />;
    case 'follow-ups-table':
      return <FollowUpsTable {...props} />;
    case 'notes-table':
      return <NotesTable {...props} />;
    case 'team-table':
      return <TeamTable {...props} />;
    case 'problems-table':
      return <ProblemsTable {...props} />;
    case 'network-appointments-table':
      return <NetworkAppointmentsTable {...props} />;
    case 'select-network-appointments-table':
      return <SelectNetworkAppointmentsTable {...props} />;
    case 'select-diagnosis-table':
      return <SelectDiagnosisTable {...props} />;
    case 'select-medication-table':
      return <SelectMedicationTable {...props} />;
    case 'profile-diagnoses-table':
      return <ProfileDiagnosesTable {...props} />;
    case 'profile-medications-table':
      return <ProfileMedicationsTable {...props} />;
    case 'profile-problems-table':
      return <ProfileProblemsTable {...props} />;
    case 'profile-insurances-table':
      return <InsurancesTable {...props} />;
    case 'profile-contacts-table':
      return <ContactsTable {...props} />;
    case 'profile-teams-table':
      return <TeamsTable {...props} />;
    default:
      return <MainHouseholdRender {...props} />;
  }
}
