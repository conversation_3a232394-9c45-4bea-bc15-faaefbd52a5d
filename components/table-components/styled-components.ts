import Avatar from '@mui/material/Avatar';
import TableCell, { TableCellProps } from '@mui/material/TableCell';
import TableRow, { TableRowProps } from '@mui/material/TableRow';
import { border, borderBottom, styled } from '@mui/system';
import { cellStyles, rowStyles, avatarStyles } from './styled-objects';
// Import style variants
interface StyledTableRowProps extends TableRowProps {
  variant?:string;
  index?: any;
}
interface StyledTableCellProps extends TableCellProps {
  cellType?: string; // Define your custom prop
}
export const StyledTableCell = styled(TableCell)<StyledTableCellProps>(({ cellType }) => ({
  ...cellStyles[cellType || 'body'], // Use 'body' as a default if no variant is provided
}));

export const StyledTableRow = styled(TableRow)<StyledTableRowProps>(({ variant }) => ({
  ...rowStyles[variant || 'default'], // Use 'default' as a default if no variant is provided
}));

export const StyledAvatar = styled(Avatar)(({ variant }) => ({
  borderRadius: '50%',
  ...avatarStyles[variant || 'small'], // Use 'small' as a default if no variant is provided
}));

const addSectionBorderlessContainer = {
  borderCollapse: 'collapse',
  borderSpacing: 0,
  border: '1px solid #E5E5E5',
  boxShadow: 'none',
};
const addSectionBorderlessTable = {
  borderCollapse: 'collapse',
  borderSpacing: 0,
  border: '1px solid #E5E5E5',
  borderLeft: '0px',
  borderRight: '0px',
};
export const borderlessTableContainer = (variant: string) => {
  switch (variant) {
    case 'addsection-member-table':
      return {
        ...addSectionBorderlessContainer,
      };
    case 'report-full-data':
      return {
        ...addSectionBorderlessContainer,
        border: 'none',
      };
    case 'addsection-navigator-table':
      return {
        ...addSectionBorderlessContainer,
      };
    case 'addsection-team-table':
      return {
        ...addSectionBorderlessContainer,

      };
    case 'addsection-service-table':
      return {
        ...addSectionBorderlessContainer,

      };
    case 'social-plan-network-table':
      return {
        ...addSectionBorderlessContainer,

      };
    default:
      return {
        boxShadow: 'none',
      };
  }
};

export const borderlessTable = (variant: string) => {
  switch (variant) {
    case 'addsection-member-table':
      return {
        ...addSectionBorderlessTable,
      };
    case 'addsection-team-table':
      return {
        ...addSectionBorderlessTable,

      };
    case 'report-full-data':
      return {
        ...addSectionBorderlessTable,
        border: '0.2px solid #E5E5E5',
        borderBottom: '0.3px solid #E5E5E5',
        borderLeft: '0px',
      };
    case 'addsection-service-table':
      return {
        ...addSectionBorderlessTable,
      };
    case 'social-plan-network-table':
      return {
        ...addSectionBorderlessTable,
      };
    default:
      return {
        borderCollapse: 'collapse',
        borderSpacing: 0,
        border: 'none',
      };
  }
};

export const borderlessTableCell = {
  border: 'none',
};
