/* eslint-disable no-unused-vars */
import React, { useEffect, useState } from 'react';
import { Button } from '@mui/material';
import InsertPhotoIcon from '@mui/icons-material/InsertPhoto';

interface BannerPickerProps {
  setPicture: React.Dispatch<React.SetStateAction<string>>;
  picture: string;
  className?: string;
}

function BannerPicker({ setPicture, picture, className }: BannerPickerProps) {
  const [banner, setBanner] = useState('');
  const [buttonText, setButtonText] = useState('Select Image');

  const handleFileChange = (event: any) => {
    if (event.target.files && event.target.files[0]) {
      const reader = new FileReader();
      reader.onload = (e: any) => {
        setPicture(e.target.result);
        setBanner(e.target.result);
        setButtonText('Select Image');
      };

      reader.readAsDataURL(event.target.files[0]);
    }
  };
  useEffect(() => {
    if (picture) {
      setBanner(picture);
      setButtonText('Select Image');
    }
  }, [picture]);

  return (
    <div>
      <div
        className={`flex flex-col items-center justify-center border-solid border-1 border-[#DCDFDF] rounded-md h-56 w-96 ${className}`}
        style={{
          backgroundImage: `url(${banner})`,
          backgroundSize: 'cover',
        }}
      >
        {!banner && (
          <>
            <InsertPhotoIcon style={{ color: '#E97100', width: 60, height: 60 }} />
            <Button variant="text" sx={{ color: '#E97100' }} component="label">
              <div className="grid grid-flow-col auto-cols-max gap-3">
                Select Image
              </div>
              <input type="file" hidden onChange={handleFileChange} />
            </Button>
            <p className="italic text-[11px] text-gray-500 mt-1">Images must be (jpeg, png) format and 400px x 280px</p>
          </>
        )}
      </div>
      <div className="grid grid-flow-col auto-cols-max gap-1">
        {banner && (
        <>
          <Button
            variant="text"
            sx={{ color: '#DE6F09', textDecoration: 'underline' }}
            onClick={() => setBanner('')}
          >
            Remove
          </Button>
          <p>OR</p>
          <Button
            sx={{ color: '#E97100', textDecoration: 'underline' }}
            variant="text"
            component="label"
          >
            Choose New Image
            <input type="file" hidden onChange={handleFileChange} />
          </Button>
        </>
        )}
      </div>
    </div>
  );
}

export default BannerPicker;
