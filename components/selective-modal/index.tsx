/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React from 'react';
import { Box, Modal } from '@mui/material';
import { Close, Description } from '@mui/icons-material';

interface SelectSocialPlanProps {
    open: boolean;
    title: string;
    setOpen: React.Dispatch<React.SetStateAction<boolean>>;
    itemTypes: {
        title: string;
        key: string;
    }[];
    // eslint-disable-next-line no-unused-vars
    onChange: (value: string) => void;
}

// eslint-disable-next-line react/function-component-definition
const SelectiveModal = ({
  open, setOpen, itemTypes = [], onChange, title,

}: SelectSocialPlanProps) => {
  const handleClose = () => {
    setOpen(false);
  };

  return (
    <Modal open={open} onClose={handleClose}>
      <Box sx={{
        position: 'absolute',
        maxHeight: 'auto',
        minWidth: '40%',
        outline: 'none',
        borderRadius: '8px',
        top: '50%',
        left: '50%',
        transform: 'translate(-50%, -50%)',
        width: 450,
        padding: 2,
        bgcolor: 'background.paper',
        boxShadow: 24,
      }}
      >
        <div className="grid grid-flow-col m-0 p-0 relative auto-cols-auto justify-center justify-items-center items-center borderLine">
          <p className="m-0 p-0 text-lg">{title}</p>
          <Close onClick={handleClose} className="cursor-pointer absolute top-0 right-0 float-right w-8 h-8" />
        </div>
        <div
          className="grid grid-flow-row h-full"
        >
          { itemTypes.map((item) => (
            <div
              key={item.key}
              onClick={() => onChange(item.key)}
              style={{
                height: '52px',
              }}
              className={`grid grid-flow-col w-full border-2 border-solid cursor-pointer items-center auto-cols-max pl-3
             gap-3 mt-3 rounded-lg border-gray-base hover:text-primary hover:border-primary`}
            >
              <Description className="text-center font-[500] text-2xl" />
              <p className="text-center text-sm font-[500] m-0 p-0">{item.title}</p>
            </div>
          ))}
        </div>
      </Box>
    </Modal>
  );
};

export default SelectiveModal;
