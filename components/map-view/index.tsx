import { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { Checkbox } from '@mui/material';
import { useRouter } from 'next/router';

mapboxgl.accessToken = 'pk.eyJ1IjoiYm91bG1wb3MiLCJhIjoiY2wzaDdmMXk3MTljbTNrcDhxMmRvczc0aSJ9.E8moVfuzANGeV5OaSAK8gg';

const sectionStyles = {
  resilient: { color: '#2370CB' },
  urgent_needs: { color: '#E97100' },
  stable: { color: '#008390' },
  location: { color: 'grey' },
};

function MapComponent({ locations }: any) {
  const mapContainerRef = useRef<any>(null);
  const router = useRouter();
  const [visibleSections, setVisibleSections] = useState({
    resilient: true,
    urgent_needs: true,
    stable: true,
    location: true,
  });

  useEffect(() => {
    const map = new mapboxgl.Map({
      container: mapContainerRef.current,
      style: 'mapbox://styles/mapbox/light-v10',
      center: [-81.5158, 27.6648],
      zoom: 3, // Set a default zoom level
    });
    const bounds = new mapboxgl.LngLatBounds();

    const addMarkers = (section:any, points:any) => {
      if (!points) return;
      points?.forEach((point:any) => {
        const { lat, lon, memberId } = point;
        if (lat >= -90 && lat <= 90 && lon >= -180 && lon <= 180) {
          const el = document.createElement('img');
          el.style.backgroundColor = (sectionStyles as any)[section].color;
          el.className = 'cursor-pointer w-[20px] h-[20px] hover:w-[25px] hover:h-[25px] transition-all duration-300';
          el.style.borderRadius = '50%';
          el.style.border = '1px solid white';
          el.style.boxShadow = `0 0 10px ${(sectionStyles as any)[section].color}`;

          // Add click event listener
          el.addEventListener('click', () => {
            // Perform action on click
            router.push(`/members/profile/${memberId}`);
          });
          new mapboxgl.Marker(el)
            .setLngLat([parseFloat(lon), parseFloat(lat)])
            .addTo(map);
          bounds.extend([lon, lat]);
        }
      });
    };

    Object.keys(visibleSections).forEach((section) => {
      if ((visibleSections as any)[section]) {
        addMarkers(section, locations[section]);
      }
    });
    if (!bounds.isEmpty()) {
      map.fitBounds(bounds, { padding: 100 });
    }

    return () => map.remove();
  }, [locations, visibleSections]);

  const handleToggle = (section:any) => {
    setVisibleSections((prevState:any) => ({
      ...prevState,
      [section]: !prevState[section],
    }));
  };

  return (
    <div>

      <div className="grid grid-flow-col auto-cols-max items-end float-right">
        {Object.keys(visibleSections).map((section) => (
          // eslint-disable-next-line jsx-a11y/label-has-associated-control
          <label
            key={section}
            className="grid grid-flow-col auto-cols-max items-center"
          >
            <Checkbox
              checked={(visibleSections as any)[section] as boolean}
              style={{ color: (sectionStyles as any)[section].color }}
              onChange={() => handleToggle(section)}
            />
            <p className="font-[500] text-xs m-0">{section.charAt(0).toUpperCase() + section.slice(1).replace('_', ' ')}</p>
          </label>
        ))}
      </div>
      <div ref={mapContainerRef} style={{ width: '100%', height: '500px' }} />
    </div>
  );
}

export default MapComponent;
