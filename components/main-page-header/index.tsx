import React from 'react';

interface MainPageHeaderProps {
    title: string;
    subTitle: React.ReactNode;
    renderRight: React.ReactNode;
}

function MainPageHeader({ title, subTitle, renderRight }: MainPageHeaderProps) {
  return (
    <div className="pr-10 borderLine pl-5 pb-3 pt-3 max-h-screen grid grid-flow-col auto-cols-auto items-center overflow-auto">
      <h1
        className="m-0 font-[500] text-xl"
      >
        {title}
        {' '}
        <span className="font-[300]">
          {subTitle}
        </span>
      </h1>
      <div className="justify-self-end grid grid-flow-col auto-cols-max gap-2">
        {renderRight}
      </div>
    </div>
  );
}

export default MainPageHeader;
