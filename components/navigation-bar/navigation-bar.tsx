/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { useLoading } from 'contexts/loading-context/loading-context';
import useAuth from 'hooks/use-auth';
import { useRouter } from 'next/router';
import {
  useState, useCallback, useEffect,
} from 'react';

/* eslint-disable-next-line */
export interface NavigationBarProps { }

interface IMenuItems {
  id: number;
  key: string;
  name: string;
  icon: string;
  link: string;
}

const MenuItems: IMenuItems[] = [
  {
    id: 0,
    key: 'tasks',
    name: 'Tasks',
    icon: 'bg-icon-tasks',
    link: '/tasks',

  },
  {
    id: 1,
    key: 'households',
    name: 'Households',
    icon: 'bg-icon-house',
    link: '/households',
  },
  {
    id: 2,
    key: 'members',
    name: 'Members',
    icon: 'bg-icon-clients',
    link: '/members',
  },
  {
    id: 3,
    key: 'teams',
    name: 'Teams',
    icon: 'bg-icon-teams',
    link: '/teams',
  },
  {
    id: 4,
    key: 'chats',
    name: 'Chat',
    icon: 'bg-icon-chat',
    link: '/chats',
  },
  {
    id: 5,
    key: 'navigators',
    name: 'Navigators',
    icon: 'bg-icon-navigators',
    link: '/navigators',
  },
  {
    id: 6,
    key: 'networks',
    name: 'Networks',
    icon: 'bg-icon-networks',
    link: '/networks',
  },
  {
    id: 7,
    key: 'reporting',
    name: 'Reporting',
    icon: 'bg-icon-reporting',
    link: '/reporting',
  },
];

function NavigationBar() {
  const [selected, setSelected] = useState(0);
  const router = useRouter();
  const { showLoading, hideLoading } = useLoading();
  const { user } = useAuth();

  const handleClick = useCallback(async (e: any) => {
    showLoading();
    const index = parseInt(e.currentTarget.dataset.index, 10);
    setSelected(index);
    await router.push(MenuItems[index].link);
    hideLoading();
  }, []);

  useEffect(() => {
    const index = MenuItems.findIndex((item) => router.pathname.includes(item.link));
    if (index !== selected) {
      setSelected(index);
    }
  }, [router.pathname]);

  return (
    <div
      className="bg-white max-h-[calc(100vh-60px)] grid grid-flow-row gap-3 pt-2 auto-rows-max justify-between relative z-10"
    >
      {
        MenuItems.map((item, index) => {
          if (!user?.meta?.access?.includes(item.key)) {
            return null;
          }

          return (
            <div
              data-index={item.id}
              onClick={handleClick}
              key={index as number}
              className={`grid p-5 ${item.id === selected ? 'bg-[#FFF7EC] rounded-md' : ''} ml-0.5 items-center cursor-pointer justify-items-center`}
            >
              <div
                key={item.id as number}
                data-index={item.id}
                className={`${item.icon} ${item.id === selected && 'mix-blend-difference'}
                bg-center cursor-pointer justify-center bg-no-repeat w-[22px] h-[22px]`}
              />
              <p className="m-0 p-0 text-xs font-[500]">
                {item.name}
              </p>
            </div>
          );
        })
      }
    </div>
  );
}
export default NavigationBar;
