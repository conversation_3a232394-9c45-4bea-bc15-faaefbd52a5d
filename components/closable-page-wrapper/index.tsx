import { ReactNode } from 'react';
import CancelOutlinedIcon from '@mui/icons-material/CancelOutlined';

interface ClosablePageWrapperProps {
    children: ReactNode;
    onClose: () => void;
}
const container = {
  position: 'fixed',
  width: '100%',
  backgroundColor: '#fff',
  zIndex: 2,
  borderBottom: '1px solid #E0E0E0',
} as const;
const icon = {
  width: 35,
  height: 35,
};

function ClosablePageWrapper({ children, onClose }: ClosablePageWrapperProps) {
  return (
    <div
      style={container}
      className="grid grid-flow-col gap-8 p-3 pl-5 auto-cols-max items-center relative"
    >
      <CancelOutlinedIcon
        onClick={onClose}
        style={icon}
        className="text-[#646F79] text-3xl cursor-pointer"
      />
      {children}
    </div>
  );
}

export default ClosablePageWrapper;
