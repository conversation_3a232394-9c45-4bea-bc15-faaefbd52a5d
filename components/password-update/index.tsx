import React, { useState } from 'react';
import { Close, Visibility, VisibilityOff } from '@mui/icons-material';
import { Modal, Button, TextField, IconButton, InputAdornment } from '@mui/material';
import { useFormik } from 'formik';
import { createValidationSchema } from 'utils/helper';
import CheckIcon from '@mui/icons-material/Check';

interface PasswordUpdateProps {
  onComplete: (values: any) => void;
  open: boolean;
  setOpen: (value: boolean) => void;
}

function PasswordUpdate({
  onComplete, open, setOpen,
}: PasswordUpdateProps) {
  const [showOldPassword, setShowOldPassword] = useState(false);
  const [showNewPassword, setShowNewPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const initialValues = {
    oldPassword: '',
    newPassword: '',
    confirmPassword: '',
  };

  const formik = useFormik({
    initialValues,
    onSubmit: async (values) => {
      try {
        onComplete(values);
        setOpen(false);
      } catch (err) {
        console.log(err);
      }
    },
    validateOnChange: false,
    validationSchema: createValidationSchema(initialValues),
  });

  const handleClickShowOldPassword = () => setShowOldPassword(!showOldPassword);
  const handleClickShowNewPassword = () => setShowNewPassword(!showNewPassword);
  const handleClickShowConfirmPassword = () => setShowConfirmPassword(!showConfirmPassword);

  const handleMouseDownPassword = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();
  };

  return (
    <Modal className="items-center flex justify-center" open={open} onClose={() => setOpen(false)}>
      <div className="bg-white lg:w-[30%] w-full relative min-h-[50%] mb-10 rounded-lg">
        <div
          style={{ borderBottom: '1px solid #E0E0E0' }}
          className="h-[64px] items-center relative justify-center grid grid-flow-col border border-black"
        >
          <div className="grid grid-flow-row p-2 items-center">
            <p className="m-0 font-semibold text-md">Update Password</p>
          </div>
          <Close className="absolute right-5 cursor-pointer" onClick={() => setOpen(false)} />
        </div>

        <div className="grid grid-cols-1 auto-cols-max gap-8 p-5">
          <TextField
            name="oldPassword"
            label="Old Password"
            variant="outlined"
            type={showOldPassword ? 'text' : 'password'}
            value={formik.values.oldPassword}
            onChange={formik.handleChange}
            error={formik.touched.oldPassword && Boolean(formik.errors.oldPassword)}
            helperText={formik.touched.oldPassword && formik.errors.oldPassword}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle old password visibility"
                    onClick={handleClickShowOldPassword}
                    onMouseDown={handleMouseDownPassword}
                    edge="end"
                  >
                    {showOldPassword ? <Visibility /> : <VisibilityOff />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          <TextField
            name="newPassword"
            label="New Password"
            variant="outlined"
            type={showNewPassword ? 'text' : 'password'}
            value={formik.values.newPassword}
            onChange={formik.handleChange}
            error={formik.touched.newPassword && Boolean(formik.errors.newPassword)}
            helperText={formik.touched.newPassword && formik.errors.newPassword}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle new password visibility"
                    onClick={handleClickShowNewPassword}
                    onMouseDown={handleMouseDownPassword}
                    edge="end"
                  >
                    {showNewPassword ? <Visibility /> : <VisibilityOff />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
          <TextField
            name="confirmPassword"
            label="Confirm Password"
            variant="outlined"
            type={showConfirmPassword ? 'text' : 'password'}
            value={formik.values.confirmPassword}
            onChange={formik.handleChange}
            error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
            helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton
                    aria-label="toggle confirm password visibility"
                    onClick={handleClickShowConfirmPassword}
                    onMouseDown={handleMouseDownPassword}
                    edge="end"
                  >
                    {showConfirmPassword ? <Visibility /> : <VisibilityOff />}
                  </IconButton>
                </InputAdornment>
              ),
            }}
          />
        </div>

        <div
          id="footer-area"
          style={{
            borderTop: '1px solid #E0E0E0',
            position: 'absolute',
            alignItems: 'center',
            justifyItems: 'center',
            width: '100%',
            zIndex: 2,
            bottom: 0,
          }}
          className="h-[54px] bg-white items-center justify-center grid p-4 border border-black"
        >
          <Button
            onClick={formik.submitForm}
            variant="contained"
            className="bg-[#2D62ED] w-[90%] items-center grid top-2 text-white absolute rounded-md"
          >
            <div className="grid grid-flow-col justify-center items-center gap-2">
              <CheckIcon />
              Confirm
            </div>
          </Button>
        </div>
      </div>
    </Modal>
  );
}

export default PasswordUpdate;
