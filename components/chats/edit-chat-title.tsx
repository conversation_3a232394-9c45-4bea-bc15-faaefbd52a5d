/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useState } from 'react';
import {
  Box, Button, Modal, TextField, Typography,
} from '@mui/material';
import { useModal } from 'contexts/modal-context/modal-context';
import { Close } from '@mui/icons-material';
import API from 'api/src/lib/api';
import CheckIcon from '@mui/icons-material/Check';

interface EditChatTitleProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  chatTitle: string;
  chatId: string;
  refresh: () => Promise<void>;
}

function EditChatTitle({
  open,
  setOpen,
  chatTitle,
  chatId,
  refresh,
}: EditChatTitleProps) {
  const { showModal } = useModal();
  const [title, setTitle] = useState(chatTitle);

  const handleClose = () => {
    setOpen(false);
  };

  const handleSave = async () => {
    if (title.trim() === '') {
      showModal('Error', 'Title cannot be empty');
      return;
    }

    try {
      await API.CHATS.updateChatTitle(chatId, { title });
      await refresh();
      handleClose();
    } catch (error) {
      showModal('Error', 'Failed to update chat title');
    }
  };

  return (
    <Modal open={open} onClose={handleClose}>
      <Box
        sx={{
          position: 'absolute',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 700,
          bgcolor: '#FFFFFF',
          display: 'flex',
          flexDirection: 'column',
          borderRadius: '12px',
        }}
      >
        <div
          className="flex justify-center items-center p-4 relative"
          style={{ borderBottom: '1px solid #E0E0E0' }}
        >
          <div className="text-[20px] font-[500]">Edit Chat Title</div>
          <Close
            className="absolute right-4 cursor-pointer text-gray-500 hover:text-[#262626]"
            style={{
              strokeWidth: '1',
              width: '32px',
              height: '32px',
            }}
            onClick={handleClose}
          />
        </div>
        <div className="p-4 mb-10">
          <TextField
            label="Chat Title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            fullWidth
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: '8px',
                backgroundColor: '#F5F5F5',
              },
            }}
          />
        </div>
        <div
          className="p-2 flex justify-end"
          style={{ borderTop: '1px solid #E0E0E0' }}
        >
          <Button
            variant="contained"
            className="rounded-md m-0 p-0 justify-center items-center"
            onClick={handleSave}
          >
            <CheckIcon
              style={{ width: '16px', height: '16px' }}
              className="mr-2"
            />
            Save
          </Button>
        </div>
      </Box>
    </Modal>
  );
}

export default EditChatTitle;
