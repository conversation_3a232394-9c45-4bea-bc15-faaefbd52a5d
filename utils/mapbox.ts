import mbxDirections from '@mapbox/mapbox-sdk/services/directions';

const directionsClient = mbxDirections({
  accessToken: 'kNn206rnWRGm@',
});

export const getDrivingDistance = async (
  from: [number, number], // [lng, lat]
  to: [number, number],
): Promise<number | null> => {
  try {
    const response = await directionsClient
      .getDirections({
        profile: 'driving',
        geometries: 'geojson',
        waypoints: [{ coordinates: from }, { coordinates: to }],
      })
      .send();

    const meters = response.body.routes[0].distance;
    const miles = meters / 1609.34;
    return Math.round(miles * 10) / 10; // round to 1 decimal
  } catch (err) {
    console.error('Error fetching driving distance:', err);
    return null;
  }
};

export default getDrivingDistance;
