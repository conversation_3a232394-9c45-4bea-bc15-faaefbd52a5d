/* eslint-disable no-nested-ternary */
/* eslint-disable no-unused-vars */
/* eslint-disable @typescript-eslint/no-explicit-any */
// eslint-disable-next-line import/no-extraneous-dependencies
import * as yup from 'yup';
import { Fullhrp } from 'contexts/constant-context/type';
import { AttachmentResponse } from 'types/attachment-types';
import { Segment } from 'utils/type';

interface ValidationCondition {
  fields: string[];
  isRequiredIfTrue: boolean;
  isRequiredIfFalse: boolean;
  errorMessageIfTrue?: string; // Optional custom error message when condition is true
  errorMessageIfFalse?: string; // Optional custom error message when condition is false
  prerequisite?: (values: any) => boolean;
}

export interface ValidationConfig {
  conditions: { [key: string]: ValidationCondition[] };
}
export const convertKeyValue = async (
  // eslint-disable-next-line default-param-last
  dataArray: any[] = [],
  keyField: string,
  valueField: string,
  [keyName, valueName]: string[],
) => Promise.all(
  dataArray.map(
    async (item) => ({
      [keyName]: await item[keyField],
      [valueName]: await item[valueField],
    } as never),
  ),
);
/* eslint-disable import/prefer-default-export */
export function calculateAge(birthdate: string) {
  const dob = new Date(birthdate);
  const today = new Date();

  let age = today.getFullYear() - dob.getFullYear();
  const monthDifference = today.getMonth() - dob.getMonth();
  if (
    monthDifference < 0
    || (monthDifference === 0 && today.getDate() < dob.getDate())
  ) {
    // eslint-disable-next-line no-plusplus
    age--;
  }

  return Number.isNaN(age) ? '-' : age;
}

export function getLatestImage(
  attachments: AttachmentResponse[],
  type: string,
) {
  if (!attachments) return '';
  const images = attachments
    .filter((attachment) => attachment.kind === type)
    .sort((a, b) => {
      const dateA = new Date(a.createdAt).getTime();
      const dateB = new Date(b.createdAt).getTime();
      return dateB - dateA;
    })[0];
  return images?.url || '';
}

export function getLatestData(data: any[], field = 'createdAt') {
  if (!data) return [];
  return data.sort((a, b) => {
    const dateA = new Date(a[field]).getTime();
    const dateB = new Date(b[field]).getTime();
    return dateB - dateA;
  })[0];
}

export function dateToEpoch(dateStr: string) {
  const date = new Date(dateStr); // This creates a date object using the date string
  return Math.floor(date.getTime() / 1000); // getTime returns milliseconds, convert to seconds
}

export function timeDifference(date: number) {
  const now: any = new Date();
  const givenDate: any = new Date(date * 1000); // Convert epoch seconds to date
  const msDifference = givenDate - now;
  const absDifference = Math.abs(msDifference);

  // Calculate days and hours
  const days = Math.floor(absDifference / (1000 * 60 * 60 * 24));
  const hours = Math.floor((absDifference / (1000 * 60 * 60)) % 24);

  // Determine if the time difference is within today
  if (days === 0 && hours <= 24) {
    if (msDifference < 0) {
      return 'Today'; // Event occurred earlier today
    }
    if (hours === 0) {
      return 'Today'; // Event is within the next hour
    }
    return 'Today'; // Event is later today
  }

  const result = `${days}d`;
  if (msDifference < 0) {
    return 'Past Due';
  }
  if (days === 0) return 'Today';
  return `${result}`;
}

export function timeDifferenceFullDaysWord(date: number) {
  const now: any = new Date();
  const givenDate: any = new Date(date * 1000); // Convert epoch seconds to date
  const msDifference = givenDate - now;
  const absDifference = Math.abs(msDifference);

  // Calculate days and hours
  const days = Math.floor(absDifference / (1000 * 60 * 60 * 24));
  const hours = Math.floor((absDifference / (1000 * 60 * 60)) % 24);

  // Determine if the time difference is within today
  if (days === 0 && hours <= 24) {
    if (msDifference < 0) {
      return 'Today'; // Event occurred earlier today
    }
    if (hours === 0) {
      return 'Today'; // Event is within the next hour
    }
    return 'Today'; // Event is later today
  }

  const result = `${days} days`;
  if (msDifference < 0) {
    return 'Past Due';
  }
  if (days === 0) return 'Today';
  return `${result}`;
}

// Addressing issue of conflicting date format on server and client during hydration
let hasMounted = false;

if (typeof window !== 'undefined') {
  // This sets the flag true on first load (client-only)
  window.addEventListener('DOMContentLoaded', () => {
    hasMounted = true;
  });
}

export function dateFormat(dateString: string, time?: boolean, utc?: boolean): string {
  if (!dateString) return '-';
  if (dateString === 'Invalid Date') return '-';

  const date = new Date(dateString);

  // Default to UTC on server, use browser tz on client
  const timeZone = utc || !hasMounted
    ? 'UTC'
    : Intl.DateTimeFormat().resolvedOptions().timeZone;

  const formattedDate = new Intl.DateTimeFormat('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    timeZone,
  }).format(date);

  if (time) {
    const formattedTime = new Intl.DateTimeFormat('en-US', {
      hour: 'numeric',
      minute: 'numeric',
      hour12: true,
      timeZone,
    }).format(date);
    return `${formattedDate} ${formattedTime}`;
  }

  return formattedDate;
}

export function hexToRGB(_hex: string, alpha = 1) {
  // Remove the '#' if it's included
  const hex = _hex?.replace(/^#/, '');

  // Parse the hex string to three separate parts for R, G, and B
  const bigint = parseInt(hex, 16);
  // eslint-disable-next-line no-bitwise
  const r = (bigint >> 16) & 255;
  // eslint-disable-next-line no-bitwise
  const g = (bigint >> 8) & 255;
  // eslint-disable-next-line no-bitwise
  const b = bigint & 255;

  // Return the RGB values as an object
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

export function findRangeTitle(
  ranges: Fullhrp[],
  score: number | string,
): object | null {
  // eslint-disable-next-line no-param-reassign
  score = Number(score); // Convert score to number
  let result = null;
  // eslint-disable-next-line no-restricted-syntax
  for (const range of ranges) {
    const [low, high] = range.overall_score_range;
    if (score < high && score >= low) {
      result = {
        title: range.title,
        color: range.color,
      };
      break;
    }
  }

  return result; // Return the result or null if no range was found that contains the score.
}

// eslint-disable-next-line no-unused-vars
export function groupBy(list: any[], keyGetter: (x: any) => void) {
  const map = new Map();
  list.forEach((item) => {
    const key = keyGetter(item);
    const collection = map.get(key);
    if (!collection) {
      map.set(key, [item]);
    } else {
      collection.push(item);
    }
  });
  return Array.from(map.values()) as never[];
}
export function groupByNested(
  list: any[],
  keyGetter: (x: any) => any,
  nestedKey: string,
) {
  const map = new Map();
  list.forEach((item) => {
    const key = keyGetter(item);
    const collection = map.get(key);
    if (!collection) {
      map.set(key, item[nestedKey]);
    } else {
      collection.push(item[nestedKey]);
    }
  });
  return Array.from(map.values()) as never[];
}

export const validateFormWithConditions = (
  values: any,
  config: ValidationConfig,
) => {
  const errors = {};

  Object.keys(config.conditions).forEach((conditionKey) => {
    config.conditions[conditionKey].forEach(
      ({
        fields,
        isRequiredIfTrue,
        isRequiredIfFalse,
        prerequisite,
        errorMessageIfTrue,
        errorMessageIfFalse,
      }) => {
        const isActive = conditionKey === 'general' || values[conditionKey]; // Assume 'general' always applies
        if (!prerequisite || prerequisite(values)) {
          fields.forEach((field) => {
            const fieldValue = field
              .split('.')
              .reduce((acc, part) => acc && acc[part], values);
            const isRequired = isActive ? isRequiredIfTrue : isRequiredIfFalse;
            const errorMessage = isActive
              ? errorMessageIfTrue
              : errorMessageIfFalse;

            if (isRequired && !fieldValue) {
              const errorPath = field.split('.');
              let lastErrorObj: any = errors;
              errorPath.forEach((path: any, index) => {
                if (index === errorPath.length - 1) {
                  lastErrorObj[path] = errorMessage || `${field} is required`;
                } else {
                  lastErrorObj[path] = lastErrorObj[path] || {};
                  lastErrorObj = lastErrorObj[path];
                }
              });
            }
          });
        }
      },
    );
  });

  return errors;
};

// eslint-disable-next-line default-param-last
export function getKeyValue(
  key: string,
  pick: string,
  constants: any,
  prop?: string,
) {
  if (key && pick && constants) {
    const picker = constants[pick];
    const result = picker?.find((item: any) => item.key === key);
    if (!result) return '';
    return result[prop || 'title'];
  }
  return '';
}

export function capitalizeWords(string: string, allWords = false): string {
  // Check if the input is a valid string
  if (typeof string !== 'string') {
    return '';
  }

  // Check if the string is not empty
  if (string.length === 0) {
    return string; // Return an empty string as it is
  }

  if (allWords) {
    // Capitalize the first letter of each word in the string
    return string
      .split(' ')
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  }
  // Capitalize only the first letter of the entire string
  return string.charAt(0).toUpperCase() + string.slice(1).toLowerCase();
}

// eslint-disable-next-line no-promise-executor-return
export const sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

export function formatPhoneNumber(phoneNumber: string) {
  if (!phoneNumber) return '';
  // Remove any non-digit characters from the input string
  const cleaned = phoneNumber.replace(/\D/g, '');

  // Check if the cleaned string has 10 digits
  if (cleaned.length === 10) {
    // Use regular expressions to format the phone number
    return cleaned.replace(/(\d{3})(\d{3})(\d{4})/, '$1-$2-$3');
  }
  // If the input doesn't have 10 digits, return it as is
  return phoneNumber;
}

export function getIdsFromArray(array: any[], key = 'id') {
  const ids = array.map((item) => item[key]);
  return ids;
}

export function isSafari() {
  if (typeof window === 'undefined') return false;
  // eslint-disable-next-line no-unsafe-optional-chaining
  const { userAgent } = window?.navigator;

  // Check for Safari
  // eslint-disable-next-line no-shadow, no-underscore-dangle
  const _isSafari = /^((?!chrome|android).)*safari/i.test(userAgent);

  return _isSafari;
}

export function isEmptyObject(obj: object) {
  return Object.keys(obj).length === 0;
}

export function createValidationSchema(initialValues: any) {
  let schema = yup.object().shape({});
  Object.keys(initialValues).forEach((key) => {
    // Check if the current key is 'email'
    if (key === 'email') {
      // Apply specific validation rules for 'email'
      schema = schema.shape({
        [key]: yup
          .string()
          .email('Invalid email')
          .required(`${key} is required`),
      });
    } else {
      // Apply generic validation rule for other fields
      schema = schema.shape({
        [key]: yup.string().required(`${key} is required`),
      });
    }
  });
  return schema;
}

export function addressFormatter(location: any) {
  // Destructure the location object to get each field
  if (!location) return '';
  const {
    street, street2, city, state, zip,
  } = location;

  // Create an array and filter out empty values to avoid extra spaces or commas
  const addressParts = [street, street2, city, state, zip].filter(
    (part) => part,
  );

  // Join the parts with a comma and a space, except between street and street2
  return addressParts.reduce((acc, part, index) => {
    if (index === 1 && part) {
      // Directly append street2 without a comma if it exists
      return `${acc} ${part}`;
    }
    return acc ? `${acc}, ${part}` : part;
  }, '');
}

const colors = [
  '#008390',
  '#F09C4D',
  '#F4B880',
  '#F8D4B3',
  '#FCEAD9',
  '#006973',
  '#E0863B',
  '#EDB265',
  '#F4C191',
  '#FCE6C4',
  '#004F56',
  '#CF6E29',
  '#E6994B',
  '#EDAE7D',
  '#F9D1AD',
];

export const fromObjectToSegments = (
  obj: { [key: string]: number },
  fullData?: boolean,
): Segment[] => {
  if (Object.keys(obj).length === 0) {
    return [];
  }

  // Convert object to array and sort by value in descending order
  const sortedEntries = Object.entries(obj)
    .map(([name, value]) => ({ name, value }))
    .sort((a, b) => b.value - a.value);

  // Assign colors to the sorted segments
  const segments = sortedEntries.map((segment, index) => ({
    ...segment,
    color: colors[index % colors.length],
  }));
  if (fullData) return segments;
  // If there are more than 4 segments, combine the remaining into "Others"
  if (segments.length > 4) {
    const topSegments = segments.slice(0, 4);
    const othersValue = segments
      .slice(4)
      .reduce((acc, segment) => acc + segment.value, 0);
    topSegments.push({ name: 'Others', value: othersValue, color: colors[4] });
    return topSegments;
  }

  return segments;
};
export const toServerDate = (date: string) => {
  // Check if the input date is already in ISO format
  if (date.includes('T')) {
    return date; // If it's already in ISO format, return it as is
  }

  const [datePart, timePart] = date.split(' '); // Split date and time parts

  // If there's no time part, return the date with default time (00:00:00)
  if (!timePart) {
    return `${datePart}T00:00:00.000Z`;
  }

  const is24HourFormat = (timeString: string) => {
    // Regular expression to match the 24-hour time format (HH:mm or HH:mm:ss)
    const regex = /^([01]\d|2[0-3]):[0-5]\d(:[0-5]\d)?$/;
    return regex.test(timeString);
  };

  // Check if the time part is already in 24-hour format
  if (is24HourFormat(timePart)) {
    // If time is already in 24-hour format, construct ISO string format directly
    return `${datePart}T${timePart}:00.000Z`;
  }

  // Handle 12-hour format with "am" or "pm"
  // Split timePart into time and period only if timePart is defined
  const [time, period] = timePart ? timePart.split(' ') : ['', ''];

  // eslint-disable-next-line prefer-const
  let [hours, minutes] = time.split(':');

  // Convert hours to 24-hour format if in 12-hour format
  if (period?.toLowerCase() === 'pm' && hours !== '12') {
    hours = String(parseInt(hours, 10) + 12);
  } else if (period?.toLowerCase() === 'am' && hours === '12') {
    hours = '00';
  }

  // Construct ISO string format with correct hours and minutes
  const isoString = new Date(
    `${datePart}T${hours}:${minutes}:00.000Z`,
  ).toISOString();
  return isoString;
};

export const makeQueryString = (params: string) => {
  const filteredValues = Object.fromEntries(
    // eslint-disable-next-line no-unused-vars
    Object.entries(params).filter(([__, value]) => value.trim() !== ''),
  );

  if (Object.keys(filteredValues).length === 0) {
    return;
  }
  const queryString = new URLSearchParams(filteredValues)
    .toString()
    .toLowerCase();
  // eslint-disable-next-line consistent-return
  return queryString;
};
