/* eslint-disable no-restricted-syntax */
/* eslint-disable no-unused-expressions */
/* eslint-disable max-classes-per-file */
window.NREUM || (NREUM = {});
NREUM.init = {
  distributed_tracing: { enabled: true },
  privacy: { cookies_enabled: true },
  ajax: { deny_list: ['bam.nr-data.net'] },
};

NREUM.loader_config = {
  accountID: '6419180',
  trustKey: '6419180',
  agentID: '**********',
  licenseKey: 'NRJS-8480b73916427e4eefb',
  applicationID: '**********',
};
NREUM.info = {
  beacon: 'bam.nr-data.net',
  errorBeacon: 'bam.nr-data.net',
  licenseKey: 'NRJS-8480b73916427e4eefb',
  applicationID: '**********',
  sa: 1,
}; /*! For license information please see nr-loader-spa-1.290.1.min.js.LICENSE.txt */
(() => {
  let e;
    let t;
    const r = {
      8122: (e, t, r) => {
        
        r.d(t, { a: () => i });
        const n = r(944);
        function i(e, t) {
          try {
            if (!e || typeof e !== 'object') return (0, n.R)(3);
            if (!t || typeof t !== 'object') return (0, n.R)(4);
            const r = Object.create(
                Object.getPrototypeOf(t),
                Object.getOwnPropertyDescriptors(t)
              );
              const o = Object.keys(r).length === 0 ? e : r;
            for (const a in o)
              if (void 0 !== e[a])
                try {
                  if (e[a] === null) {
                    r[a] = null;
                    continue;
                  }
                  Array.isArray(e[a]) && Array.isArray(t[a])
                    ? (r[a] = Array.from(new Set([...e[a], ...t[a]])))
                    : typeof e[a] === 'object' && typeof t[a] === 'object'
                    ? (r[a] = i(e[a], t[a]))
                    : (r[a] = e[a]);
                } catch (e) {
                  r[a] || (0, n.R)(1, e);
                }
            return r;
          } catch (e) {
            (0, n.R)(2, e);
          }
        }
      },
      2555: (e, t, r) => {
        
        r.d(t, { D: () => s, f: () => a });
        const n = r(384);
          const i = r(8122);
        const o = {
          beacon: n.NT.beacon,
          errorBeacon: n.NT.errorBeacon,
          licenseKey: void 0,
          applicationID: void 0,
          sa: void 0,
          queueTime: void 0,
          applicationTime: void 0,
          ttGuid: void 0,
          user: void 0,
          account: void 0,
          product: void 0,
          extra: void 0,
          jsAttributes: {},
          userAttributes: void 0,
          atts: void 0,
          transactionName: void 0,
          tNamePlain: void 0,
        };
        function a(e) {
          try {
            return !!e.licenseKey && !!e.errorBeacon && !!e.applicationID;
          } catch (e) {
            return !1;
          }
        }
        const s = (e) => (0, i.a)(e, o);
      },
      9324: (e, t, r) => {
        
        r.d(t, { F3: () => i, Xs: () => o, Yq: () => a, xv: () => n });
        const n = '1.290.1';
          const i = 'PROD';
          const o = 'CDN';
          const a = '^2.0.0-alpha.18';
      },
      6154: (e, t, r) => {
        
        r.d(t, {
          A4: () => s,
          OF: () => d,
          RI: () => i,
          WN: () => h,
          bv: () => o,
          gm: () => a,
          lR: () => f,
          m: () => u,
          mw: () => c,
          sb: () => l,
        });
        const n = r(1863);
        const i = typeof window !== 'undefined' && !!window.document;
          const o =
            typeof WorkerGlobalScope !== 'undefined' &&
            ((typeof self !== 'undefined' &&
              self instanceof WorkerGlobalScope &&
              self.navigator instanceof WorkerNavigator) ||
              (typeof globalThis !== 'undefined' &&
                globalThis instanceof WorkerGlobalScope &&
                globalThis.navigator instanceof WorkerNavigator));
          const a = i
            ? window
            : typeof WorkerGlobalScope !== 'undefined' &&
              ((typeof self !== 'undefined' &&
                self instanceof WorkerGlobalScope &&
                self) ||
                (typeof globalThis !== 'undefined' &&
                  globalThis instanceof WorkerGlobalScope &&
                  globalThis));
          const s = a?.document?.readyState === 'complete';
          const c = Boolean(a?.document?.visibilityState === 'hidden');
          const u = `${  a?.location}`;
          const d = /iPad|iPhone|iPod/.test(a.navigator?.userAgent);
          const l = d && typeof SharedWorker === 'undefined';
          const f = (() => {
            const e = a.navigator?.userAgent?.match(/Firefox[/\s](\d+\.\d+)/);
            return Array.isArray(e) && e.length >= 2 ? +e[1] : 0;
          })();
          const h = Date.now() - (0, n.t)();
      },
      7295: (e, t, r) => {
        
        r.d(t, { Xv: () => a, gX: () => i, iW: () => o });
        let n = [];
        function i(e) {
          if (!e || o(e)) return !1;
          if (n.length === 0) return !0;
          for (let t = 0; t < n.length; t++) {
            const r = n[t];
            if (r.hostname === '*') return !1;
            if (s(r.hostname, e.hostname) && c(r.pathname, e.pathname))
              return !1;
          }
          return !0;
        }
        function o(e) {
          return void 0 === e.hostname;
        }
        function a(e) {
          if (((n = []), e && e.length))
            for (let t = 0; t < e.length; t++) {
              let r = e[t];
              if (!r) continue;
              r.indexOf('http://') === 0
                ? (r = r.substring(7))
                : r.indexOf('https://') === 0 && (r = r.substring(8));
              const i = r.indexOf('/');
              let o; let a;
              i > 0
                ? ((o = r.substring(0, i)), (a = r.substring(i)))
                : ((o = r), (a = ''));
              const [s] = o.split(':');
              n.push({ hostname: s, pathname: a });
            }
        }
        function s(e, t) {
          return !(e.length > t.length) && t.indexOf(e) === t.length - e.length;
        }
        function c(e, t) {
          return (
            e.indexOf('/') === 0 && (e = e.substring(1)),
            t.indexOf('/') === 0 && (t = t.substring(1)),
            e === '' || e === t
          );
        }
      },
      3241: (e, t, r) => {
        
        r.d(t, { W: () => o });
        const n = r(6154);
        const i = 'newrelic';
        function o(e = {}) {
          try {
            n.gm.dispatchEvent(new CustomEvent(i, { detail: e }));
          } catch (e) {}
        }
      },
      1687: (e, t, r) => {
        
        r.d(t, { Ak: () => c, Ze: () => l, x3: () => u });
        const n = r(7836);
          const i = r(3606);
          const o = r(860);
          const a = r(2646);
        const s = {};
        function c(e, t) {
          const r = { staged: !1, priority: o.P3[t] || 0 };
          d(e), s[e].get(t) || s[e].set(t, r);
        }
        function u(e, t) {
          e &&
            s[e] &&
            (s[e].get(t) && s[e].delete(t), h(e, t, !1), s[e].size && f(e));
        }
        function d(e) {
          if (!e) throw new Error('agentIdentifier required');
          s[e] || (s[e] = new Map());
        }
        function l(e = '', t = 'feature', r = !1) {
          if ((d(e), !e || !s[e].get(t) || r)) return h(e, t);
          (s[e].get(t).staged = !0), f(e);
        }
        function f(e) {
          const t = Array.from(s[e]);
          t.every(([e, t]) => t.staged) &&
            (t.sort((e, t) => e[1].priority - t[1].priority),
            t.forEach(([t]) => {
              s[e].delete(t), h(e, t);
            }));
        }
        function h(e, t, r = !0) {
          const o = e ? n.ee.get(e) : n.ee;
            const s = i.i.handlers;
          if (!o.aborted && o.backlog && s) {
            if (r) {
              const e = o.backlog[t];
                const r = s[t];
              if (r) {
                for (let t = 0; e && t < e.length; ++t) p(e[t], r);
                Object.entries(r).forEach(([e, t]) => {
                  Object.values(t || {}).forEach((t) => {
                    t[0]?.on &&
                      t[0]?.context() instanceof a.y &&
                      t[0].on(e, t[1]);
                  });
                });
              }
            }
            o.isolatedBacklog || delete s[t],
              (o.backlog[t] = null),
              o.emit(`drain-${  t}`, []);
          }
        }
        function p(e, t) {
          const r = e[1];
          Object.values(t[r] || {}).forEach((t) => {
            const r = e[0];
            if (t[0] === r) {
              const n = t[1];
                const i = e[3];
                const o = e[2];
              n.apply(i, o);
            }
          });
        }
      },
      7836: (e, t, r) => {
        
        r.d(t, { P: () => s, ee: () => c });
        const n = r(384);
          const i = r(8990);
          const o = r(2646);
          const a = r(5607);
        const s = 'nr@context:'.concat(a.W);
          const c = (function e(t, r) {
            const n = {};
              const a = {};
              const d = {};
              let l = !1;
            try {
              l =
                r.length === 16 &&
                u.initializedAgents?.[r]?.runtime.isolatedBacklog;
            } catch (e) {}
            var f = {
              on: p,
              addEventListener: p,
              removeEventListener (e, t) {
                const r = n[e];
                if (!r) return;
                for (let i = 0; i < r.length; i++) r[i] === t && r.splice(i, 1);
              },
              emit (e, r, n, i, o) {
                !1 !== o && (o = !0);
                if (c.aborted && !i) return;
                t && o && t.emit(e, r, n);
                for (var s = h(n), u = g(e), d = u.length, l = 0; l < d; l++)
                  u[l].apply(s, r);
                const p = v()[a[e]];
                p && p.push([f, e, r, s]);
                return s;
              },
              get: m,
              listeners: g,
              context: h,
              buffer (e, t) {
                const r = v();
                if (((t = t || 'feature'), f.aborted)) return;
                Object.entries(e || {}).forEach(([e, n]) => {
                  (a[n] = t), t in r || (r[t] = []);
                });
              },
              abort () {
                (f._aborted = !0),
                  Object.keys(f.backlog).forEach((e) => {
                    delete f.backlog[e];
                  });
              },
              isBuffering (e) {
                return !!v()[a[e]];
              },
              debugId: r,
              backlog: l
                ? {}
                : t && typeof t.backlog === 'object'
                ? t.backlog
                : {},
              isolatedBacklog: l,
            };
            return (
              Object.defineProperty(f, 'aborted', {
                get: () => {
                  let e = f._aborted || !1;
                  return e || (t && (e = t.aborted), e);
                },
              }),
              f
            );
            function h(e) {
              return e && e instanceof o.y
                ? e
                : e
                ? (0, i.I)(e, s, () => new o.y(s))
                : new o.y(s);
            }
            function p(e, t) {
              n[e] = g(e).concat(t);
            }
            function g(e) {
              return n[e] || [];
            }
            function m(t) {
              return (d[t] = d[t] || e(f, t));
            }
            function v() {
              return f.backlog;
            }
          })(void 0, 'globalEE');
          const u = (0, n.Zm)();
        u.ee || (u.ee = c);
      },
      2646: (e, t, r) => {
        
        r.d(t, { y: () => n });
        class n {
          constructor(e) {
            this.contextId = e;
          }
        }
      },
      9908: (e, t, r) => {
        
        r.d(t, { d: () => n, p: () => i });
        var n = r(7836).ee.get('handle');
        function i(e, t, r, i, o) {
          o
            ? (o.buffer([e], i), o.emit(e, t, r))
            : (n.buffer([e], i), n.emit(e, t, r));
        }
      },
      3606: (e, t, r) => {
        
        r.d(t, { i: () => o });
        const n = r(9908);
        o.on = a;
        const i = (o.handlers = {});
        function o(e, t, r, o) {
          a(o || n.d, i, e, t, r);
        }
        function a(e, t, r, i, o) {
          o || (o = 'feature'), e || (e = n.d);
          const a = (t[o] = t[o] || {});
          (a[r] = a[r] || []).push([e, i]);
        }
      },
      3878: (e, t, r) => {
        
        function n(e, t) {
          return { capture: e, passive: !1, signal: t };
        }
        function i(e, t, r = !1, i) {
          window.addEventListener(e, t, n(r, i));
        }
        function o(e, t, r = !1, i) {
          document.addEventListener(e, t, n(r, i));
        }
        r.d(t, { DD: () => o, jT: () => n, sp: () => i });
      },
      5607: (e, t, r) => {
        
        r.d(t, { W: () => n });
        const n = (0, r(9566).bz)();
      },
      9566: (e, t, r) => {
        
        r.d(t, { LA: () => s, ZF: () => c, bz: () => a, el: () => u });
        const n = r(6154);
        const i = 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx';
        function o(e, t) {
          return e ? 15 & e[t] : (16 * Math.random()) | 0;
        }
        function a() {
          const e = n.gm?.crypto || n.gm?.msCrypto;
          let t;
            let r = 0;
          return (
            e &&
              e.getRandomValues &&
              (t = e.getRandomValues(new Uint8Array(30))),
            i
              .split('')
              .map((e) =>
                e === 'x'
                  ? o(t, r++).toString(16)
                  : e === 'y'
                  ? ((3 & o()) | 8).toString(16)
                  : e
              )
              .join('')
          );
        }
        function s(e) {
          const t = n.gm?.crypto || n.gm?.msCrypto;
          let r;
            let i = 0;
          t && t.getRandomValues && (r = t.getRandomValues(new Uint8Array(e)));
          const a = [];
          for (let s = 0; s < e; s++) a.push(o(r, i++).toString(16));
          return a.join('');
        }
        function c() {
          return s(16);
        }
        function u() {
          return s(32);
        }
      },
      2614: (e, t, r) => {
        
        r.d(t, {
          BB: () => a,
          H3: () => n,
          g: () => u,
          iL: () => c,
          tS: () => s,
          uh: () => i,
          wk: () => o,
        });
        const n = 'NRBA';
          const i = 'SESSION';
          const o = 144e5;
          const a = 18e5;
          const s = {
            STARTED: 'session-started',
            PAUSE: 'session-pause',
            RESET: 'session-reset',
            RESUME: 'session-resume',
            UPDATE: 'session-update',
          };
          const c = { SAME_TAB: 'same-tab', CROSS_TAB: 'cross-tab' };
          const u = { OFF: 0, FULL: 1, ERROR: 2 };
      },
      1863: (e, t, r) => {
        
        function n() {
          return Math.floor(performance.now());
        }
        r.d(t, { t: () => n });
      },
      7485: (e, t, r) => {
        
        r.d(t, { D: () => i });
        const n = r(6154);
        function i(e) {
          if ((e || '').indexOf('data:') === 0) return { protocol: 'data' };
          try {
            const t = new URL(e, location.href);
              const r = {
                port: t.port,
                hostname: t.hostname,
                pathname: t.pathname,
                search: t.search,
                protocol: t.protocol.slice(0, t.protocol.indexOf(':')),
                sameOrigin:
                  t.protocol === n.gm?.location?.protocol &&
                  t.host === n.gm?.location?.host,
              };
            return (
              (r.port && r.port !== '') ||
                (t.protocol === 'http:' && (r.port = '80'),
                t.protocol === 'https:' && (r.port = '443')),
              r.pathname && r.pathname !== ''
                ? r.pathname.startsWith('/') ||
                  (r.pathname = '/'.concat(r.pathname))
                : (r.pathname = '/'),
              r
            );
          } catch (e) {
            return {};
          }
        }
      },
      944: (e, t, r) => {
        
        function n(e, t) {
          typeof console.debug === 'function' &&
            console.debug(
              'New Relic Warning: https://github.com/newrelic/newrelic-browser-agent/blob/main/docs/warning-codes.md#'.concat(
                e
              ),
              t
            );
        }
        r.d(t, { R: () => n });
      },
      5701: (e, t, r) => {
        
        r.d(t, { B: () => o, t: () => a });
        const n = r(3241);
        const i = new Set();
          const o = {};
        function a(e, t) {
          const r = t.agentIdentifier;
          (o[r] ??= {}),
            e &&
              typeof e === 'object' &&
              (i.has(r) ||
                (t.ee.emit('rumresp', [e]),
                (o[r] = e),
                i.add(r),
                (0, n.W)({
                  agentIdentifier: r,
                  loaded: !0,
                  drained: !0,
                  type: 'lifecycle',
                  name: 'load',
                  feature: void 0,
                  data: e,
                })));
        }
      },
      8990: (e, t, r) => {
        
        r.d(t, { I: () => i });
        const n = Object.prototype.hasOwnProperty;
        function i(e, t, r) {
          if (n.call(e, t)) return e[t];
          const i = r();
          if (Object.defineProperty && Object.keys)
            try {
              return (
                Object.defineProperty(e, t, {
                  value: i,
                  writable: !0,
                  enumerable: !1,
                }),
                i
              );
            } catch (e) {}
          return (e[t] = i), i;
        }
      },
      6389: (e, t, r) => {
        
        function n(e, t = 500, r = {}) {
          const n = r?.leading || !1;
          let i;
          return (...r) => {
            n &&
              void 0 === i &&
              (e.apply(this, r),
              (i = setTimeout(() => {
                i = clearTimeout(i);
              }, t))),
              n ||
                (clearTimeout(i),
                (i = setTimeout(() => {
                  e.apply(this, r);
                }, t)));
          };
        }
        function i(e) {
          let t = !1;
          return (...r) => {
            t || ((t = !0), e.apply(this, r));
          };
        }
        r.d(t, { J: () => i, s: () => n });
      },
      3304: (e, t, r) => {
        
        r.d(t, { A: () => o });
        const n = r(7836);
        const i = () => {
          const e = new WeakSet();
          return (t, r) => {
            if (typeof r === 'object' && r !== null) {
              if (e.has(r)) return;
              e.add(r);
            }
            return r;
          };
        };
        function o(e) {
          try {
            return JSON.stringify(e, i()) ?? '';
          } catch (e) {
            try {
              n.ee.emit('internal-error', [e]);
            } catch (e) {}
            return '';
          }
        }
      },
      3496: (e, t, r) => {
        
        function n(e) {
          return !e || !(!e.licenseKey || !e.applicationID);
        }
        function i(e, t) {
          return (
            !e ||
            (e.licenseKey === t.info.licenseKey &&
              e.applicationID === t.info.applicationID)
          );
        }
        r.d(t, { A: () => i, I: () => n });
      },
      5289: (e, t, r) => {
        
        r.d(t, { GG: () => o, sB: () => a });
        const n = r(3878);
        function i() {
          return (
            typeof document === 'undefined' || document.readyState === 'complete'
          );
        }
        function o(e, t) {
          if (i()) return e();
          (0, n.sp)('load', e, t);
        }
        function a(e) {
          if (i()) return e();
          (0, n.DD)('DOMContentLoaded', e);
        }
      },
      384: (e, t, r) => {
        
        r.d(t, {
          NT: () => o,
          US: () => u,
          Zm: () => a,
          bQ: () => c,
          dV: () => s,
          pV: () => d,
        });
        const n = r(6154);
          const i = r(1863);
        const o = { beacon: 'bam.nr-data.net', errorBeacon: 'bam.nr-data.net' };
        function a() {
          return (
            n.gm.NREUM || (n.gm.NREUM = {}),
            void 0 === n.gm.newrelic && (n.gm.newrelic = n.gm.NREUM),
            n.gm.NREUM
          );
        }
        function s() {
          const e = a();
          return (
            e.o ||
              (e.o = {
                ST: n.gm.setTimeout,
                SI: n.gm.setImmediate,
                CT: n.gm.clearTimeout,
                XHR: n.gm.XMLHttpRequest,
                REQ: n.gm.Request,
                EV: n.gm.Event,
                PR: n.gm.Promise,
                MO: n.gm.MutationObserver,
                FETCH: n.gm.fetch,
                WS: n.gm.WebSocket,
              }),
            e
          );
        }
        function c(e, t) {
          const r = a();
          (r.initializedAgents ??= {}),
            (t.initializedAt = { ms: (0, i.t)(), date: new Date() }),
            (r.initializedAgents[e] = t);
        }
        function u(e, t) {
          a()[e] = t;
        }
        function d() {
          return (
            (function () {
              const e = a();
              const t = e.info || {};
              e.info = { beacon: o.beacon, errorBeacon: o.errorBeacon, ...t };
            })(),
            (function () {
              const e = a();
              const t = e.init || {};
              e.init = { ...t };
            })(),
            s(),
            (function () {
              const e = a();
              const t = e.loader_config || {};
              e.loader_config = { ...t };
            })(),
            a()
          );
        }
      },
      2843: (e, t, r) => {
        
        r.d(t, { u: () => i });
        const n = r(3878);
        function i(e, t = !1, r, i) {
          (0, n.DD)(
            'visibilitychange',
            () => {
              if (t) return void (document.visibilityState === 'hidden' && e());
              e(document.visibilityState);
            },
            r,
            i
          );
        }
      },
      8139: (e, t, r) => {
        
        r.d(t, { u: () => f });
        const n = r(7836);
          const i = r(3434);
          const o = r(8990);
          const a = r(6154);
        const s = {};
          const c = a.gm.XMLHttpRequest;
          const u = 'addEventListener';
          const d = 'removeEventListener';
          const l = 'nr@wrapped:'.concat(n.P);
        function f(e) {
          const t = (function (e) {
            return (e || n.ee).get('events');
          })(e);
          if (s[t.debugId]++) return t;
          s[t.debugId] = 1;
          const r = (0, i.YM)(t, !0);
          function f(e) {
            r.inPlace(e, [u, d], '-', p);
          }
          function p(e, t) {
            return e[1];
          }
          return (
            'getPrototypeOf' in Object &&
              (a.RI && h(document, f), c && h(c.prototype, f), h(a.gm, f)),
            t.on(`${u  }-start`, function (e, t) {
              const n = e[1];
              if (
                n !== null &&
                (typeof n === 'function' || typeof n === 'object')
              ) {
                const i = (0, o.I)(n, l, () => {
                  const e = {
                    object () {
                      if (typeof n.handleEvent !== 'function') return;
                      return n.handleEvent.apply(n, arguments);
                    },
                    function: n,
                  }[typeof n];
                  return e ? r(e, 'fn-', null, e.name || 'anonymous') : n;
                });
                this.wrapped = e[1] = i;
              }
            }),
            t.on(`${d  }-start`, function (e) {
              e[1] = this.wrapped || e[1];
            }),
            t
          );
        }
        function h(e, t, ...r) {
          let n = e;
          for (
            ;
            typeof n === 'object' && !Object.prototype.hasOwnProperty.call(n, u);

          )
            n = Object.getPrototypeOf(n);
          n && t(n, ...r);
        }
      },
      3434: (e, t, r) => {
        
        r.d(t, { Jt: () => o, YM: () => c });
        const n = r(7836);
          const i = r(5607);
        const o = 'nr@original:'.concat(i.W);
        const a = Object.prototype.hasOwnProperty;
          let s = !1;
        function c(e, t) {
          return (
            e || (e = n.ee),
            (r.inPlace = function (e, t, n, i, o) {
              n || (n = '');
              const a = n.charAt(0) === '-';
              for (let s = 0; s < t.length; s++) {
                const c = t[s];
                  const u = e[c];
                d(u) || (e[c] = r(u, a ? c + n : n, i, c, o));
              }
            }),
            (r.flag = o),
            r
          );
          function r(t, r, n, s, c) {
            return d(t)
              ? t
              : (r || (r = ''),
                (nrWrapper[o] = t),
                (function (e, t, r) {
                  if (Object.defineProperty && Object.keys)
                    try {
                      return (
                        Object.keys(e).forEach((r) => {
                          Object.defineProperty(t, r, {
                            get () {
                              return e[r];
                            },
                            set (t) {
                              return (e[r] = t), t;
                            },
                          });
                        }),
                        t
                      );
                    } catch (e) {
                      u([e], r);
                    }
                  for (const n in e) a.call(e, n) && (t[n] = e[n]);
                })(t, nrWrapper, e),
                nrWrapper);
            function nrWrapper() {
              let o; let a; let d; let l;
              try {
                (a = this),
                  (o = [...arguments]),
                  (d = typeof n === 'function' ? n(o, a) : n || {});
              } catch (t) {
                u([t, '', [o, a, s], d], e);
              }
              i(`${r  }start`, [o, a, s], d, c);
              try {
                return (l = t.apply(a, o));
              } catch (e) {
                throw (i(`${r  }err`, [o, a, e], d, c), e);
              } finally {
                i(`${r  }end`, [o, a, l], d, c);
              }
            }
          }
          function i(r, n, i, o) {
            if (!s || t) {
              const a = s;
              s = !0;
              try {
                e.emit(r, n, i, t, o);
              } catch (t) {
                u([t, r, n, i], e);
              }
              s = a;
            }
          }
        }
        function u(e, t) {
          t || (t = n.ee);
          try {
            t.emit('internal-error', e);
          } catch (e) {}
        }
        function d(e) {
          return !(e && typeof e === 'function' && e.apply && !e[o]);
        }
      },
      9300: (e, t, r) => {
        
        r.d(t, { T: () => n });
        const n = r(860).K7.ajax;
      },
      3333: (e, t, r) => {
        
        r.d(t, {
          $v: () => u,
          TZ: () => n,
          Zp: () => i,
          kd: () => c,
          mq: () => s,
          nf: () => a,
          qN: () => o,
        });
        const n = r(860).K7.genericEvents;
          const i = ['auxclick', 'click', 'copy', 'keydown', 'paste', 'scrollend'];
          const o = ['focus', 'blur'];
          const a = 4;
          const s = 1e3;
          const c = ['PageAction', 'UserAction', 'BrowserPerformance'];
          const u = {
            MARKS: 'experimental.marks',
            MEASURES: 'experimental.measures',
            RESOURCES: 'experimental.resources',
          };
      },
      6774: (e, t, r) => {
        
        r.d(t, { T: () => n });
        const n = r(860).K7.jserrors;
      },
      993: (e, t, r) => {
        
        r.d(t, { A$: () => o, ET: () => a, TZ: () => s, p_: () => i });
        const n = r(860);
        const i = {
            ERROR: 'ERROR',
            WARN: 'WARN',
            INFO: 'INFO',
            DEBUG: 'DEBUG',
            TRACE: 'TRACE',
          };
          const o = { OFF: 0, ERROR: 1, WARN: 2, INFO: 3, DEBUG: 4, TRACE: 5 };
          const a = 'log';
          const s = n.K7.logging;
      },
      3785: (e, t, r) => {
        
        r.d(t, { R: () => c, b: () => u });
        const n = r(9908);
          const i = r(1863);
          const o = r(860);
          const a = r(8154);
          const s = r(993);
        function c(e, t, r = {}, c = s.p_.INFO, u, d = (0, i.t)()) {
          (0, n.p)(
            a.xV,
            ['API/logging/'.concat(c.toLowerCase(), '/called')],
            void 0,
            o.K7.metrics,
            e
          ),
            (0, n.p)(s.ET, [d, t, r, c, u], void 0, o.K7.logging, e);
        }
        function u(e) {
          return (
            typeof e === 'string' &&
            Object.values(s.p_).some((t) => t === e.toUpperCase().trim())
          );
        }
      },
      8154: (e, t, r) => {
        
        r.d(t, {
          z_: () => o,
          XG: () => s,
          TZ: () => n,
          rs: () => i,
          xV: () => a,
        });
        r(6154), r(9566), r(384);
        const n = r(860).K7.metrics;
          const i = 'sm';
          const o = 'cm';
          const a = 'storeSupportabilityMetrics';
          const s = 'storeEventMetrics';
      },
      6630: (e, t, r) => {
        
        r.d(t, { T: () => n });
        const n = r(860).K7.pageViewEvent;
      },
      782: (e, t, r) => {
        
        r.d(t, { T: () => n });
        const n = r(860).K7.pageViewTiming;
      },
      6344: (e, t, r) => {
        
        r.d(t, {
          BB: () => d,
          G4: () => o,
          Qb: () => l,
          TZ: () => i,
          Ug: () => a,
          _s: () => s,
          bc: () => u,
          yP: () => c,
        });
        const n = r(2614);
        const i = r(860).K7.sessionReplay;
          const o = {
            RECORD: 'recordReplay',
            PAUSE: 'pauseReplay',
            ERROR_DURING_REPLAY: 'errorDuringReplay',
          };
          const a = 0.12;
          const s = {
            DomContentLoaded: 0,
            Load: 1,
            FullSnapshot: 2,
            IncrementalSnapshot: 3,
            Meta: 4,
            Custom: 5,
          };
          const c = { [n.g.ERROR]: 15e3, [n.g.FULL]: 3e5, [n.g.OFF]: 0 };
          const u = {
            RESET: { message: 'Session was reset', sm: 'Reset' },
            IMPORT: { message: 'Recorder failed to import', sm: 'Import' },
            TOO_MANY: { message: '429: Too Many Requests', sm: 'Too-Many' },
            TOO_BIG: { message: 'Payload was too large', sm: 'Too-Big' },
            CROSS_TAB: {
              message: 'Session Entity was set to OFF on another tab',
              sm: 'Cross-Tab',
            },
            ENTITLEMENTS: {
              message: 'Session Replay is not allowed and will not be started',
              sm: 'Entitlement',
            },
          };
          const d = 5e3;
          const l = { API: 'api' };
      },
      5270: (e, t, r) => {
        
        r.d(t, { Aw: () => s, CT: () => c, SR: () => a, rF: () => u });
        const n = r(384);
          const i = r(7767);
          const o = r(6154);
        function a(e) {
          return (
            !!(0, n.dV)().o.MO && (0, i.V)(e) && !0 === e?.session_trace.enabled
          );
        }
        function s(e) {
          return !0 === e?.session_replay.preload && a(e);
        }
        function c(e, t) {
          const r = t.correctAbsoluteTimestamp(e);
          return {
            originalTimestamp: e,
            correctedTimestamp: r,
            timestampDiff: e - r,
            originTime: o.WN,
            correctedOriginTime: t.correctedOriginTime,
            originTimeDiff: Math.floor(o.WN - t.correctedOriginTime),
          };
        }
        function u(e, t) {
          try {
            if (typeof t?.type === 'string') {
              if (t.type.toLowerCase() === 'password')
                return '*'.repeat(e?.length || 0);
              if (
                void 0 !== t?.dataset?.nrUnmask ||
                t?.classList?.contains('nr-unmask')
              )
                return e;
            }
          } catch (e) {}
          return typeof e === 'string'
            ? e.replace(/[\S]/g, '*')
            : '*'.repeat(e?.length || 0);
        }
      },
      3738: (e, t, r) => {
        
        r.d(t, {
          He: () => i,
          Kp: () => s,
          Lc: () => u,
          Rz: () => d,
          TZ: () => n,
          bD: () => o,
          d3: () => a,
          jx: () => l,
          uP: () => c,
        });
        const n = r(860).K7.sessionTrace;
          const i = 'bstResource';
          const o = 'resource';
          const a = '-start';
          const s = '-end';
          const c = `fn${  a}`;
          const u = `fn${  s}`;
          const d = 'pushState';
          const l = 1e3;
      },
      3962: (e, t, r) => {
        
        r.d(t, {
          AM: () => o,
          O2: () => c,
          Qu: () => u,
          TZ: () => s,
          ih: () => d,
          pP: () => a,
          tC: () => i,
        });
        const n = r(860);
        const i = ['click', 'keydown', 'submit', 'popstate'];
          const o = 'api';
          const a = 'initialPageLoad';
          const s = n.K7.softNav;
          const c = { INITIAL_PAGE_LOAD: '', ROUTE_CHANGE: 1, UNSPECIFIED: 2 };
          const u = { INTERACTION: 1, AJAX: 2, CUSTOM_END: 3, CUSTOM_TRACER: 4 };
          const d = { IP: 'in progress', FIN: 'finished', CAN: 'cancelled' };
      },
      7378: (e, t, r) => {
        
        r.d(t, {
          $p: () => x,
          BR: () => b,
          Kp: () => w,
          L3: () => y,
          Lc: () => c,
          NC: () => o,
          SG: () => d,
          TZ: () => i,
          U6: () => p,
          UT: () => m,
          d3: () => R,
          dT: () => f,
          e5: () => A,
          gx: () => v,
          l9: () => l,
          oW: () => h,
          op: () => g,
          rw: () => u,
          tH: () => E,
          uP: () => s,
          wW: () => T,
          xq: () => a,
        });
        const n = r(384);
        const i = r(860).K7.spa;
          const o = ['click', 'submit', 'keypress', 'keydown', 'keyup', 'change'];
          const a = 999;
          const s = 'fn-start';
          const c = 'fn-end';
          const u = 'cb-start';
          const d = 'api-ixn-';
          const l = 'remaining';
          const f = 'interaction';
          const h = 'spaNode';
          const p = 'jsonpNode';
          const g = 'fetch-start';
          const m = 'fetch-done';
          const v = 'fetch-body-';
          const b = 'jsonp-end';
          const y = (0, n.dV)().o.ST;
          const R = '-start';
          const w = '-end';
          const x = '-body';
          const T = `cb${  w}`;
          const A = 'jsTime';
          const E = 'fetch';
      },
      4234: (e, t, r) => {
        
        r.d(t, { W: () => o });
        const n = r(7836);
          const i = r(1687);
        class o {
          constructor(e, t) {
            (this.agentIdentifier = e),
              (this.ee = n.ee.get(e)),
              (this.featureName = t),
              (this.blocked = !1);
          }

          deregisterDrain() {
            (0, i.x3)(this.agentIdentifier, this.featureName);
          }
        }
      },
      7767: (e, t, r) => {
        
        r.d(t, { V: () => i });
        const n = r(6154);
        const i = (e) => n.RI && !0 === e?.privacy.cookies_enabled;
      },
      1741: (e, t, r) => {
        
        r.d(t, { W: () => o });
        const n = r(944);
          const i = r(4261);
        class o {
          #e(e, ...t) {
            if (this[e] !== o.prototype[e]) return this[e](...t);
            (0, n.R)(35, e);
          }

          addPageAction(e, t) {
            return this.#e(i.hG, e, t);
          }

          register(e) {
            return this.#e(i.eY, e);
          }

          recordCustomEvent(e, t) {
            return this.#e(i.fF, e, t);
          }

          setPageViewName(e, t) {
            return this.#e(i.Fw, e, t);
          }

          setCustomAttribute(e, t, r) {
            return this.#e(i.cD, e, t, r);
          }

          noticeError(e, t) {
            return this.#e(i.o5, e, t);
          }

          setUserId(e) {
            return this.#e(i.Dl, e);
          }

          setApplicationVersion(e) {
            return this.#e(i.nb, e);
          }

          setErrorHandler(e) {
            return this.#e(i.bt, e);
          }

          addRelease(e, t) {
            return this.#e(i.k6, e, t);
          }

          log(e, t) {
            return this.#e(i.$9, e, t);
          }

          start() {
            return this.#e(i.d3);
          }

          finished(e) {
            return this.#e(i.BL, e);
          }

          recordReplay() {
            return this.#e(i.CH);
          }

          pauseReplay() {
            return this.#e(i.Tb);
          }

          addToTrace(e) {
            return this.#e(i.U2, e);
          }

          setCurrentRouteName(e) {
            return this.#e(i.PA, e);
          }

          interaction() {
            return this.#e(i.dT);
          }

          wrapLogger(e, t, r) {
            return this.#e(i.Wb, e, t, r);
          }
        }
      },
      4261: (e, t, r) => {
        
        r.d(t, {
          $9: () => d,
          BL: () => c,
          CH: () => p,
          Dl: () => w,
          Fw: () => R,
          PA: () => v,
          Pl: () => n,
          Tb: () => f,
          U2: () => a,
          Wb: () => T,
          bt: () => y,
          cD: () => b,
          d3: () => x,
          dT: () => u,
          eY: () => g,
          fF: () => h,
          hG: () => o,
          hw: () => i,
          k6: () => s,
          nb: () => m,
          o5: () => l,
        });
        const n = 'api-';
          const i = `${n  }ixn-`;
          const o = 'addPageAction';
          const a = 'addToTrace';
          const s = 'addRelease';
          const c = 'finished';
          const u = 'interaction';
          const d = 'log';
          const l = 'noticeError';
          const f = 'pauseReplay';
          const h = 'recordCustomEvent';
          const p = 'recordReplay';
          const g = 'register';
          const m = 'setApplicationVersion';
          const v = 'setCurrentRouteName';
          const b = 'setCustomAttribute';
          const y = 'setErrorHandler';
          const R = 'setPageViewName';
          const w = 'setUserId';
          const x = 'start';
          const T = 'wrapLogger';
      },
      5205: (e, t, r) => {
        
        r.d(t, { j: () => S });
        const n = r(384);
          const i = r(1741);
        const o = r(2555);
          const a = r(3333);
        const s = (e) => {
          if (!e || typeof e !== 'string') return !1;
          try {
            document.createDocumentFragment().querySelector(e);
          } catch {
            return !1;
          }
          return !0;
        };
        const c = r(2614);
          const u = r(944);
          const d = r(8122);
        const l = '[data-nr-mask]';
          const f = (e) =>
            (0, d.a)(
              e,
              (() => {
                const e = {
                  feature_flags: [],
                  experimental: { marks: !1, measures: !1, resources: !1 },
                  mask_selector: '*',
                  block_selector: '[data-nr-block]',
                  mask_input_options: {
                    color: !1,
                    date: !1,
                    'datetime-local': !1,
                    email: !1,
                    month: !1,
                    number: !1,
                    range: !1,
                    search: !1,
                    tel: !1,
                    text: !1,
                    time: !1,
                    url: !1,
                    week: !1,
                    textarea: !1,
                    select: !1,
                    password: !0,
                  },
                };
                return {
                  ajax: {
                    deny_list: void 0,
                    block_internal: !0,
                    enabled: !0,
                    autoStart: !0,
                  },
                  api: {
                    allow_registered_children: !0,
                    duplicate_registered_data: !1,
                  },
                  distributed_tracing: {
                    enabled: void 0,
                    exclude_newrelic_header: void 0,
                    cors_use_newrelic_header: void 0,
                    cors_use_tracecontext_headers: void 0,
                    allowed_origins: void 0,
                  },
                  get feature_flags() {
                    return e.feature_flags;
                  },
                  set feature_flags(t) {
                    e.feature_flags = t;
                  },
                  generic_events: { enabled: !0, autoStart: !0 },
                  harvest: { interval: 30 },
                  jserrors: { enabled: !0, autoStart: !0 },
                  logging: { enabled: !0, autoStart: !0 },
                  metrics: { enabled: !0, autoStart: !0 },
                  obfuscate: void 0,
                  page_action: { enabled: !0 },
                  page_view_event: { enabled: !0, autoStart: !0 },
                  page_view_timing: { enabled: !0, autoStart: !0 },
                  performance: {
                    get capture_marks() {
                      return (
                        e.feature_flags.includes(a.$v.MARKS) ||
                        e.experimental.marks
                      );
                    },
                    set capture_marks(t) {
                      e.experimental.marks = t;
                    },
                    get capture_measures() {
                      return (
                        e.feature_flags.includes(a.$v.MEASURES) ||
                        e.experimental.measures
                      );
                    },
                    set capture_measures(t) {
                      e.experimental.measures = t;
                    },
                    capture_detail: !0,
                    resources: {
                      get enabled() {
                        return (
                          e.feature_flags.includes(a.$v.RESOURCES) ||
                          e.experimental.resources
                        );
                      },
                      set enabled(t) {
                        e.experimental.resources = t;
                      },
                      asset_types: [],
                      first_party_domains: [],
                      ignore_newrelic: !0,
                    },
                  },
                  privacy: { cookies_enabled: !0 },
                  proxy: { assets: void 0, beacon: void 0 },
                  session: { expiresMs: c.wk, inactiveMs: c.BB },
                  session_replay: {
                    autoStart: !0,
                    enabled: !1,
                    preload: !1,
                    sampling_rate: 10,
                    error_sampling_rate: 100,
                    collect_fonts: !1,
                    inline_images: !1,
                    fix_stylesheets: !0,
                    mask_all_inputs: !0,
                    get mask_text_selector() {
                      return e.mask_selector;
                    },
                    set mask_text_selector(t) {
                      s(t)
                        ? (e.mask_selector = ''.concat(t, ',').concat(l))
                        : t === '' || t === null
                        ? (e.mask_selector = l)
                        : (0, u.R)(5, t);
                    },
                    get block_class() {
                      return 'nr-block';
                    },
                    get ignore_class() {
                      return 'nr-ignore';
                    },
                    get mask_text_class() {
                      return 'nr-mask';
                    },
                    get block_selector() {
                      return e.block_selector;
                    },
                    set block_selector(t) {
                      s(t)
                        ? (e.block_selector += ','.concat(t))
                        : t !== '' && (0, u.R)(6, t);
                    },
                    get mask_input_options() {
                      return e.mask_input_options;
                    },
                    set mask_input_options(t) {
                      t && typeof t === 'object'
                        ? (e.mask_input_options = { ...t, password: !0 })
                        : (0, u.R)(7, t);
                    },
                  },
                  session_trace: { enabled: !0, autoStart: !0 },
                  soft_navigations: { enabled: !0, autoStart: !0 },
                  spa: { enabled: !0, autoStart: !0 },
                  ssl: void 0,
                  user_actions: {
                    enabled: !0,
                    elementAttributes: ['id', 'className', 'tagName', 'type'],
                  },
                };
              })()
            );
        const h = r(6154);
          const p = r(9324);
        let g = 0;
        const m = {
            buildEnv: p.F3,
            distMethod: p.Xs,
            version: p.xv,
            originTime: h.WN,
          };
          const v = {
            appMetadata: {},
            customTransaction: void 0,
            denyList: void 0,
            disabled: !1,
            entityManager: void 0,
            harvester: void 0,
            isolatedBacklog: !1,
            isRecording: !1,
            loaderType: void 0,
            maxBytes: 3e4,
            obfuscator: void 0,
            onerror: void 0,
            ptid: void 0,
            releaseIds: {},
            session: void 0,
            timeKeeper: void 0,
            get harvestCount() {
              return ++g;
            },
          };
          const b = (e) => {
            const t = (0, d.a)(e, v);
              const r = Object.keys(m).reduce(
                (e, t) => (
                  (e[t] = {
                    value: m[t],
                    writable: !1,
                    configurable: !0,
                    enumerable: !0,
                  }),
                  e
                ),
                {}
              );
            return Object.defineProperties(t, r);
          };
        const y = r(5701);
        const R = (e) => {
          const t = e.startsWith('http');
          (e += '/'), (r.p = t ? e : `https://${  e}`);
        };
        const w = r(7836);
          const x = r(3241);
        const T = {
            accountID: void 0,
            trustKey: void 0,
            agentID: void 0,
            licenseKey: void 0,
            applicationID: void 0,
            xpid: void 0,
          };
          const A = (e) => (0, d.a)(e, T);
          const E = new Set();
        function S(e, t = {}, r, a) {
          let {
            init: s,
            info: c,
            loader_config: u,
            runtime: d = {},
            exposed: l = !0,
          } = t;
          if (!c) {
            const e = (0, n.pV)();
            (s = e.init), (c = e.info), (u = e.loader_config);
          }
          (e.init = f(s || {})),
            (e.loader_config = A(u || {})),
            (c.jsAttributes ??= {}),
            h.bv && (c.jsAttributes.isWorker = !0),
            (e.info = (0, o.D)(c));
          const p = e.init;
            const g = [c.beacon, c.errorBeacon];
          E.has(e.agentIdentifier) ||
            (p.proxy.assets && (R(p.proxy.assets), g.push(p.proxy.assets)),
            p.proxy.beacon && g.push(p.proxy.beacon),
            (function (e) {
              const t = (0, n.pV)();
              Object.getOwnPropertyNames(i.W.prototype).forEach((r) => {
                const n = i.W.prototype[r];
                if (typeof n !== 'function' || n === 'constructor') return;
                const o = t[r];
                e[r] &&
                  !1 !== e.exposed &&
                  e.runtime?.loaderType !== 'micro-agent' &&
                  (t[r] = (...t) => {
                    const n = e[r](...t);
                    return o ? o(...t) : n;
                  });
              });
            })(e),
            (0, n.US)('activatedFeatures', y.B),
            (e.runSoftNavOverSpa &&=
              !0 === p.soft_navigations.enabled &&
              p.feature_flags.includes('soft_nav'))),
            (d.denyList = [
              ...(p.ajax.deny_list || []),
              ...(p.ajax.block_internal ? g : []),
            ]),
            (d.ptid = e.agentIdentifier),
            (d.loaderType = r),
            (e.runtime = b(d)),
            E.has(e.agentIdentifier) ||
              ((e.ee = w.ee.get(e.agentIdentifier)),
              (e.exposed = l),
              (0, x.W)({
                agentIdentifier: e.agentIdentifier,
                drained: !!y.B?.[e.agentIdentifier],
                type: 'lifecycle',
                name: 'initialize',
                feature: void 0,
                data: e.config,
              })),
            E.add(e.agentIdentifier);
        }
      },
      8374: (e, t, r) => {
        r.nc = (() => {
          try {
            return document?.currentScript?.nonce;
          } catch (e) {}
          return '';
        })();
      },
      860: (e, t, r) => {
        
        r.d(t, {
          $J: () => d,
          K7: () => c,
          P3: () => u,
          XX: () => i,
          Yy: () => s,
          df: () => o,
          qY: () => n,
          v4: () => a,
        });
        const n = 'events';
          const i = 'jserrors';
          const o = 'browser/blobs';
          const a = 'rum';
          const s = 'browser/logs';
          const c = {
            ajax: 'ajax',
            genericEvents: 'generic_events',
            jserrors: i,
            logging: 'logging',
            metrics: 'metrics',
            pageAction: 'page_action',
            pageViewEvent: 'page_view_event',
            pageViewTiming: 'page_view_timing',
            sessionReplay: 'session_replay',
            sessionTrace: 'session_trace',
            softNav: 'soft_navigations',
            spa: 'spa',
          };
          const u = {
            [c.pageViewEvent]: 1,
            [c.pageViewTiming]: 2,
            [c.metrics]: 3,
            [c.jserrors]: 4,
            [c.spa]: 5,
            [c.ajax]: 6,
            [c.sessionTrace]: 7,
            [c.softNav]: 8,
            [c.sessionReplay]: 9,
            [c.logging]: 10,
            [c.genericEvents]: 11,
          };
          const d = {
            [c.pageViewEvent]: a,
            [c.pageViewTiming]: n,
            [c.ajax]: n,
            [c.spa]: n,
            [c.softNav]: n,
            [c.metrics]: i,
            [c.jserrors]: i,
            [c.sessionTrace]: o,
            [c.sessionReplay]: o,
            [c.logging]: s,
            [c.genericEvents]: 'ins',
          };
      },
    };
    const n = {};
  function i(e) {
    const t = n[e];
    if (void 0 !== t) return t.exports;
    const o = (n[e] = { exports: {} });
    return r[e](o, o.exports, i), o.exports;
  }
  (i.m = r),
    (i.d = (e, t) => {
      for (const r in t)
        i.o(t, r) &&
          !i.o(e, r) &&
          Object.defineProperty(e, r, { enumerable: !0, get: t[r] });
    }),
    (i.f = {}),
    (i.e = (e) =>
      Promise.all(Object.keys(i.f).reduce((t, r) => (i.f[r](e, t), t), []))),
    (i.u = (e) =>
      (`${{ 212: 'nr-spa-compressor', 249: 'nr-spa-recorder', 478: 'nr-spa' }[e] 
      }-1.290.1.min.js`)),
    (i.o = (e, t) => Object.prototype.hasOwnProperty.call(e, t)),
    (e = {}),
    (t = 'NRBA-1.290.1.PROD:'),
    (i.l = (r, n, o, a) => {
      if (e[r]) e[r].push(n);
      else {
        let s; let c;
        if (void 0 !== o)
          for (
            let u = document.getElementsByTagName('script'), d = 0;
            d < u.length;
            d++
          ) {
            const l = u[d];
            if (
              l.getAttribute('src') == r ||
              l.getAttribute('data-webpack') == t + o
            ) {
              s = l;
              break;
            }
          }
        if (!s) {
          c = !0;
          const f = {
            478: 'sha512-bYJwHMoCCpXDQfA5hvSei4HwsEoHDE8P9QRCyoqJdWe/2X7uN+05p4AkgHkI681KTscP9+8UtsRVsg9W8qU3+w==',
            249: 'sha512-W+68kymoEsNmLnSyqERiMF4DEGMf+tp1fTdyhFX0uE7hme+zt4z4K3WGpyfAHm9839IMwbWgNOZdjj/tCa0dzw==',
            212: 'sha512-DnvL4wRJBl0T6iraWz6wD4Wn61762wIk7+UCZ1DPaHfzyQ63fWYb54YLJUck6S09BpYxBwWOvmQk0qBBGqiwTQ==',
          };
          ((s = document.createElement('script')).charset = 'utf-8'),
            (s.timeout = 120),
            i.nc && s.setAttribute('nonce', i.nc),
            s.setAttribute('data-webpack', t + o),
            (s.src = r),
            s.src.indexOf(`${window.location.origin  }/`) !== 0 &&
              (s.crossOrigin = 'anonymous'),
            f[a] && (s.integrity = f[a]);
        }
        e[r] = [n];
        const h = (t, n) => {
            (s.onerror = s.onload = null), clearTimeout(p);
            const i = e[r];
            if (
              (delete e[r],
              s.parentNode && s.parentNode.removeChild(s),
              i && i.forEach((e) => e(n)),
              t)
            )
              return t(n);
          };
          var p = setTimeout(
            h.bind(null, void 0, { type: 'timeout', target: s }),
            12e4
          );
        (s.onerror = h.bind(null, s.onerror)),
          (s.onload = h.bind(null, s.onload)),
          c && document.head.appendChild(s);
      }
    }),
    (i.r = (e) => {
      typeof Symbol !== 'undefined' &&
        Symbol.toStringTag &&
        Object.defineProperty(e, Symbol.toStringTag, { value: 'Module' }),
        Object.defineProperty(e, '__esModule', { value: !0 });
    }),
    (i.p = 'https://js-agent.newrelic.com/'),
    (() => {
      const e = { 38: 0, 788: 0 };
      i.f.j = (t, r) => {
        let n = i.o(e, t) ? e[t] : void 0;
        if (n !== 0)
          if (n) r.push(n[2]);
          else {
            const o = new Promise((r, i) => (n = e[t] = [r, i]));
            r.push((n[2] = o));
            const a = i.p + i.u(t);
              const s = new Error();
            i.l(
              a,
              (r) => {
                if (i.o(e, t) && ((n = e[t]) !== 0 && (e[t] = void 0), n)) {
                  const o = r && (r.type === 'load' ? 'missing' : r.type);
                    const a = r && r.target && r.target.src;
                  (s.message =
                    `Loading chunk ${  t  } failed.\n(${  o  }: ${  a  })`),
                    (s.name = 'ChunkLoadError'),
                    (s.type = o),
                    (s.request = a),
                    n[1](s);
                }
              },
              `chunk-${  t}`,
              t
            );
          }
      };
      const t = (t, r) => {
          let n;
            let o;
            const [a, s, c] = r;
            let u = 0;
          if (a.some((t) => e[t] !== 0)) {
            for (n in s) i.o(s, n) && (i.m[n] = s[n]);
            if (c) c(i);
          }
          for (t && t(r); u < a.length; u++)
            (o = a[u]), i.o(e, o) && e[o] && e[o][0](), (e[o] = 0);
        };
        const r = (self['webpackChunk:NRBA-1.290.1.PROD'] =
          self['webpackChunk:NRBA-1.290.1.PROD'] || []);
      r.forEach(t.bind(null, 0)), (r.push = t.bind(null, r.push.bind(r)));
    })(),
    (() => {
      
      i(8374);
      const e = i(9566);
        const t = i(1741);
      class r extends t.W {
        agentIdentifier = (0, e.LA)(16);
      }
      const n = i(860);
      const o = Object.values(n.K7);
      const a = i(5205);
      const s = i(9908);
        const c = i(1863);
        const u = i(4261);
        const d = i(3241);
        const l = i(944);
        const f = i(5701);
        const h = i(8154);
      function p(e, t, i, o) {
        const a = o || i;
        !a ||
          (a[e] && a[e] !== r.prototype[e]) ||
          (a[e] = function () {
            (0, s.p)(
              h.xV,
              [`API/${  e  }/called`],
              void 0,
              n.K7.metrics,
              i.ee
            ),
              (0, d.W)({
                agentIdentifier: i.agentIdentifier,
                drained: !!f.B?.[i.agentIdentifier],
                type: 'data',
                name: 'api',
                feature: u.Pl + e,
                data: {},
              });
            try {
              return t.apply(this, arguments);
            } catch (e) {
              (0, l.R)(23, e);
            }
          });
      }
      function g(e, t, r, n, i) {
        const o = e.info;
        r === null ? delete o.jsAttributes[t] : (o.jsAttributes[t] = r),
          (i || r === null) &&
            (0, s.p)(u.Pl + n, [(0, c.t)(), t, r], void 0, 'session', e.ee);
      }
      const m = i(1687);
        const v = i(4234);
        const b = i(5289);
        const y = i(6154);
        const R = i(5270);
        const w = i(7767);
        const x = i(6389);
      class T extends v.W {
        constructor(e, t) {
          super(e.agentIdentifier, t),
            (this.abortHandler = void 0),
            (this.featAggregate = void 0),
            (this.onAggregateImported = void 0),
            (this.deferred = Promise.resolve()),
            !1 === e.init[this.featureName].autoStart
              ? (this.deferred = new Promise((t, r) => {
                  this.ee.on(
                    'manual-start-all',
                    (0, x.J)(() => {
                      (0, m.Ak)(e.agentIdentifier, this.featureName), t();
                    })
                  );
                }))
              : (0, m.Ak)(e.agentIdentifier, t);
        }

        importAggregator(e, t, r = {}) {
          if (this.featAggregate) return;
          let o;
          this.onAggregateImported = new Promise((e) => {
            o = e;
          });
          const a = async () => {
            let a;
            await this.deferred;
            try {
              if ((0, w.V)(e.init)) {
                const { setupAgentSession: t } = await i
                  .e(478)
                  .then(i.bind(i, 6526));
                a = t(e);
              }
            } catch (e) {
              (0, l.R)(20, e),
                this.ee.emit('internal-error', [e]),
                this.featureName === n.K7.sessionReplay &&
                  this.abortHandler?.();
            }
            try {
              if (!this.#t(this.featureName, a, e.init))
                return (
                  (0, m.Ze)(this.agentIdentifier, this.featureName), void o(!1)
                );
              const { Aggregate: n } = await t();
              (this.featAggregate = new n(e, r)),
                e.runtime.harvester.initializedAggregates.push(
                  this.featAggregate
                ),
                o(!0);
            } catch (e) {
              (0, l.R)(34, e),
                this.abortHandler?.(),
                (0, m.Ze)(this.agentIdentifier, this.featureName, !0),
                o(!1),
                this.ee && this.ee.abort();
            }
          };
          y.RI ? (0, b.GG)(() => a(), !0) : a();
        }

        #t(e, t, r) {
          switch (e) {
            case n.K7.sessionReplay:
              return (0, R.SR)(r) && !!t;
            case n.K7.sessionTrace:
              return !!t;
            default:
              return !0;
          }
        }
      }
      const A = i(6630);
      class E extends T {
        static featureName = A.T;

        constructor(e) {
          let t;
          super(e, A.T),
            (t = e),
            p(
              u.Fw,
              (e, r) => {
                typeof e === 'string' &&
                  (e.charAt(0) !== '/' && (e = `/${  e}`),
                  (t.runtime.customTransaction =
                    (r || 'http://custom.transaction') + e),
                  (0, s.p)(u.Pl + u.Fw, [(0, c.t)()], void 0, void 0, t.ee));
              },
              t
            ),
            this.ee.on('api-send-rum', (e, t) =>
              (0, s.p)('send-rum', [e, t], void 0, this.featureName, this.ee)
            ),
            this.importAggregator(e, () => i.e(478).then(i.bind(i, 1983)));
        }
      }
      const S = i(384);
      const _ = i(2843);
        const O = i(3878);
        const N = i(782);
      class P extends T {
        static featureName = N.T;

        constructor(e) {
          super(e, N.T),
            y.RI &&
              ((0, _.u)(
                () => (0, s.p)('docHidden', [(0, c.t)()], void 0, N.T, this.ee),
                !0
              ),
              (0, O.sp)('pagehide', () =>
                (0, s.p)('winPagehide', [(0, c.t)()], void 0, N.T, this.ee)
              ),
              this.importAggregator(e, () => i.e(478).then(i.bind(i, 9917))));
        }
      }
      class I extends T {
        static featureName = h.TZ;

        constructor(e) {
          super(e, h.TZ),
            y.RI &&
              document.addEventListener('securitypolicyviolation', (e) => {
                (0, s.p)(
                  h.xV,
                  ['Generic/CSPViolation/Detected'],
                  void 0,
                  this.featureName,
                  this.ee
                );
              }),
            this.importAggregator(e, () => i.e(478).then(i.bind(i, 8351)));
        }
      }
      const j = i(6774);
        const C = i(3304);
      class k {
        constructor(e, t, r, n, i) {
          (this.name = 'UncaughtError'),
            (this.message = typeof e === 'string' ? e : (0, C.A)(e)),
            (this.sourceURL = t),
            (this.line = r),
            (this.column = n),
            (this.__newrelic = i);
        }
      }
      function L(e) {
        return D(e)
          ? e
          : new k(
              void 0 !== e?.message ? e.message : e,
              e?.filename || e?.sourceURL,
              e?.lineno || e?.line,
              e?.colno || e?.col,
              e?.__newrelic
            );
      }
      function H(e) {
        const t = 'Unhandled Promise Rejection: ';
        if (!e?.reason) return;
        if (D(e.reason)) {
          try {
            e.reason.message.startsWith(t) ||
              (e.reason.message = t + e.reason.message);
          } catch (e) {}
          return L(e.reason);
        }
        const r = L(e.reason);
        return (
          (r.message || '').startsWith(t) || (r.message = t + r.message), r
        );
      }
      function M(e) {
        if (
          e.error instanceof SyntaxError &&
          !/:\d+$/.test(e.error.stack?.trim())
        ) {
          const t = new k(
            e.message,
            e.filename,
            e.lineno,
            e.colno,
            e.error.__newrelic
          );
          return (t.name = SyntaxError.name), t;
        }
        return D(e.error) ? e.error : L(e);
      }
      function D(e) {
        return e instanceof Error && !!e.stack;
      }
      function K(e, t, r, i, o = (0, c.t)()) {
        typeof e === 'string' && (e = new Error(e)),
          (0, s.p)(
            'err',
            [e, o, !1, t, r.runtime.isRecording, void 0, i],
            void 0,
            n.K7.jserrors,
            r.ee
          );
      }
      const U = i(3496);
        const F = i(993);
        const B = i(3785);
      function W(
        e,
        { customAttributes: t = {}, level: r = F.p_.INFO } = {},
        n,
        i,
        o = (0, c.t)()
      ) {
        (0, B.R)(n.ee, e, t, r, i, o);
      }
      function G(e, t, r, i, o = (0, c.t)()) {
        (0, s.p)(u.Pl + u.hG, [o, e, t, i], void 0, n.K7.genericEvents, r.ee);
      }
      function V(e) {
        p(
          u.eY,
          (t) => (function (e, t) {
              const r = {};
              let i; let o;
              (0, l.R)(54, 'newrelic.register'),
                e.init.api.allow_registered_children ||
                  (i = () => (0, l.R)(55));
              (t && (0, U.I)(t)) || (i = () => (0, l.R)(48, t));
              const a = {
                addPageAction: (n, i = {}) => {
                  u(G, [n, { ...r, ...i }, e], t);
                },
                log: (n, i = {}) => {
                  u(
                    W,
                    [
                      n,
                      {
                        ...i,
                        customAttributes: {
                          ...r,
                          ...(i.customAttributes || {}),
                        },
                      },
                      e,
                    ],
                    t
                  );
                },
                noticeError: (n, i = {}) => {
                  u(K, [n, { ...r, ...i }, e], t);
                },
                setApplicationVersion: (e) => {
                  r['application.version'] = e;
                },
                setCustomAttribute: (e, t) => {
                  r[e] = t;
                },
                setUserId: (e) => {
                  r['enduser.id'] = e;
                },
                metadata: {
                  customAttributes: r,
                  target: t,
                  get connected() {
                    return o || Promise.reject(new Error('Failed to connect'));
                  },
                },
              };
              i
                ? i()
                : (o = new Promise((n, i) => {
                    try {
                      const o = e.runtime?.entityManager;
                      let s = !!o?.get().entityGuid;
                        const c = o?.getEntityGuidFor(t.licenseKey, t.applicationID);
                        let u = !!c;
                      if (s && u) (t.entityGuid = c), n(a);
                      else {
                        const d = setTimeout(
                          () => i(new Error('Failed to connect - Timeout')),
                          15e3
                        );
                        function l(r) {
                          (0, U.A)(r, e)
                            ? (s ||= !0)
                            : t.licenseKey === r.licenseKey &&
                              t.applicationID === r.applicationID &&
                              ((u = !0), (t.entityGuid = r.entityGuid)),
                            s &&
                              u &&
                              (clearTimeout(d),
                              e.ee.removeEventListener('entity-added', l),
                              n(a));
                        }
                        e.ee.emit('api-send-rum', [r, t]),
                          e.ee.on('entity-added', l);
                      }
                    } catch (f) {
                      i(f);
                    }
                  }));
              const u = async (t, r, a) => {
                if (i) return i();
                const u = (0, c.t)();
                (0, s.p)(
                  h.xV,
                  ['API/register/'.concat(t.name, '/called')],
                  void 0,
                  n.K7.metrics,
                  e.ee
                );
                try {
                  await o;
                  const n = e.init.api.duplicate_registered_data;
                  (!0 === n ||
                    (Array.isArray(n) && n.includes(a.entityGuid))) &&
                    t(...r, void 0, u),
                    t(...r, a.entityGuid, u);
                } catch (e) {
                  (0, l.R)(50, e);
                }
              };
              return a;
            })(e, t),
          e
        );
      }
      class z extends T {
        static featureName = j.T;

        constructor(e) {
          let t;
          super(e, j.T),
            (t = e),
            p(u.o5, (e, r) => K(e, r, t), t),
            (function (e) {
              p(
                u.bt,
                (t) => {
                  e.runtime.onerror = t;
                },
                e
              );
            })(e),
            (function (e) {
              let t = 0;
              p(
                u.k6,
                function (e, r) {
                  ++t > 10 ||
                    (this.runtime.releaseIds[e.slice(-200)] = (`${  r}`).slice(
                      -200
                    ));
                },
                e
              );
            })(e),
            V(e);
          try {
            this.removeOnAbort = new AbortController();
          } catch (e) {}
          this.ee.on('internal-error', (t, r) => {
            this.abortHandler &&
              (0, s.p)(
                'ierr',
                [L(t), (0, c.t)(), !0, {}, e.runtime.isRecording, r],
                void 0,
                this.featureName,
                this.ee
              );
          }),
            y.gm.addEventListener(
              'unhandledrejection',
              (t) => {
                this.abortHandler &&
                  (0, s.p)(
                    'err',
                    [
                      H(t),
                      (0, c.t)(),
                      !1,
                      { unhandledPromiseRejection: 1 },
                      e.runtime.isRecording,
                    ],
                    void 0,
                    this.featureName,
                    this.ee
                  );
              },
              (0, O.jT)(!1, this.removeOnAbort?.signal)
            ),
            y.gm.addEventListener(
              'error',
              (t) => {
                this.abortHandler &&
                  (0, s.p)(
                    'err',
                    [M(t), (0, c.t)(), !1, {}, e.runtime.isRecording],
                    void 0,
                    this.featureName,
                    this.ee
                  );
              },
              (0, O.jT)(!1, this.removeOnAbort?.signal)
            ),
            (this.abortHandler = this.#r),
            this.importAggregator(e, () => i.e(478).then(i.bind(i, 5928)));
        }

        #r() {
          this.removeOnAbort?.abort(), (this.abortHandler = void 0);
        }
      }
      const Z = i(8990);
      let q = 1;
      function X(e) {
        const t = typeof e;
        return !e || (t !== 'object' && t !== 'function')
          ? -1
          : e === y.gm
          ? 0
          : (0, Z.I)(e, 'nr@id', () => q++);
      }
      function Y(e) {
        if (typeof e === 'string' && e.length) return e.length;
        if (typeof e === 'object') {
          if (
            typeof ArrayBuffer !== 'undefined' &&
            e instanceof ArrayBuffer &&
            e.byteLength
          )
            return e.byteLength;
          if (typeof Blob !== 'undefined' && e instanceof Blob && e.size)
            return e.size;
          if (!(typeof FormData !== 'undefined' && e instanceof FormData))
            try {
              return (0, C.A)(e).length;
            } catch (e) {
              
            }
        }
      }
      const J = i(8139);
        const Q = i(7836);
        const ee = i(3434);
      const te = {};
        const re = ['open', 'send'];
      function ne(e) {
        const t = e || Q.ee;
        const r = (function (e) {
          return (e || Q.ee).get('xhr');
        })(t);
        if (void 0 === y.gm.XMLHttpRequest) return r;
        if (te[r.debugId]++) return r;
        (te[r.debugId] = 1), (0, J.u)(t);
        const n = (0, ee.YM)(r);
          const i = y.gm.XMLHttpRequest;
          const o = y.gm.MutationObserver;
          const a = y.gm.Promise;
          const s = y.gm.setInterval;
          const c = 'readystatechange';
          const u = [
            'onload',
            'onerror',
            'onabort',
            'onloadstart',
            'onloadend',
            'onprogress',
            'ontimeout',
          ];
          let d = [];
          const f = (y.gm.XMLHttpRequest = function (e) {
            const t = new i(e);
              const o = r.context(t);
            try {
              r.emit('new-xhr', [t], o),
                t.addEventListener(
                  c,
                  ((a = o),
                  function () {
                    const e = this;
                    e.readyState > 3 &&
                      !a.resolved &&
                      ((a.resolved = !0), r.emit('xhr-resolved', [], e)),
                      n.inPlace(e, u, 'fn-', b);
                  }),
                  (0, O.jT)(!1)
                );
            } catch (e) {
              (0, l.R)(15, e);
              try {
                r.emit('internal-error', [e]);
              } catch (e) {}
            }
            let a;
            return t;
          });
        function h(e, t) {
          n.inPlace(t, ['onreadystatechange'], 'fn-', b);
        }
        if (
          ((function (e, t) {
            for (const r in e) t[r] = e[r];
          })(i, f),
          (f.prototype = i.prototype),
          n.inPlace(f.prototype, re, '-xhr-', b),
          r.on('send-xhr-start', (e, t) => {
            h(e, t),
              (function (e) {
                d.push(e),
                  o && (p ? p.then(v) : s ? s(v) : ((g = -g), (m.data = g)));
              })(t);
          }),
          r.on('open-xhr-start', h),
          o)
        ) {
          var p = a && a.resolve();
          if (!s && !a) {
            var g = 1;
              var m = document.createTextNode(g);
            new o(v).observe(m, { characterData: !0 });
          }
        } else
          t.on('fn-end', (e) => {
            (e[0] && e[0].type === c) || v();
          });
        function v() {
          for (let e = 0; e < d.length; e++) h(0, d[e]);
          d.length && (d = []);
        }
        function b(e, t) {
          return t;
        }
        return r;
      }
      const ie = 'fetch-';
        const oe = `${ie  }body-`;
        const ae = ['arrayBuffer', 'blob', 'json', 'text', 'formData'];
        const se = y.gm.Request;
        const ce = y.gm.Response;
        const ue = 'prototype';
      const de = {};
      function le(e) {
        const t = (function (e) {
          return (e || Q.ee).get('fetch');
        })(e);
        if (!(se && ce && y.gm.fetch)) return t;
        if (de[t.debugId]++) return t;
        function r(e, r, n) {
          const i = e[r];
          typeof i === 'function' &&
            (e[r] = function () {
              let e;
                const r = [...arguments];
                const o = {};
              t.emit(`${n  }before-start`, [r], o),
                o[Q.P] && o[Q.P].dt && (e = o[Q.P].dt);
              const a = i.apply(this, r);
              return (
                t.emit(`${n  }start`, [r, e], a),
                a.then(
                  (e) => (t.emit(`${n  }end`, [null, e], a), e),
                  (e) => {
                    throw (t.emit(`${n  }end`, [e], a), e);
                  }
                )
              );
            });
        }
        return (
          (de[t.debugId] = 1),
          ae.forEach((e) => {
            r(se[ue], e, oe), r(ce[ue], e, oe);
          }),
          r(y.gm, 'fetch', ie),
          t.on(`${ie  }end`, function (e, r) {
            const n = this;
            if (r) {
              const i = r.headers.get('content-length');
              i !== null && (n.rxSize = i), t.emit(`${ie  }done`, [null, r], n);
            } else t.emit(`${ie  }done`, [e], n);
          }),
          t
        );
      }
      const fe = i(7485);
      class he {
        constructor(e) {
          this.agentRef = e;
        }

        generateTracePayload(t) {
          const r = this.agentRef.loader_config;
          if (!this.shouldGenerateTrace(t) || !r) return null;
          const n = (r.accountID || '').toString() || null;
            const i = (r.agentID || '').toString() || null;
            const o = (r.trustKey || '').toString() || null;
          if (!n || !i) return null;
          const a = (0, e.ZF)();
            const s = (0, e.el)();
            const c = Date.now();
            const u = { spanId: a, traceId: s, timestamp: c };
          return (
            (t.sameOrigin ||
              (this.isAllowedOrigin(t) &&
                this.useTraceContextHeadersForCors())) &&
              ((u.traceContextParentHeader =
                this.generateTraceContextParentHeader(a, s)),
              (u.traceContextStateHeader = this.generateTraceContextStateHeader(
                a,
                c,
                n,
                i,
                o
              ))),
            ((t.sameOrigin && !this.excludeNewrelicHeader()) ||
              (!t.sameOrigin &&
                this.isAllowedOrigin(t) &&
                this.useNewrelicHeaderForCors())) &&
              (u.newrelicHeader = this.generateTraceHeader(a, s, c, n, i, o)),
            u
          );
        }

        generateTraceContextParentHeader(e, t) {
          return `00-${  t  }-${  e  }-01`;
        }

        generateTraceContextStateHeader(e, t, r, n, i) {
          return `${i  }@nr=0-1-${  r  }-${  n  }-${  e  }----${  t}`;
        }

        generateTraceHeader(e, t, r, n, i, o) {
          if (!(typeof y.gm?.btoa === 'function')) return null;
          const a = {
            v: [0, 1],
            d: { ty: 'Browser', ac: n, ap: i, id: e, tr: t, ti: r },
          };
          return o && n !== o && (a.d.tk = o), btoa((0, C.A)(a));
        }

        shouldGenerateTrace(e) {
          return (
            this.agentRef.init?.distributed_tracing && this.isAllowedOrigin(e)
          );
        }

        isAllowedOrigin(e) {
          let t = !1;
          const r = this.agentRef.init?.distributed_tracing;
          if (e.sameOrigin) t = !0;
          else if (r?.allowed_origins instanceof Array)
            for (let n = 0; n < r.allowed_origins.length; n++) {
              const i = (0, fe.D)(r.allowed_origins[n]);
              if (
                e.hostname === i.hostname &&
                e.protocol === i.protocol &&
                e.port === i.port
              ) {
                t = !0;
                break;
              }
            }
          return t;
        }

        excludeNewrelicHeader() {
          const e = this.agentRef.init?.distributed_tracing;
          return !!e && !!e.exclude_newrelic_header;
        }

        useNewrelicHeaderForCors() {
          const e = this.agentRef.init?.distributed_tracing;
          return !!e && !1 !== e.cors_use_newrelic_header;
        }

        useTraceContextHeadersForCors() {
          const e = this.agentRef.init?.distributed_tracing;
          return !!e && !!e.cors_use_tracecontext_headers;
        }
      }
      const pe = i(9300);
        const ge = i(7295);
        const me = ['load', 'error', 'abort', 'timeout'];
        const ve = me.length;
        const be = (0, S.dV)().o.REQ;
        const ye = (0, S.dV)().o.XHR;
      const Re = 'X-NewRelic-App-Data';
      class we extends T {
        static featureName = pe.T;

        constructor(e) {
          super(e, pe.T),
            (this.dt = new he(e)),
            (this.handler = (e, t, r, n) => (0, s.p)(e, t, r, n, this.ee));
          try {
            const e = {
              xmlhttprequest: 'xhr',
              fetch: 'fetch',
              beacon: 'beacon',
            };
            y.gm?.performance?.getEntriesByType('resource').forEach((t) => {
              if (t.initiatorType in e && t.responseStatus !== 0) {
                const r = { status: t.responseStatus };
                  const i = {
                    rxSize: t.transferSize,
                    duration: Math.floor(t.duration),
                    cbTime: 0,
                  };
                xe(r, t.name),
                  this.handler(
                    'xhr',
                    [r, i, t.startTime, t.responseEnd, e[t.initiatorType]],
                    void 0,
                    n.K7.ajax
                  );
              }
            });
          } catch (e) {}
          le(this.ee),
            ne(this.ee),
            (function (e, t, r, i) {
              function o(e) {
                const t = this;
                (t.totalCbs = 0),
                  (t.called = 0),
                  (t.cbTime = 0),
                  (t.end = A),
                  (t.ended = !1),
                  (t.xhrGuids = {}),
                  (t.lastSize = null),
                  (t.loadCaptureCalled = !1),
                  (t.params = this.params || {}),
                  (t.metrics = this.metrics || {}),
                  e.addEventListener(
                    'load',
                    (r) => {
                      E(t, e);
                    },
                    (0, O.jT)(!1)
                  ),
                  y.lR ||
                    e.addEventListener(
                      'progress',
                      (e) => {
                        t.lastSize = e.loaded;
                      },
                      (0, O.jT)(!1)
                    );
              }
              function a(e) {
                (this.params = { method: e[0] }),
                  xe(this, e[1]),
                  (this.metrics = {});
              }
              function u(t, r) {
                e.loader_config.xpid &&
                  this.sameOrigin &&
                  r.setRequestHeader('X-NewRelic-ID', e.loader_config.xpid);
                const n = i.generateTracePayload(this.parsedOrigin);
                if (n) {
                  let o = !1;
                  n.newrelicHeader &&
                    (r.setRequestHeader('newrelic', n.newrelicHeader),
                    (o = !0)),
                    n.traceContextParentHeader &&
                      (r.setRequestHeader(
                        'traceparent',
                        n.traceContextParentHeader
                      ),
                      n.traceContextStateHeader &&
                        r.setRequestHeader(
                          'tracestate',
                          n.traceContextStateHeader
                        ),
                      (o = !0)),
                    o && (this.dt = n);
                }
              }
              function d(e, r) {
                const n = this.metrics;
                  const i = e[0];
                  const o = this;
                if (n && i) {
                  const a = Y(i);
                  a && (n.txSize = a);
                }
                (this.startTime = (0, c.t)()),
                  (this.body = i),
                  (this.listener = function (e) {
                    try {
                      e.type !== 'abort' ||
                        o.loadCaptureCalled ||
                        (o.params.aborted = !0),
                        (e.type !== 'load' ||
                          (o.called === o.totalCbs &&
                            (o.onloadCalled || typeof r.onload !== 'function') &&
                            typeof o.end === 'function')) &&
                          o.end(r);
                    } catch (e) {
                      try {
                        t.emit('internal-error', [e]);
                      } catch (e) {}
                    }
                  });
                for (let s = 0; s < ve; s++)
                  r.addEventListener(me[s], this.listener, (0, O.jT)(!1));
              }
              function l(e, t, r) {
                (this.cbTime += e),
                  t ? (this.onloadCalled = !0) : (this.called += 1),
                  this.called !== this.totalCbs ||
                    (!this.onloadCalled && typeof r.onload === 'function') ||
                    typeof this.end !== 'function' ||
                    this.end(r);
              }
              function f(e, t) {
                const r = `${  X(e)  }${!!t}`;
                this.xhrGuids &&
                  !this.xhrGuids[r] &&
                  ((this.xhrGuids[r] = !0), (this.totalCbs += 1));
              }
              function p(e, t) {
                const r = `${  X(e)  }${!!t}`;
                this.xhrGuids &&
                  this.xhrGuids[r] &&
                  (delete this.xhrGuids[r], (this.totalCbs -= 1));
              }
              function g() {
                this.endTime = (0, c.t)();
              }
              function m(e, r) {
                r instanceof ye &&
                  e[0] === 'load' &&
                  t.emit('xhr-load-added', [e[1], e[2]], r);
              }
              function v(e, r) {
                r instanceof ye &&
                  e[0] === 'load' &&
                  t.emit('xhr-load-removed', [e[1], e[2]], r);
              }
              function b(e, t, r) {
                t instanceof ye &&
                  (r === 'onload' && (this.onload = !0),
                  ((e[0] && e[0].type) === 'load' || this.onload) &&
                    (this.xhrCbStart = (0, c.t)()));
              }
              function R(e, r) {
                this.xhrCbStart &&
                  t.emit(
                    'xhr-cb-time',
                    [(0, c.t)() - this.xhrCbStart, this.onload, r],
                    r
                  );
              }
              function w(e) {
                let t;
                  const r = e[1] || {};
                if (
                  (typeof e[0] === 'string'
                    ? (t = e[0]).length === 0 &&
                      y.RI &&
                      (t = `${  y.gm.location.href}`)
                    : e[0] && e[0].url
                    ? (t = e[0].url)
                    : y.gm?.URL && e[0] && e[0] instanceof URL
                    ? (t = e[0].href)
                    : typeof e[0].toString === 'function' &&
                      (t = e[0].toString()),
                  typeof t === 'string' && t.length !== 0)
                ) {
                  t &&
                    ((this.parsedOrigin = (0, fe.D)(t)),
                    (this.sameOrigin = this.parsedOrigin.sameOrigin));
                  const n = i.generateTracePayload(this.parsedOrigin);
                  if (n && (n.newrelicHeader || n.traceContextParentHeader))
                    if (e[0] && e[0].headers)
                      s(e[0].headers, n) && (this.dt = n);
                    else {
                      const o = {};
                      for (const a in r) o[a] = r[a];
                      (o.headers = new Headers(r.headers || {})),
                        s(o.headers, n) && (this.dt = n),
                        e.length > 1 ? (e[1] = o) : e.push(o);
                    }
                }
                function s(e, t) {
                  let r = !1;
                  return (
                    t.newrelicHeader &&
                      (e.set('newrelic', t.newrelicHeader), (r = !0)),
                    t.traceContextParentHeader &&
                      (e.set('traceparent', t.traceContextParentHeader),
                      t.traceContextStateHeader &&
                        e.set('tracestate', t.traceContextStateHeader),
                      (r = !0)),
                    r
                  );
                }
              }
              function x(e, t) {
                (this.params = {}),
                  (this.metrics = {}),
                  (this.startTime = (0, c.t)()),
                  (this.dt = t),
                  e.length >= 1 && (this.target = e[0]),
                  e.length >= 2 && (this.opts = e[1]);
                let r;
                  const n = this.opts || {};
                  const i = this.target;
                typeof i === 'string'
                  ? (r = i)
                  : typeof i === 'object' && i instanceof be
                  ? (r = i.url)
                  : y.gm?.URL &&
                    typeof i === 'object' &&
                    i instanceof URL &&
                    (r = i.href),
                  xe(this, r);
                const o = (
                  `${  (i && i instanceof be && i.method) || n.method || 'GET'}`
                ).toUpperCase();
                (this.params.method = o),
                  (this.body = n.body),
                  (this.txSize = Y(n.body) || 0);
              }
              function T(e, t) {
                if (
                  ((this.endTime = (0, c.t)()),
                  this.params || (this.params = {}),
                  (0, ge.iW)(this.params))
                )
                  return;
                let i;
                (this.params.status = t ? t.status : 0),
                  typeof this.rxSize === 'string' &&
                    this.rxSize.length > 0 &&
                    (i = +this.rxSize);
                const o = {
                  txSize: this.txSize,
                  rxSize: i,
                  duration: (0, c.t)() - this.startTime,
                };
                r(
                  'xhr',
                  [this.params, o, this.startTime, this.endTime, 'fetch'],
                  this,
                  n.K7.ajax
                );
              }
              function A(e) {
                const t = this.params;
                  const i = this.metrics;
                if (!this.ended) {
                  this.ended = !0;
                  for (let t = 0; t < ve; t++)
                    e.removeEventListener(me[t], this.listener, !1);
                  t.aborted ||
                    (0, ge.iW)(t) ||
                    ((i.duration = (0, c.t)() - this.startTime),
                    this.loadCaptureCalled || e.readyState !== 4
                      ? t.status == null && (t.status = 0)
                      : E(this, e),
                    (i.cbTime = this.cbTime),
                    r(
                      'xhr',
                      [t, i, this.startTime, this.endTime, 'xhr'],
                      this,
                      n.K7.ajax
                    ));
                }
              }
              function E(e, r) {
                e.params.status = r.status;
                const i = (function (e, t) {
                  const r = e.responseType;
                  return r === 'json' && t !== null
                    ? t
                    : r === 'arraybuffer' || r === 'blob' || r === 'json'
                    ? Y(e.response)
                    : r === 'text' || r === '' || void 0 === r
                    ? Y(e.responseText)
                    : void 0;
                })(r, e.lastSize);
                if (
                  (i && (e.metrics.rxSize = i),
                  e.sameOrigin && r.getAllResponseHeaders().indexOf(Re) >= 0)
                ) {
                  const o = r.getResponseHeader(Re);
                  o &&
                    ((0, s.p)(
                      h.rs,
                      ['Ajax/CrossApplicationTracing/Header/Seen'],
                      void 0,
                      n.K7.metrics,
                      t
                    ),
                    (e.params.cat = o.split(', ').pop()));
                }
                e.loadCaptureCalled = !0;
              }
              t.on('new-xhr', o),
                t.on('open-xhr-start', a),
                t.on('open-xhr-end', u),
                t.on('send-xhr-start', d),
                t.on('xhr-cb-time', l),
                t.on('xhr-load-added', f),
                t.on('xhr-load-removed', p),
                t.on('xhr-resolved', g),
                t.on('addEventListener-end', m),
                t.on('removeEventListener-end', v),
                t.on('fn-end', R),
                t.on('fetch-before-start', w),
                t.on('fetch-start', x),
                t.on('fn-start', b),
                t.on('fetch-done', T);
            })(e, this.ee, this.handler, this.dt),
            this.importAggregator(e, () => i.e(478).then(i.bind(i, 3845)));
        }
      }
      function xe(e, t) {
        const r = (0, fe.D)(t);
          const n = e.params || e;
        (n.hostname = r.hostname),
          (n.port = r.port),
          (n.protocol = r.protocol),
          (n.host = `${r.hostname  }:${  r.port}`),
          (n.pathname = r.pathname),
          (e.parsedOrigin = r),
          (e.sameOrigin = r.sameOrigin);
      }
      const Te = {};
        const Ae = ['pushState', 'replaceState'];
      function Ee(e) {
        const t = (function (e) {
          return (e || Q.ee).get('history');
        })(e);
        return (
          !y.RI ||
            Te[t.debugId]++ ||
            ((Te[t.debugId] = 1),
            (0, ee.YM)(t).inPlace(window.history, Ae, '-')),
          t
        );
      }
      const Se = i(3738);
      function _e(e) {
        p(
          u.BL,
          (t = (0, c.t)()) => {
            (0, s.p)(h.XG, [u.BL, { time: t }], void 0, n.K7.metrics, e.ee),
              e.addToTrace({ name: u.BL, start: t + y.WN, origin: 'nr' }),
              (0, s.p)(
                u.Pl + u.hG,
                [t, u.BL],
                void 0,
                n.K7.genericEvents,
                e.ee
              );
          },
          e
        );
      }
      const {
        He: Oe,
        bD: Ne,
        d3: Pe,
        Kp: Ie,
        TZ: je,
        Lc: Ce,
        uP: ke,
        Rz: Le,
      } = Se;
      class He extends T {
        static featureName = je;

        constructor(e) {
          let t;
          super(e, je),
            (t = e),
            p(
              u.U2,
              (e) => {
                if (!(e && typeof e === 'object' && e.name && e.start)) return;
                const r = {
                  n: e.name,
                  s: e.start - y.WN,
                  e: (e.end || e.start) - y.WN,
                  o: e.origin || '',
                  t: 'api',
                };
                (0, s.p)('bstApi', [r], void 0, n.K7.sessionTrace, t.ee);
              },
              t
            ),
            _e(e);
          if (!(0, w.V)(e.init)) return void this.deregisterDrain();
          const r = this.ee;
          let o;
          Ee(r),
            (this.eventsEE = (0, J.u)(r)),
            this.eventsEE.on(ke, function (e, t) {
              this.bstStart = (0, c.t)();
            }),
            this.eventsEE.on(Ce, function (e, t) {
              (0,
              s.p)('bst', [e[0], t, this.bstStart, (0, c.t)()], void 0, n.K7.sessionTrace, r);
            }),
            r.on(Le + Pe, function (e) {
              (this.time = (0, c.t)()),
                (this.startPath = location.pathname + location.hash);
            }),
            r.on(Le + Ie, function (e) {
              (0,
              s.p)('bstHist', [location.pathname + location.hash, this.startPath, this.time], void 0, n.K7.sessionTrace, r);
            });
          try {
            (o = new PerformanceObserver((e) => {
              const t = e.getEntries();
              (0, s.p)(Oe, [t], void 0, n.K7.sessionTrace, r);
            })),
              o.observe({ type: Ne, buffered: !0 });
          } catch (e) {}
          this.importAggregator(e, () => i.e(478).then(i.bind(i, 575)), {
            resourceObserver: o,
          });
        }
      }
      const Me = i(2614);
        const De = i(6344);
      class Ke extends T {
        static featureName = De.TZ;

        #n;

        #i;

        constructor(e) {
          let t;
          let r;
          super(e, De.TZ),
            (t = e),
            p(
              u.CH,
              () => {
                (0, s.p)(u.CH, [], void 0, n.K7.sessionReplay, t.ee);
              },
              t
            ),
            (function (e) {
              p(
                u.Tb,
                () => {
                  (0, s.p)(u.Tb, [], void 0, n.K7.sessionReplay, e.ee);
                },
                e
              );
            })(e),
            (this.#i = e);
          try {
            r = JSON.parse(
              localStorage.getItem(''.concat(Me.H3, '_').concat(Me.uh))
            );
          } catch (e) {}
          (0, R.SR)(e.init) && this.ee.on(De.G4.RECORD, () => this.#o()),
            this.#a(r)
              ? ((this.#n = r?.sessionReplayMode), this.#s())
              : this.importAggregator(this.#i, () =>
                  i.e(478).then(i.bind(i, 6167))
                ),
            this.ee.on('err', (e) => {
              this.#i.runtime.isRecording &&
                ((this.errorNoticed = !0),
                (0, s.p)(
                  De.G4.ERROR_DURING_REPLAY,
                  [e],
                  void 0,
                  this.featureName,
                  this.ee
                ));
            });
        }

        #a(e) {
          return (
            (e &&
              (e.sessionReplayMode === Me.g.FULL ||
                e.sessionReplayMode === Me.g.ERROR)) ||
            (0, R.Aw)(this.#i.init)
          );
        }

        #c = !1;

        async #s(e) {
          if (!this.#c) {
            this.#c = !0;
            try {
              const { Recorder: t } = await Promise.all([
                i.e(478),
                i.e(249),
              ]).then(i.bind(i, 8589));
              (this.recorder ??= new t({
                mode: this.#n,
                agentIdentifier: this.agentIdentifier,
                trigger: e,
                ee: this.ee,
                agentRef: this.#i,
              })),
                this.recorder.startRecording(),
                (this.abortHandler = this.recorder.stopRecording);
            } catch (e) {
              this.parent.ee.emit('internal-error', [e]);
            }
            this.importAggregator(
              this.#i,
              () => i.e(478).then(i.bind(i, 6167)),
              { recorder: this.recorder, errorNoticed: this.errorNoticed }
            );
          }
        }

        #o() {
          this.featAggregate
            ? this.featAggregate.mode !== Me.g.FULL &&
              this.featAggregate.initializeRecording(Me.g.FULL, !0)
            : ((this.#n = Me.g.FULL),
              this.#s(De.Qb.API),
              this.recorder &&
                this.recorder.parent.mode !== Me.g.FULL &&
                ((this.recorder.parent.mode = Me.g.FULL),
                this.recorder.stopRecording(),
                this.recorder.startRecording(),
                (this.abortHandler = this.recorder.stopRecording)));
        }
      }
      const Ue = i(3962);
      function Fe(e) {
        const t = e.ee.get('tracer');
        function r() {}
        p(
          u.dT,
          (e) => new r().get(typeof e === 'object' ? e : {}),
          e
        );
        const i = (r.prototype = {
          createTracer (r, i) {
            const o = {};
              const a = this;
              const d = typeof i === 'function';
            return (
              (0, s.p)(
                h.xV,
                ['API/createTracer/called'],
                void 0,
                n.K7.metrics,
                e.ee
              ),
              e.runSoftNavOverSpa ||
                (0, s.p)(
                  `${u.hw  }tracer`,
                  [(0, c.t)(), r, o],
                  a,
                  n.K7.spa,
                  e.ee
                ),
              function () {
                if (
                  (t.emit(`${d ? '' : 'no-'  }fn-start`, [(0, c.t)(), a, d], o),
                  d)
                )
                  try {
                    return i.apply(this, arguments);
                  } catch (e) {
                    const r = typeof e === 'string' ? new Error(e) : e;
                    throw (t.emit('fn-err', [arguments, this, r], o), r);
                  } finally {
                    t.emit('fn-end', [(0, c.t)()], o);
                  }
              }
            );
          },
        });
        [
          'actionText',
          'setName',
          'setAttribute',
          'save',
          'ignore',
          'onEnd',
          'getContext',
          'end',
          'get',
        ].forEach((t) => {
          p.apply(this, [
            t,
            function () {
              return (
                (0, s.p)(
                  u.hw + t,
                  [(0, c.t)(), ...arguments],
                  this,
                  e.runSoftNavOverSpa ? n.K7.softNav : n.K7.spa,
                  e.ee
                ),
                this
              );
            },
            e,
            i,
          ]);
        }),
          p(
            u.PA,
            function () {
              e.runSoftNavOverSpa
                ? (0, s.p)(
                    `${u.hw  }routeName`,
                    [performance.now(), ...arguments],
                    void 0,
                    n.K7.softNav,
                    e.ee
                  )
                : (0, s.p)(
                    `${u.Pl  }routeName`,
                    [(0, c.t)(), ...arguments],
                    this,
                    n.K7.spa,
                    e.ee
                  );
            },
            e
          );
      }
      class Be extends T {
        static featureName = Ue.TZ;

        constructor(e) {
          if ((super(e, Ue.TZ), Fe(e), !y.RI || !(0, S.dV)().o.MO)) return;
          const t = Ee(this.ee);
          Ue.tC.forEach((e) => {
            (0, O.sp)(
              e,
              (e) => {
                a(e);
              },
              !0
            );
          });
          const r = () =>
            (0, s.p)(
              'newURL',
              [(0, c.t)(), `${  window.location}`],
              void 0,
              this.featureName,
              this.ee
            );
          t.on('pushState-end', r), t.on('replaceState-end', r);
          try {
            this.removeOnAbort = new AbortController();
          } catch (e) {}
          (0, O.sp)(
            'popstate',
            (e) =>
              (0, s.p)(
                'newURL',
                [e.timeStamp, `${  window.location}`],
                void 0,
                this.featureName,
                this.ee
              ),
            !0,
            this.removeOnAbort?.signal
          );
          let n = !1;
          const o = new ((0, S.dV)().o.MO)((e, t) => {
              n ||
                ((n = !0),
                requestAnimationFrame(() => {
                  (0, s.p)(
                    'newDom',
                    [(0, c.t)()],
                    void 0,
                    this.featureName,
                    this.ee
                  ),
                    (n = !1);
                }));
            });
            const a = (0, x.s)(
              (e) => {
                (0, s.p)('newUIEvent', [e], void 0, this.featureName, this.ee),
                  o.observe(document.body, {
                    attributes: !0,
                    childList: !0,
                    subtree: !0,
                    characterData: !0,
                  });
              },
              100,
              { leading: !0 }
            );
          (this.abortHandler = function () {
            this.removeOnAbort?.abort(),
              o.disconnect(),
              (this.abortHandler = void 0);
          }),
            this.importAggregator(e, () => i.e(478).then(i.bind(i, 4393)), {
              domObserver: o,
            });
        }
      }
      const We = i(7378);
      const Ge = {};
        const Ve = ['appendChild', 'insertBefore', 'replaceChild'];
      function ze(e) {
        const t = (function (e) {
          return (e || Q.ee).get('jsonp');
        })(e);
        if (!y.RI || Ge[t.debugId]) return t;
        Ge[t.debugId] = !0;
        const r = (0, ee.YM)(t);
          const n = /[?&](?:callback|cb)=([^&#]+)/;
          const i = /(.*)\.([^.]+)/;
          const o = /^(\w+)(\.|$)(.*)$/;
        function a(e, t) {
          if (!e) return t;
          const r = e.match(o);
            const n = r[1];
          return a(r[3], t[n]);
        }
        return (
          r.inPlace(Node.prototype, Ve, 'dom-'),
          t.on('dom-start', (e) => {
            !(function (e) {
              if (
                !e ||
                typeof e.nodeName !== 'string' ||
                e.nodeName.toLowerCase() !== 'script'
              )
                return;
              if (typeof e.addEventListener !== 'function') return;
              const o = ((s = e.src), (c = s.match(n)), c ? c[1] : null);
              let s; let c;
              if (!o) return;
              const u = (function (e) {
                const t = e.match(i);
                if (t && t.length >= 3)
                  return { key: t[2], parent: a(t[1], window) };
                return { key: e, parent: window };
              })(o);
              if (typeof u.parent[u.key] !== 'function') return;
              const d = {};
              function l() {
                t.emit('jsonp-end', [], d),
                  e.removeEventListener('load', l, (0, O.jT)(!1)),
                  e.removeEventListener('error', f, (0, O.jT)(!1));
              }
              function f() {
                t.emit('jsonp-error', [], d),
                  t.emit('jsonp-end', [], d),
                  e.removeEventListener('load', l, (0, O.jT)(!1)),
                  e.removeEventListener('error', f, (0, O.jT)(!1));
              }
              r.inPlace(u.parent, [u.key], 'cb-', d),
                e.addEventListener('load', l, (0, O.jT)(!1)),
                e.addEventListener('error', f, (0, O.jT)(!1)),
                t.emit('new-jsonp', [e.src], d);
            })(e[0]);
          }),
          t
        );
      }
      const Ze = {};
      function qe(e) {
        const t = (function (e) {
          return (e || Q.ee).get('promise');
        })(e);
        if (Ze[t.debugId]) return t;
        Ze[t.debugId] = !0;
        const r = t.context;
          const n = (0, ee.YM)(t);
          const i = y.gm.Promise;
        return (
          i &&
            (function () {
              function e(r) {
                const o = t.context();
                  const a = n(r, 'executor-', o, null, !1);
                const s = Reflect.construct(i, [a], e);
                return (
                  (t.context(s).getCtx = function () {
                    return o;
                  }),
                  s
                );
              }
              (y.gm.Promise = e),
                Object.defineProperty(e, 'name', { value: 'Promise' }),
                (e.toString = function () {
                  return i.toString();
                }),
                Object.setPrototypeOf(e, i),
                ['all', 'race'].forEach((r) => {
                  const n = i[r];
                  e[r] = function (e) {
                    let i = !1;
                    [...(e || [])].forEach((e) => {
                      this.resolve(e).then(a(r === 'all'), a(!1));
                    });
                    const o = n.apply(this, arguments);
                    return o;
                    function a(e) {
                      return function () {
                        t.emit('propagate', [null, !i], o, !1, !1),
                          (i = i || !e);
                      };
                    }
                  };
                }),
                ['resolve', 'reject'].forEach((r) => {
                  const n = i[r];
                  e[r] = function (e) {
                    const r = n.apply(this, arguments);
                    return (
                      e !== r && t.emit('propagate', [e, !0], r, !1, !1), r
                    );
                  };
                }),
                (e.prototype = i.prototype);
              const o = i.prototype.then;
              (i.prototype.then = function (...e) {
                const i = this;
                  const a = r(i);
                (a.promise = i),
                  (e[0] = n(e[0], 'cb-', a, null, !1)),
                  (e[1] = n(e[1], 'cb-', a, null, !1));
                const s = o.apply(this, e);
                return (
                  (a.nextPromise = s),
                  t.emit('propagate', [i, !0], s, !1, !1),
                  s
                );
              }),
                (i.prototype.then[ee.Jt] = o),
                t.on('executor-start', function (e) {
                  (e[0] = n(e[0], 'resolve-', this, null, !1)),
                    (e[1] = n(e[1], 'resolve-', this, null, !1));
                }),
                t.on('executor-err', (e, t, r) => {
                  e[1](r);
                }),
                t.on('cb-end', function (e, r, n) {
                  t.emit('propagate', [n, !0], this.nextPromise, !1, !1);
                }),
                t.on('propagate', function (e, r, n) {
                  (this.getCtx && !r) ||
                    (this.getCtx = function () {
                      if (e instanceof Promise) var r = t.context(e);
                      return r && r.getCtx ? r.getCtx() : this;
                    });
                });
            })(),
          t
        );
      }
      const Xe = {};
        const Ye = 'setTimeout';
        const $e = 'setInterval';
        const Je = 'clearTimeout';
        const Qe = '-start';
        const et = [Ye, 'setImmediate', $e, Je, 'clearImmediate'];
      function tt(e) {
        const t = (function (e) {
          return (e || Q.ee).get('timer');
        })(e);
        if (Xe[t.debugId]++) return t;
        Xe[t.debugId] = 1;
        const r = (0, ee.YM)(t);
        return (
          r.inPlace(y.gm, et.slice(0, 2), `${Ye  }-`),
          r.inPlace(y.gm, et.slice(2, 3), `${$e  }-`),
          r.inPlace(y.gm, et.slice(3), `${Je  }-`),
          t.on($e + Qe, (e, t, n) => {
            e[0] = r(e[0], 'fn-', null, n);
          }),
          t.on(Ye + Qe, function (e, t, n) {
            (this.method = n),
              (this.timerDuration = isNaN(e[1]) ? 0 : +e[1]),
              (e[0] = r(e[0], 'fn-', this, n));
          }),
          t
        );
      }
      const rt = {};
      function nt(e) {
        const t = (function (e) {
          return (e || Q.ee).get('mutation');
        })(e);
        if (!y.RI || rt[t.debugId]) return t;
        rt[t.debugId] = !0;
        const r = (0, ee.YM)(t);
          const n = y.gm.MutationObserver;
        return (
          n &&
            ((window.MutationObserver = function (e) {
              return this instanceof n
                ? new n(r(e, 'fn-'))
                : n.apply(this, arguments);
            }),
            (MutationObserver.prototype = n.prototype)),
          t
        );
      }
      const {
        TZ: it,
        d3: ot,
        Kp: at,
        $p: st,
        wW: ct,
        e5: ut,
        tH: dt,
        uP: lt,
        rw: ft,
        Lc: ht,
      } = We;
      class pt extends T {
        static featureName = it;

        constructor(e) {
          if ((super(e, it), Fe(e), !y.RI)) return;
          try {
            this.removeOnAbort = new AbortController();
          } catch (e) {}
          let t;
            let r = 0;
          const n = this.ee.get('tracer');
            const o = ze(this.ee);
            const a = qe(this.ee);
            const u = tt(this.ee);
            const d = ne(this.ee);
            const l = this.ee.get('events');
            const f = le(this.ee);
            const h = Ee(this.ee);
            const p = nt(this.ee);
          function g(e, t) {
            h.emit('newURL', [`${  window.location}`, t]);
          }
          function m() {
            r++, (t = window.location.hash), (this[lt] = (0, c.t)());
          }
          function v() {
            r--, window.location.hash !== t && g(0, !0);
            const e = (0, c.t)();
            (this[ut] = ~~this[ut] + e - this[lt]), (this[ht] = e);
          }
          function b(e, t) {
            e.on(t, function () {
              this[t] = (0, c.t)();
            });
          }
          this.ee.on(lt, m),
            a.on(ft, m),
            o.on(ft, m),
            this.ee.on(ht, v),
            a.on(ct, v),
            o.on(ct, v),
            this.ee.on('fn-err', (...t) => {
              t[2]?.__newrelic?.[e.agentIdentifier] ||
                (0, s.p)(
                  'function-err',
                  [...t],
                  void 0,
                  this.featureName,
                  this.ee
                );
            }),
            this.ee.buffer([lt, ht, 'xhr-resolved'], this.featureName),
            l.buffer([lt], this.featureName),
            u.buffer(
              [`setTimeout${  at}`, `clearTimeout${  ot}`, lt],
              this.featureName
            ),
            d.buffer([lt, 'new-xhr', `send-xhr${  ot}`], this.featureName),
            f.buffer(
              [dt + ot, `${dt  }-done`, dt + st + ot, dt + st + at],
              this.featureName
            ),
            h.buffer(['newURL'], this.featureName),
            p.buffer([lt], this.featureName),
            a.buffer(
              ['propagate', ft, ct, 'executor-err', `resolve${  ot}`],
              this.featureName
            ),
            n.buffer([lt, `no-${  lt}`], this.featureName),
            o.buffer(
              ['new-jsonp', 'cb-start', 'jsonp-error', 'jsonp-end'],
              this.featureName
            ),
            b(f, dt + ot),
            b(f, `${dt  }-done`),
            b(o, 'new-jsonp'),
            b(o, 'jsonp-end'),
            b(o, 'cb-start'),
            h.on('pushState-end', g),
            h.on('replaceState-end', g),
            window.addEventListener(
              'hashchange',
              g,
              (0, O.jT)(!0, this.removeOnAbort?.signal)
            ),
            window.addEventListener(
              'load',
              g,
              (0, O.jT)(!0, this.removeOnAbort?.signal)
            ),
            window.addEventListener(
              'popstate',
              () => {
                g(0, r > 1);
              },
              (0, O.jT)(!0, this.removeOnAbort?.signal)
            ),
            (this.abortHandler = this.#r),
            this.importAggregator(e, () => i.e(478).then(i.bind(i, 5592)));
        }

        #r() {
          this.removeOnAbort?.abort(), (this.abortHandler = void 0);
        }
      }
      const gt = i(3333);
      class mt extends T {
        static featureName = gt.TZ;

        constructor(e) {
          super(e, gt.TZ);
          const t = [
            e.init.page_action.enabled,
            e.init.performance.capture_marks,
            e.init.performance.capture_measures,
            e.init.user_actions.enabled,
            e.init.performance.resources.enabled,
          ];
          let r;
          if (
            ((r = e),
            p(u.hG, (e, t) => G(e, t, r), r),
            (function (e) {
              p(
                u.fF,
                function () {
                  (0, s.p)(
                    u.Pl + u.fF,
                    [(0, c.t)(), ...arguments],
                    void 0,
                    n.K7.genericEvents,
                    e.ee
                  );
                },
                e
              );
            })(e),
            _e(e),
            V(e),
            y.RI &&
              (e.init.user_actions.enabled &&
                (gt.Zp.forEach((e) =>
                  (0, O.sp)(
                    e,
                    (e) =>
                      (0, s.p)('ua', [e], void 0, this.featureName, this.ee),
                    !0
                  )
                ),
                gt.qN.forEach((e) => {
                  const t = (0, x.s)(
                    (e) => {
                      (0, s.p)('ua', [e], void 0, this.featureName, this.ee);
                    },
                    500,
                    { leading: !0 }
                  );
                  (0, O.sp)(e, t);
                })),
              e.init.performance.resources.enabled &&
                y.gm.PerformanceObserver?.supportedEntryTypes.includes(
                  'resource'
                )))
          ) {
            new PerformanceObserver((e) => {
              e.getEntries().forEach((e) => {
                (0, s.p)(
                  'browserPerformance.resource',
                  [e],
                  void 0,
                  this.featureName,
                  this.ee
                );
              });
            }).observe({ type: 'resource', buffered: !0 });
          }
          t.some((e) => e)
            ? this.importAggregator(e, () => i.e(478).then(i.bind(i, 8019)))
            : this.deregisterDrain();
        }
      }
      const vt = i(2646);
      const bt = new Map();
      function yt(e, t, r, n) {
        if (
          typeof t !== 'object' ||
          !t ||
          typeof r !== 'string' ||
          !r ||
          typeof t[r] !== 'function'
        )
          return (0, l.R)(29);
        const i = (function (e) {
            return (e || Q.ee).get('logger');
          })(e);
          const o = (0, ee.YM)(i);
          const a = new vt.y(Q.P);
        (a.level = n.level), (a.customAttributes = n.customAttributes);
        const s = t[r]?.[ee.Jt] || t[r];
        return (
          bt.set(s, a), o.inPlace(t, [r], 'wrap-logger-', () => bt.get(s)), i
        );
      }
      class Rt extends T {
        static featureName = F.TZ;

        constructor(e) {
          let t;
          super(e, F.TZ),
            (t = e),
            p(u.$9, (e, r) => W(e, r, t), t),
            (function (e) {
              p(
                u.Wb,
                (
                  t,
                  r,
                  { customAttributes: n = {}, level: i = F.p_.INFO } = {}
                ) => {
                  yt(e.ee, t, r, { customAttributes: n, level: i });
                },
                e
              );
            })(e),
            V(e);
          const r = this.ee;
          yt(r, y.gm.console, 'log', { level: 'info' }),
            yt(r, y.gm.console, 'error', { level: 'error' }),
            yt(r, y.gm.console, 'warn', { level: 'warn' }),
            yt(r, y.gm.console, 'info', { level: 'info' }),
            yt(r, y.gm.console, 'debug', { level: 'debug' }),
            yt(r, y.gm.console, 'trace', { level: 'trace' }),
            this.ee.on('wrap-logger-end', function ([e]) {
              const { level: t, customAttributes: n } = this;
              (0, B.R)(r, e, n, t);
            }),
            this.importAggregator(e, () => i.e(478).then(i.bind(i, 5288)));
        }
      }
      new (class extends r {
        constructor(e) {
          let t;
          (super(), y.gm)
            ? ((this.features = {}),
              (0, S.bQ)(this.agentIdentifier, this),
              (this.desiredFeatures = new Set(e.features || [])),
              this.desiredFeatures.add(E),
              (this.runSoftNavOverSpa = [...this.desiredFeatures].some(
                (e) => e.featureName === n.K7.softNav
              )),
              (0, a.j)(this, e, e.loaderType || 'agent'),
              (t = this),
              p(
                u.cD,
                (e, r, n = !1) => {
                  if (typeof e === 'string') {
                    if (
                      ['string', 'number', 'boolean'].includes(typeof r) ||
                      r === null
                    )
                      return g(t, e, r, u.cD, n);
                    (0, l.R)(40, typeof r);
                  } else (0, l.R)(39, typeof e);
                },
                t
              ),
              (function (e) {
                p(
                  u.Dl,
                  (t) => {
                    if (typeof t === 'string' || t === null)
                      return g(e, 'enduser.id', t, u.Dl, !0);
                    (0, l.R)(41, typeof t);
                  },
                  e
                );
              })(this),
              (function (e) {
                p(
                  u.nb,
                  (t) => {
                    if (typeof t === 'string' || t === null)
                      return g(e, 'application.version', t, u.nb, !1);
                    (0, l.R)(42, typeof t);
                  },
                  e
                );
              })(this),
              (function (e) {
                p(
                  u.d3,
                  () => {
                    e.ee.emit('manual-start-all');
                  },
                  e
                );
              })(this),
              this.run())
            : (0, l.R)(21);
        }

        get config() {
          return {
            info: this.info,
            init: this.init,
            loader_config: this.loader_config,
            runtime: this.runtime,
          };
        }

        get api() {
          return this;
        }

        run() {
          try {
            const e = (function (e) {
                const t = {};
                return (
                  o.forEach((r) => {
                    t[r] = !!e[r]?.enabled;
                  }),
                  t
                );
              })(this.init);
              const t = [...this.desiredFeatures];
            t.sort((e, t) => n.P3[e.featureName] - n.P3[t.featureName]),
              t.forEach((t) => {
                if (!e[t.featureName] && t.featureName !== n.K7.pageViewEvent)
                  return;
                if (this.runSoftNavOverSpa && t.featureName === n.K7.spa)
                  return;
                if (!this.runSoftNavOverSpa && t.featureName === n.K7.softNav)
                  return;
                const r = (function (e) {
                  switch (e) {
                    case n.K7.ajax:
                      return [n.K7.jserrors];
                    case n.K7.sessionTrace:
                      return [n.K7.ajax, n.K7.pageViewEvent];
                    case n.K7.sessionReplay:
                      return [n.K7.sessionTrace];
                    case n.K7.pageViewTiming:
                      return [n.K7.pageViewEvent];
                    default:
                      return [];
                  }
                })(t.featureName).filter((e) => !(e in this.features));
                r.length > 0 &&
                  (0, l.R)(36, {
                    targetFeature: t.featureName,
                    missingDependencies: r,
                  }),
                  (this.features[t.featureName] = new t(this));
              });
          } catch (e) {
            (0, l.R)(22, e);
            for (const e in this.features) this.features[e].abortHandler?.();
            const t = (0, S.Zm)();
            delete t.initializedAgents[this.agentIdentifier]?.features,
              delete this.sharedAggregator;
            return t.ee.get(this.agentIdentifier).abort(), !1;
          }
        }
      })({
        features: [we, E, P, He, Ke, I, z, mt, Rt, Be, pt],
        loaderType: 'spa',
      });
    })();
})();
