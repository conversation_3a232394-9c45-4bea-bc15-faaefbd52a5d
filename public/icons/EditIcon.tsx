import React from 'react';

interface EditIconProps extends React.SVGProps<SVGSVGElement> {
  stroke?: string;
  size?: number;
  className?: string;
  onClick?: () => void;
}

export default function EditIcon({
  stroke = '#E97100',
  size = 18,
  className = '',
  onClick,
}: EditIconProps) {
  return (
    <svg
      width={size}
      height={size}
      viewBox="0 0 18 18"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      className={`edit-icon ${className}`}
      onClick={onClick}
    >
      <path
        d="M10.7724 4.23901L11.8824 3.12901C12.0216 2.98966 12.187 2.87911 12.3691 2.80369C12.5511 2.72827 12.7462 2.68945 12.9432 2.68945C13.1403 2.68945 13.3354 2.72827 13.5174 2.80369C13.6994 2.87911 13.8648 2.98966 14.0041 3.12901L15.0646 4.18951C15.3458 4.4708 15.5038 4.85226 15.5038 5.25001C15.5038 5.64775 15.3458 6.02922 15.0646 6.31051L13.9546 7.42051M10.7724 4.23901L3.56035 11.4503C3.31134 11.6993 3.15788 12.028 3.12685 12.3788L2.94535 14.4338C2.9356 14.543 2.94993 14.6531 2.98733 14.7562C3.02473 14.8593 3.08429 14.9529 3.16181 15.0305C3.23933 15.1081 3.33292 15.1677 3.43599 15.2052C3.53906 15.2427 3.6491 15.2572 3.75835 15.2475L5.81335 15.066C6.16467 15.0353 6.49395 14.8818 6.74335 14.6325L13.9546 7.42051M10.7724 4.23901L13.9546 7.42051"
        stroke={stroke}
        strokeWidth="var(--stroke-width, 1.5)"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}
