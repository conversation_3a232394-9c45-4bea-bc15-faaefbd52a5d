import { Teams } from './teams-types';
/* eslint-disable no-use-before-define */
export interface ChatTypes {
  creatorID: string;
  conversationState: string;
  pushEnabled: boolean;
  participants: Participant[];
  chatServiceSid: string;
  conversationFriendlyName: string;
  org: Org;
  updatedAt: string;
  latestMessageSender: LatestMessageSender;
  conversationSid: string;
  id: string;
  creator: LatestMessageSender;
  latestMessage: string;
  createdAt: string;
  title: string;
}

interface LatestMessageSender {
  auth: string;
  color: string;
  createdAt: string;
  middleName: string;
  roles: string[];
  lastName: string;
  firstName: string;
  id: string;
  org: Org;
  profile: string;
  updatedAt: string;
  email: string;
}

export interface Participant {
  color: string;
  auth: string;
  createdAt: string;
  middleName?: string;
  roles: string[];
  lastName: string;
  firstName: string;
  id: string;
  org: Org;
  profile: string;
  updatedAt: string;
  email: string;
  teams: Teams[];
}

interface Org {
  id: string;
}
