/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable no-use-before-define */
export interface MemberProfileResponse {
  survey: Survey[];
  answers: Answers[];
  member: Member;
}

export interface MemberProfileResponseDetail {
  survey: Survey;
  scored: boolean;
  answers: Answers[];
  member: Member;
}
export interface Member {
  military: string;
  tags: MemberTags[];
  deliveryDate: string;
  pregnancyStatus: string;
  org: Org;
  lastContact: string;
  color: string;
  notes: Note[];
  lang: string;
  pronouns: string;
  score: string;
  middleName?: any;
  homeless:boolean;
  type: string;
  address: any[];
  households: Household[];
  ethnicity: string;
  meta: Meta;
  firstName: string;
  gender: string;
  id: string;
  referredBy: string;
  phones: Phone[];
  enrolledOn: string;
  unenrolledDate: string;
  attachments: Attachment[];
  sexualIdentity: string;
  roles: string[];
  schools: any[];
  status: string;
  updatedAt: string;
  auth?: any;
  createdAt: string;
  headOfHouse?: any;
  email: string;
  genderIdentity: string;
  dob: string;
  lastAt: string;
  lastName: string;
  primaryUser: Org;
}
interface Meta {
  data: any;
  contacts: EmergencyContacts[];
}
interface EmergencyContacts {
  name: string;
  phone: string;
  type: string;
}
interface Attachment {
  member: Org;
  id: string;
  type: string;
  user: Question;
  url: string;
  survey: Question;
  org: Question;
  household: Question;
  updatedAt: string;
  kind: string;
  category: string;
  createdAt: string;
  name: string;
  animal: Question;
  note: Question;
}

interface Phone {
  member: Org;
  id: string;
  label: string;
  network: Question;
  updatedAt: string;
  org: Question;
  createdAt: string;
  number: string;
}

interface Household {
  id: string;
  type: string;
  householdScore: string;
  org: Org2;
  title: string;
  updatedAt: string;
  kind: string;
  createdAt: string;
  headOfHouse: Org2;
  lastVisit: string;
}

interface Org2 {
  id?: string;
}

interface Note {
  member: Org;
  id: string;
  type: string;
  subtitle?: any;
  updatedAt: string;
  title: string;
  msg: string;
  createdAt: string;
  creator: Org;
  service: Question;
}

export interface Survey {
  id: string;
  attachments: any[];
  orgID: string;
  scored: boolean;
  firstSurvey: number;
  updatedAt: string;
  score: string;
  createdAt: string;
  name: string;
  startedAt: string;
  taker: Taker;
  memberID: string;
  sections: Section[];
  lastSurvey: number;
  key: string;
  endedAt: string;
  status: string;
}
export interface Answers {
  giverID: string;
  takerID: string;
  answer: string;
  questionID: string;
  value: string;
  orgID: string;
  key: string;
  score: string;
  updatedAt: string;
  parentID: string;
  surveyID: string;
  id: string;
  createdAt: string;
}
export interface ISurveyItems {
  title: string;
  type: string;
  score: number;
}
export interface Section {
  kind?: any;
  survey: Org;
  questions: Questions[];
  score: string;
  id: string;
  type: string;
  createdAt: string;
  complete: boolean;
  title: string;
  updatedAt: string;
}

export interface Questions {
  type: string;
  section: Org;
  title: string;
  id: string;
  createdAt: string;
  value: number;
  updatedAt: string;
  message: string;
  level: number;
  score: number;
  questions: Questions[];
  question: Question[];
}

interface Question {
  id?: any;
}

interface Taker {
  roles: string[];
  firstName: string;
  org: Org;
  auth: string;
  lastName: string;
  color: string;
  middleName?: any;
  id: string;
  email: string;
  profile: string;
  updatedAt: string;
  createdAt: string;
}

interface Org {
  id: string;
}

export interface MemberTags {
  key: string;
  name: string;
  color: string;
  updatedAt: string;
  id?: string;
}
