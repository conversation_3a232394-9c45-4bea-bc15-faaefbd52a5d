/* eslint-disable no-use-before-define */
export interface TaskResponse {
  tasks: TasksResponseProps;
}

export interface TasksResponseProps {
  completed: TaskObject[];
  pending: TaskObject[];
  archive: TaskObject[];
  'picked up': TaskObject[];
}

export interface TaskObject {
  assignee: Assignee;
  completedBy: CompletedBy[];
  org: Org;
  desc: string;
  updatedAt: string;
  meta: null;
  receivers: Receiver[];
  createdAt: string;
  title: string;
  creator: Creator;
  location: Location | null;
  assessments: any[];
  id: string;
  attachments?: Attachment[];
  remote: boolean;
  reason: Policy;
  urgent: null;
  dueAtEpoc: number;
  taskDetail: TaskDetail;
  status: string;
  type: string;
}
interface Location {
  street: string;
  street2: string;
  city: string;
  state: string;
  zip: string;
  kind: string;
}
interface TaskDetail {
  task: Task;
  title: string;
  kind: string;
  createdAt: string;
  id: string;
  type: string;
  updatedAt: string;
  meta: Meta;
}

// export interface TaskAttachment {
//   url: string;
//   name?: string;
//   type?: string;
// }

interface Meta {
  data: Data;
}

interface Data {
  dateType: string;
  phone: string;
  remote: string;
  cp: string;
}

interface Task {
  id: string;
}
interface Policy {
  id: string;
  updatedAt: string;
  createdAt: string;
  msg: string;
  reason: string;
  creator: Creator;
}

interface Creator {
  org: Org;
  lastName: string;
  auth: string;
  email: string;
  color: string;
  profile: string;
  roles: string[];
  firstName: string;
  updatedAt: string;
  createdAt: string;
  id: string;
  middleName: string;
}

interface Org {
  id: string;
  updatedAt: string;
  createdAt: string;
  type: string;
  title: string;
  meta: any;
  desc: string;
  url: string;
}

interface Assignee {
  memberID: string;
  score: string;
  orgID: string;
  task: Task;
  endedAt: null;
  key: string;
  firstSurvey: null;
  updatedAt: string;
  lastSurvey: null;
  taker: Task;
  name: string;
  startedAt: string;
  profile: string;
  id: string;
  status: string;
  createdAt: string;
}
interface CompletedBy {
  profile: string;
  middleName: string;
  attachments: Attachment[];
  teams: Team[];
  firstName: string;
  lastName: string;
  auth: string;
  updatedAt: string;
  color: string;
  email: string;
  org: User;
  createdAt: string;
  id: string;
  roles: string[];
}

interface Team {
  updatedAt: string;
  createdAt: string;
  org: User;
  id: string;
  name: string;
}
interface Receiver {
  score: null;
  status: string;
  middleName: string;
  lastAt: null;
  refId: null;
  ethnicity: string;
  roles: string[];
  pronouns: string;
  lastName: string;
  deliveryDate: null;
  attachments: Attachment[];
  referredBy: string;
  type: string;
  unenrolledDate: null;
  createdAt: string;
  auth: null;
  id: string;
  enrolledOn: null;
  org: Member;
  dob: string;
  firstName: string;
  pregnancyStatus: null;
  sexualIdentity: string;
  updatedAt: string;
  gender: string;
  phones: Phone[];
  lang: string;
  email: string;
  military: null;
  genderIdentity: string;
  color: string;
}

interface Phone {
  number: string;
  updatedAt: string;
  label: string;
  user: Animal;
  org: Animal;
  createdAt: string;
  network: Animal;
  policy: Animal;
  id: string;
  member: Member;
}

export interface Attachment {
  refID: string;
  category: string;
  animal: Animal;
  household: Animal;
  survey: Animal;
  insuranceCard: Animal;
  task: Animal;
  updatedAt: string;
  kind: string;
  user: Animal;
  org: Animal;
  note: Animal;
  createdAt: string;
  url: string;
  id: string;
  type: string;
  name: string;
  content: Animal;
  member: Member;
}

interface Member {
  id: string;
}

interface Animal {
  id: null;
}
interface User {
  id: string;
}
