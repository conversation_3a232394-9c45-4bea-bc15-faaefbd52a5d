export interface NotificationMeta {
  data: {
    task?: string;
    [key: string]: any;
  };
}

export interface Notification {
  id: string;
  kind: string;
  title: string;
  message: string;
  read: boolean;
  userID: string;
  createdAt: string;
  updatedAt: string;
  meta: NotificationMeta;
}

export interface NotificationMetadata {
  total: number;
  page: number;
  per: number;
}

export interface NotificationsResponse {
  items: Notification[];
  metadata: NotificationMetadata;
}

export interface MarkNotificationsReadRequest {
  ids: string[];
  state: boolean;
}

export interface DeleteNotificationsRequest {
  ids: string[];
}

export interface NotificationFilters {
  per?: number;
  read?: boolean;
  userID: string;
  page?: number;
}

export interface NotificationCountResponse {
  total: number;
  unread: number;
}
