/* eslint-disable no-use-before-define */
export interface ReportingResponse {
  operationalData: OperationalData;
  plansData: PlansData;
  locations: Locations2;
  memberDemographics: MemberDemographicsItems;
  assessments: Assessments;
  taskData: TaskData;
}
interface KeyObject{ [key: string]: number; }
export interface TaskData {
  totalTasks: number;
  totalTasksCompleted: number;
  tasksByType: KeyObject;
  tasksByNavigators: KeyObject;
  totalTasksPending: number;
}

export interface Assessments {
  totalAssessments: number;
  assessmentsByTypes: KeyObject;
}

export interface MemberDemographicsItems {
  ethnicity: KeyObject;
  genders: KeyObject;
  households: number;
  enrolled: number;
  membersBySubStatus: KeyObject;
  insurances: KeyObject;
  membersByTypes: KeyObject;
  pending: number;
  totalInsurances: number;
  totalUnenrolled: number;
}

interface Locations2 {
  locations: Locations;
}

interface Locations {
  resilient: Resilient[];
  urgent_needs: Resilient[];
  stable: Resilient[];
}

interface Resilient {
  lon: string;
  lat: string;
}

export interface PlansData {
  networkCategories: KeyObject;
  totalPlans: number;
  totalNetworks: number;
  totalAppointments: number;
  totalServices: number;
  referralNetwork: KeyObject;
  totalActivePlans: number;
  appointmentBreakdown: KeyObject;
}

export interface OperationalData {
  totalAttachments: number;
  totalChats: number;
  totalNotes: number;
}
