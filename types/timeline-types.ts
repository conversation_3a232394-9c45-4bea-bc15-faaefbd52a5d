/* eslint-disable no-use-before-define */
export interface TimeLineResponse {
  item: Item;
  status: string;
  meta: Meta;
  id: string;
  updatedAt: string;
  memberId: string;
  desc: string;
  visible: boolean;
  creator: Creator;
  createdAt: string;
  title: string;
  carePlanId: string;
}

export interface PaginatedTimeLineResponse {
  metadata: {
    page: number;
    per: number;
    total: number;
  };
  items: TimeLineResponse[];
}

interface Creator {
  id: null | string;
  middleName?: string;
  auth?: string;
  updatedAt?: string;
  roles?: string[];
  org?: Org;
  lastName?: string;
  meta?: null;
  color?: string;
  createdAt?: string;
  email?: string;
  attachments?: Attachment[];
  firstName?: string;
  profile?: string;
}

interface Attachment {
  refID: string;
  insuranceCard: InsuranceCard;
  name: string;
  survey: InsuranceCard;
  user: Org;
  note: InsuranceCard;
  member: InsuranceCard;
  category: string;
  kind: string;
  org: InsuranceCard;
  animal: InsuranceCard;
  createdAt: string;
  updatedAt: string;
  task: InsuranceCard;
  type: string;
  household: InsuranceCard;
  content: InsuranceCard;
  id: string;
  url: string;
}

interface InsuranceCard {
  id: null;
}

interface Org {
  id: string;
}

interface Meta {
  data: Data;
}

interface Data {
  appointment?: string;
  services?: string;
  carePlanId?: string;
  reason_id?: string;
}

interface Item {
  id: null | string;
}
