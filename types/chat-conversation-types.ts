/* eslint-disable no-use-before-define */
export interface ChatConversationTypes {
  meta: Meta;
  messages: Message[];
}

interface Message {
  body: string;
  index: number;
  author: string;
  date_updated: string;
  media?: any;
  participant_sid?: any;
  conversation_sid: string;
  account_sid: string;
  delivery?: any;
  url: string;
  date_created: string;
  content_sid?: any;
  sid: string;
  attributes: string;
  links: Links;
}

interface Links {
  delivery_receipts: string;
  channel_metadata: string;
}

interface Meta {
  page: number;
  page_size: number;
  first_page_url: string;
  previous_page_url?: any;
  url: string;
  next_page_url?: any;
  key: string;
}
