interface Metadata {
    per: number;
    total: number;
    page: number;
  }

interface HeadOfHouse {
    id?: null | string | string;
  }

interface Household {
    createdAt: string;
    type: string;
    lastVisit?: any;
    title: string;
    updatedAt: string;
    householdScore?: any;
    id: string;
    headOfHouse: HeadOfHouse;
    kind: string;
  }
interface Survey {
    id?: any;
  }

  interface Org2 {
    desc: string;
    id: string;
    type: string;
    updatedAt: string;
    url: string;
    title: string;
    createdAt: string;
  }
  interface Org {
    id: string;
  }

  interface Team {
    id: string;
    org: Org;
    updatedAt: string;
    name: string;
    households: Household[];
    createdAt: string;
  }

interface Attachment {
    url: string;
    survey: Survey;
    type: string;
    household: Survey;
    kind: string;
    name: string;
    org: Survey;
    category: string;
    note: Survey;
    member: Survey;
    createdAt: string;
    user: Org;
    animal: Survey;
    updatedAt: string;
    id: string;
  }

export interface Navigator {
    email: string;
    middleName?: string;
    updatedAt: string;
    profile?: string;
    createdAt: string;
    lastName: string;
    phones: [];
    teams: Team[];
    firstName: string;
    id: string;
    auth: string;
    roles: string[];
    org: Org2;
    color: string;
    attachments: Attachment[];
    meta?: any;
  }

export interface NavigatorResponse {
    items: Navigator[];
    metadata: Metadata;
  }

export interface TableHeader {
    key: keyof Navigator;
    value: string;
}
