/* eslint-disable no-use-before-define */
export interface SurveyResultTypes {
  surveyName: string;
  sections: Section[];
}

interface Section {
  sectionTitle: string;
  questions: Question[];
}

interface Question {
  questionTitle: string;
  mainAnswer: string;
  mainScore: string;
  options: Option[];
}

interface Option {
  optionTitle: string;
  answer: string;
  score: string;
  value: string;
}
