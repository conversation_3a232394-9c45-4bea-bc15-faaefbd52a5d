/* eslint-disable no-use-before-define */
export interface MemberStatusProps {
  member: Member;
  kind: string;
  createdAt: string;
  endDate: null | string;
  updatedAt: string;
  name: string;
  startDate: string;
  address: Address[];
  location: null | string;
  state: string;
  id: string;
}

interface Address {
  updatedAt: string;
  team: Team;
  street: string;
  policy: Team;
  city: string;
  zip: string;
  school: Team;
  member: Team;
  memberStatus: Member;
  lat: null | null | string;
  street2: string;
  id: string;
  county: string;
  org: Team;
  kind: string;
  note: null;
  country: string;
  household: Team;
  location: Team;
  network: Team;
  lon: null | null | string;
  state: string;
  createdAt: string;
}

interface Team {
  id: null;
}

interface Member {
  id: string;
}
