/* eslint-disable no-use-before-define */
export interface NotesTypes {
  type: string;
  msg: string;
  createdAt: string;
  service: Service;
  member: Member;
  creator: Creator;
  title: string;
  id: string;
  updatedAt: string;
  attachments: any[];
  tags: Tag[];
  subtitle?: any;
  status: string;
}

interface Tag {
  createdAt: string;
  name: string;
  updatedAt: string;
  id: string;
  key: string;
  note: Org;
  color: string;
}

interface Creator {
  auth: string;
  email: string;
  firstName: string;
  roles: string[];
  org: Org;
  id: string;
  createdAt: string;
  attachments: Attachment[];
  lastName: string;
  updatedAt: string;
  color: string;
  profile: string;
  middleName: string;
}

interface Attachment {
  id: string;
  createdAt: string;
  survey: Service;
  category: string;
  org: Service;
  type: string;
  name: string;
  note: Service;
  member: Service;
  user: Org;
  updatedAt: string;
  kind: string;
  household: Service;
  animal: Service;
  url: string;
}

interface Member {
  referredBy: string;
  genderIdentity: string;
  pronouns: string;
  createdAt: string;
  email: string;
  middleName?: any;
  lang: string;
  lastName: string;
  score: string;
  lastAt: string;
  org: Org;
  status: string;
  firstName: string;
  sexualIdentity: string;
  roles: string[];
  updatedAt: string;
  id: string;
  dob: string;
  auth?: any;
  ethnicity: string;
  type: string;
  gender: string;
  color: string;
}

interface Org {
  id: string;
}

interface Service {
  id?: any;
}
