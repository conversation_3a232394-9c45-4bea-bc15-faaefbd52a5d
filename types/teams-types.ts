import React from 'react';

export interface Lead {
    name: string;
    image: string;
}

interface Member {
  id?: any;
}

interface Org {
  id: string;
}
interface Attachment {
  url: string;
  survey: Member;
  category: string;
  updatedAt: string;
  member: Member;
  animal: Member;
  name: string;
  kind: string;
  household: Org;
  type: string;
  note: Member;
  id: string;
  createdAt: string;
  user: Member;
  org: Member;
}

export interface Navigator {
  auth: string;
  profile?: any;
  id: string;
  attachments: Attachment[];
  roles: string[];
  color: string;
  firstName: string;
  middleName?: string;
  org: Org;
  createdAt: string;
  email: string;
  lastName: string;
  updatedAt: string;
}

interface Member2 {
  sexualIdentity: string;
  pronouns: string;
  color: string;
  lastName: string;
  email: string;
  score?: any;
  genderIdentity: string;
  updatedAt: string;
  middleName: string;
  type: string;
  dob: string;
  lastAt?: any;
  auth?: any;
  roles: string[];
  referredBy: string;
  id: string;
  lang: string;
  firstName: string;
  createdAt: string;
  gender: string;
  ethnicity: string;
  status: string;
  org: Org;
}
interface Household {
  attachments: Attachment[];
  members: Member2[];
  householdScore?: any;
  id: string;
  lastVisit?: any;
  kind: string;
  type: string;
  createdAt: string;
  headOfHouse: Member2;
  updatedAt: string;
  title: string;
  address: any[];
  pets: any[];
}

interface Address {
  zip: string;
  state: string;
  lon?: any;
  city: string;
  note?: any;
  member: Member;
  school: Member;
  network: Member;
  country: string;
  id: string;
  kind: string;
  county: string;
  household: Member;
  lat?: any;
  org: Member;
  createdAt: string;
  street: string;
  team: Org;
  updatedAt: string;
  street2?: any;
}

export interface Teams {
  org: Org;
  navigators: Navigator[];
  address: Address[];
  updatedAt: string;
  leads: Navigator[];
  name: string;
  id: string;
  households: Household[];
  createdAt: string;
  role?: string;
}

export interface TableHeader {
  key: keyof Teams;
  value: string;
}

export interface CustomizeTeamsFieldsProps {
  field: string;
  children?: React.ReactNode;
  record?: Teams;
  value: string | number;
  select?: boolean;
}
