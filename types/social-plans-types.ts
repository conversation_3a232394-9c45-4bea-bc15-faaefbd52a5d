/* eslint-disable no-use-before-define */
export interface SocialPlansProps {
    socialPlans: SocialPlan[];
    member: Member;
  }

  interface Member {
    updatedAt: string;
    auth?: any;
    notes: Note[];
    dob: string;
    org: Org;
    households: Household[];
    id: string;
    email: string;
    phones: Phone[];
    lang: string;
    referredBy: string;
    middleName?: any;
    attachments: Attachment[];
    genderIdentity: string;
    createdAt: string;
    firstName: string;
    schools: any[];
    gender: string;
    address: any[];
    color: string;
    type: string;
    headOfHouse?: any;
    score: string;
    lastName: string;
    pronouns: string;
    status: string;
    sexualIdentity: string;
    roles: string[];
    ethnicity: string;
    lastAt: string;
  }

  interface Attachment {
    createdAt: string;
    type: string;
    name: string;
    note: Reason;
    user: Reason;
    org: Reason;
    household: Reason;
    animal: Reason;
    category: string;
    id: string;
    member: Org;
    kind: string;
    survey: Reason;
    updatedAt: string;
    url: string;
  }

  interface Phone {
    org: Reason;
    number: string;
    updatedAt: string;
    label: string;
    id: string;
    network: Reason;
    user: Reason;
    member: Org;
    createdAt: string;
  }

  interface Household {
    org: Org2;
    updatedAt: string;
    title: string;
    lastVisit: string;
    householdScore?: string;
    id: string;
    kind: string;
    type: string;
    createdAt: string;
    headOfHouse: Org2;
  }

  interface Org2 {
    id?: string;
  }

  interface Note {
    createdAt: string;
    type: string;
    creator: Org;
    service: Reason;
    title: string;
    subtitle?: any;
    id: string;
    member: Org;
    msg: string;
    updatedAt: string;
  }

export interface SocialPlan {
    updatedAt: string;
    id: string;
    creator: string;
    status: string;
    type: string;
    org: Org;
    reason: Reason;
    endedAt?: any;
    title: string;
    startedAt?: any;
    createdAt: string;
    items: any[];
    reciever: string;
  }

  interface Items4 {
    items: Item3[];
    createdAt: string;
    network: Org;
    desc: string;
    socialPlan: Org;
    updatedAt: string;
    type: string;
    id: string;
    status: string;
  }

  interface Items3 {
    status: string;
    type: string;
    updatedAt: string;
    id: string;
    network: Org;
    socialPlan: Org;
    desc?: any;
    items: Item3[];
    createdAt: string;
  }

  interface Item3 {
    socialPlanID: string;
    desc: string;
    status: string;
    item: Org;
    updatedAt: string;
    createdAt: string;
    id: string;
    services: any[];
    creator: Org;
  }

  interface Items2 {
    id: string;
    updatedAt: string;
    createdAt: string;
    desc?: any;
    items: Item[];
    status: string;
    type: string;
    socialPlan: Org;
    network: Org;
  }

  interface Item2 {
    status: string;
    type: string;
    updatedAt: string;
    id: string;
    network: Org;
    socialPlan: Org;
    desc?: string;
    items: Item[];
    createdAt: string;
  }

  interface Item {
    socialPlanID: string;
    desc: string;
    status: string;
    item: Org;
    updatedAt: string;
    createdAt: string;
    id: string;
    services: Service[][];
    creator: Org;
  }

  interface Service {
    id: string;
    desc: string;
    type: string;
    createdAt: string;
    org: Org;
    name: string;
    status: string;
    updatedAt: string;
    service: string;
  }

  interface Reason {
    id?: any;
  }

  interface Org {
    id: string;
  }
