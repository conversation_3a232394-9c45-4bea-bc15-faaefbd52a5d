/* eslint-disable no-use-before-define */
import { Carrier } from './service-types';

export interface Network {
    phones: any[];
    id: string;
    updatedAt: string;
    status: string;
    contact?: string;
    types: string[];
    address: Address[];
    createdAt: string;
    services: Service[];
    name: string;
    org: Org;
    email?: string;
    website?: any;
    carriers: any[];
  }

interface Org {
    id: string;
  }

interface Address{
    street: string;
    street2: string;
    city: string;
    state: string;
    zip: string;
    kind: string;
}
  interface Service {
    updatedAt: string;
    status: string;
    desc: string;
    id: string;
    type: string;
    name: string;
    createdAt: string;
    service: string;
    org: Org;
  }
