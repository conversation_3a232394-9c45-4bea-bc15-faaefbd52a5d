{"name": "dona-workspace", "$schema": "node_modules/nx/schemas/project-schema.json", "sourceRoot": ".", "projectType": "application", "targets": {"build": {"executor": "@nx/next:build", "outputs": ["{options.outputPath}"], "defaultConfiguration": "production", "options": {"root": ".", "outputPath": "dist/dona-workspace"}, "configurations": {"development": {"outputPath": "."}, "production": {}}}, "serve": {"executor": "@nx/next:server", "defaultConfiguration": "development", "options": {"buildTarget": "dona-workspace:build", "dev": true}, "configurations": {"development": {"buildTarget": "dona-workspace:build:development", "dev": true}, "production": {"buildTarget": "dona-workspace:build:production", "dev": false}}}, "export": {"executor": "@nx/next:export", "options": {"buildTarget": "dona-workspace:build:production"}}, "test": {"executor": "@nx/jest:jest", "outputs": ["{workspaceRoot}/coverage/{projectName}"], "options": {"jestConfig": "jest.config.app.ts", "passWithNoTests": true}, "configurations": {"ci": {"ci": true, "codeCoverage": true}}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["./**/*.{ts,tsx,js,jsx}"]}}}, "tags": []}