{"compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2015", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "paths": {"@dona-workspace/api": ["api/src/index.ts"], "@dona-workspace/api/server": ["api/src/server.ts"]}}, "exclude": ["node_modules", "tmp", "jest.config.ts", "src/**/*.spec.ts", "src/**/*.test.ts", "**e2e/**/*", "dist/dona-workspace/**/*"]}