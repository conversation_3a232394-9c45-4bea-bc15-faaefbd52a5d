{"compilerOptions": {"jsx": "preserve", "allowJs": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "resolveJsonModule": true, "isolatedModules": true, "incremental": true, "plugins": [{"name": "next"}], "types": ["jest", "node"], "paths": {"@dona-workspace/api": ["api/src/index.ts"], "react": ["./node_modules/@types/react"]}}, "include": ["**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx", "next-env.d.ts", "types/**/*.d.ts"], "extends": "./tsconfig.base.json"}