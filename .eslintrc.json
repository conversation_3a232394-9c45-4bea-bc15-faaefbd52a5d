{
  "extends": [
    "plugin:@nx/react-typescript",
    "next",
    "next/core-web-vitals",
    "airbnb"
  ],
  "root": true,
  "ignorePatterns": [
    "!**/*",
    ".next/**/*"
  ],
  "plugins": [
    // "@nx"
  ],
  "overrides": [
    {
      "files": [
        "*.ts",
        "*.tsx"
      ],
      "extends": [
        "plugin:@nx/typescript",
        "airbnb"
      ],
      "rules": {
        "import/extensions": "off",
        "react/react-in-jsx-scope": "off",
        "react/jsx-filename-extension": "off",
        "react/require-default-props": "off",
        "import/no-extraneous-dependencies": "off"
      }
    },
    {
      "files": [
        "*.js",
        "*.jsx"
      ],
      "extends": [
        "plugin:@nx/javascript"
      ],
      "rules": {}
    }
  ],
  "rules": {
    "@next/next/no-html-link-for-pages": "off"
  },
  "env": {
    "jest": true
  }
}