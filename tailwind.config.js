const { createGlobPatternsForDependencies } = require('@nrwl/react/tailwind');
const { join } = require('path');

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    join(
      __dirname,
      '{src,pages,components,layouts}/**/*!(*.stories|*.spec).{ts,tsx,html}'
    ),
    ...createGlobPatternsForDependencies(__dirname),
  ],
  corePlugins: {
    preflight: false,
  },
  theme: {
    extend: {
      fontFamily: {
        'open-runde': ['OpenRunde', 'sans-serif'],
      },
      colors: {
        "primary": "#E97100",
        "secondary": "#008390",
        "dark-text": "#001018",
        "blue-base": "#1470C4",
        "gray-base": "#D5DCE2",
        "grey": "#747A7A",
      },
      backgroundImage: {
        "icon-hoh": "url('/icons/v2-navigation-icons/icon-hoh.svg')",
        "icon-cam": "url('/icons/v2-navigation-icons/icon-cam.svg')",
        "icon-edit": "url('/icons/icon-edit.svg')",
        "icon-chat": "url('/icons/v2-navigation-icons/icon-chat.svg')",
        "icon-bell": "url('/icons/v2-navigation-icons/icon-bell.svg')",
        "icon-drag": "url('/icons/icon-drag.svg')",
        "icon-clock": "url('/icons/icon-clock.svg')",
        "icon-clock-white": "url('/icons/icon-clock-white.svg')",
        "icon-check": "url('/icons/icon-check.svg')",
        "icon-check-white": "url('/icons/icon-check-white.svg')",
        "icon-flag": "url('/icons/icon-flag.svg')",
        "icon-filter": "url('/icons/icon-filter.svg')",
        "icon-reporting": "url('/icons/icon-reporting.svg')",
        "flag-bg": "url('/icons/bg-flag.svg')",
        "icon-tag": "url('/icons/v2-navigation-icons/icon-tag.svg')",
        "icon-attachment": "url('/icons/icon-attachment.svg')",
        "icon-clients": "url('/icons/v2-navigation-icons/icon-members.svg')",
        "icon-tasks": "url('/icons/v2-navigation-icons/icon-tasks.svg')",
        "icon-house": "url('/icons/v2-navigation-icons/icon-households.svg')",
        "icon-navigators": "url('/icons/v2-navigation-icons/icon-navigator.svg')",
        "icon-networks": "url('/icons/v2-navigation-icons/icon-networks.svg')",
        "icon-teams": "url('/icons/v2-navigation-icons/icon-teams.svg')",
        "icon-border": "url('/icons/v2-navigation-icons/icon-border.svg')",
      },
      screens: {
        'sm': '640px',
        'md': '768px',
        'md-lg': '900px',
        'lg': '1024px',
        '1165': '1165px',
        'xl': '1280px',
        '2xl': '1536px',
      },
    },
  },
  plugins: [],
};
