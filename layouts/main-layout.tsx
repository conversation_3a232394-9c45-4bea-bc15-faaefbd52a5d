/* eslint-disable @next/next/no-img-element */
import React, { ChangeEvent, useEffect, useState } from 'react';
import Head from 'next/head';
import Navigation from 'components/navigation-bar/navigation-bar';
import { Avatar, Button } from '@mui/material';
import AddIcon from '@mui/icons-material/Add'; // Import Add icon
import ErrorService from 'api/src/lib/error-service';
import { useModal } from 'contexts/modal-context/modal-context';
import { useLoading } from 'contexts/loading-context/loading-context';
import useAuth from 'hooks/use-auth';
import { useRouter } from 'next/router';
import SelectBox from 'components/select-box/select-box';
import Cookies from 'js-cookie';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { getLatestImage } from 'utils/helper';
import NotificationBell from 'components/notification-bell/notification-bell';

interface MainLayoutProps {
  children: React.ReactNode;
  // eslint-disable-next-line react/require-default-props
  buttonProps?: {
    addButton?: boolean;
    addButtonOnClick?: () => void;
    addButtonLabel?: string;
  };
}
// eslint-disable-next-line react/prop-types
function MainLayout({
  children = <div />,
  buttonProps = {
    addButton: true,
    addButtonOnClick: () => null,
    addButtonLabel: 'Add',
  },
}: MainLayoutProps) {
  const { showModal, hideModal } = useModal();
  const { hideLoading, showLoading } = useLoading();
  const { logout, user, teams } = useAuth();
  const { refresh } = useRefreshSSR();
  const [roles, setRoles] = useState('');
  const [selectedTeam, setSelectedTeam] = useState('');
  const router = useRouter();
  const onChangeTeam = async (e: ChangeEvent<HTMLInputElement>) => {
    Cookies.set('teamID', e.target.value.toLowerCase());
    setSelectedTeam(e.target.value.toLowerCase());
    await refresh();
  };

  useEffect(() => {
    setSelectedTeam(Cookies.get('teamID')?.toLowerCase() || '');
    setRoles(Cookies.get('roles') || '');
    if (ErrorService.mounted) return;
    ErrorService.mounted = true;
    ErrorService.setModalMethods({
      showModal,
      hideModal,
      showLoading,
      hideLoading,
    });
  }, [JSON.stringify(Cookies.get('teamID'))]);

  return (
    <>
      <Head>
        <title>Home Page</title>
      </Head>
      <div className="h-[60px] grid grid-flow-col items-center justify-between auto-cols-auto borderLineNoBottomPad">
        <div className="ml-5 cursor-pointer p-0 w-[108px] rounded-none h-10 bg-icon-tag" />
        <div className="mr-5 grid grid-flow-col items-center auto-cols-max gap-3">
          <div className="skinny-select">
            {/* {!roles.includes('admin') &&  */}
            {selectedTeam && (
              <SelectBox
                items={teams ? (teams as any) : []}
                defaultValue={selectedTeam}
                minWidth={200}
                key="teamselect"
                onChange={onChangeTeam}
                label="Select Team"
                keyVal="team"

              />
            )}
          </div>
          <NotificationBell />
          <Avatar
            onClick={() => {
              router.replace('/profile/me');
              // showModal('Warning', 'You are going to be logout', () => {
              //   logout();
              // });
            }}
            className="cursor-pointer w-8 h-8"
            src={getLatestImage(user?.attachments as any, 'userProfile')}
          />
        </div>
      </div>
      <div
        className="grid grid-flow-col gap-3"
        style={{ gridTemplateColumns: 'min-content 1fr' }}
      >
        <Navigation />
        <div className="pb-5">{children}</div>
      </div>
      {buttonProps.addButton && (
        <Button
          style={{
            position: 'fixed',
            bottom: '20px',
            height: '42px',
            minWidth: '150px',
            right: '20px',
          }}
          variant="contained"
          aria-label="add"
          onClick={() => {
            // eslint-disable-next-line no-unused-expressions
            buttonProps.addButtonOnClick && buttonProps.addButtonOnClick();
            // Handle button click event
          }}
        >
          <AddIcon style={{ color: 'white' }} />
          {' '}
          {/* Set the icon color */}
          {buttonProps.addButtonLabel}
        </Button>
      )}
    </>
  );
}

export default MainLayout;
