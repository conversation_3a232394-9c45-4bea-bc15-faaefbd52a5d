/* eslint-disable no-void */
/* eslint-disable react/jsx-props-no-multi-spaces */
import React, { useEffect } from 'react';
import Head from 'next/head';
import ErrorService from 'api/src/lib/error-service';
import { useModal } from 'contexts/modal-context/modal-context';
import { useLoading } from 'contexts/loading-context/loading-context';
import ArrowBack from '@mui/icons-material/ArrowBack';
// import { useRouter } from 'next/router';
import { capitalizeWords } from 'utils/helper';
import { SvgIconComponent } from '@mui/icons-material';
import { Button } from '@mui/material';

interface MemberLayoutProps {
    children: React.ReactNode;
    leftButtonOnClick?: () => void;
    rightButtonOnClick?: () => void;
    LeftIcon?: any;
    button: boolean;
    ButtonIcon?: SvgIconComponent;
    isIconRight?: boolean;
    buttonText?: string;
    rightButtonClass?: string;
    title: string;
    subTitle: string;
}

// eslint-disable-next-line react/prop-types
function CustomLayout({
  children, LeftIcon = ArrowBack,
  leftButtonOnClick = () => void 0, rightButtonOnClick = () => void 0,
  buttonText = 'Button', ButtonIcon = ArrowBack,
  button, title, subTitle, rightButtonClass = '', isIconRight = true,
}: MemberLayoutProps) {
  const { showModal, hideModal } = useModal();
  const { hideLoading, showLoading } = useLoading();
  // const router = useRouter();

  useEffect(() => {
    if (ErrorService.mounted) return;
    ErrorService.mounted = true;
    ErrorService.setModalMethods({
      showModal, hideModal, showLoading, hideLoading,
    });
  }, []);

  return (
    <>
      <Head>
        <title>{title}</title>
      </Head>
      <div
        style={{
          width: '100%',
          height: 76,
          backgroundColor: '#fff',
          zIndex: 2,
          borderBottom: '1px solid #E0E0E0',
        }}
        className="grid grid-flow-col
                 gap-3  pl-5 auto-cols-max
                 items-center relative"
      >
        <LeftIcon
          onClick={leftButtonOnClick}
          style={{ width: 35, height: 35 }}
          className="text-[#646F79] text-2xl cursor-pointer"
        />
        <p className="font-[500] p-0 m-0 text-[26px]">
          {capitalizeWords(title, true)}
          <span className="font-[400]">{subTitle}</span>
        </p>
        {button && (
          <Button
            onClick={rightButtonOnClick}
            variant="outlined"
            className={rightButtonClass.length > 0 ? rightButtonClass : 'rounded-lg absolute right-12 w-auto h-11 bg-primary border-primary text-white'}
          >
            {isIconRight && <ButtonIcon />}
            <p>{buttonText}</p>
          </Button>
        )}
      </div>
      <div className="grid grid-flow-row auto-cols-auto w-screen">
        {children}
      </div>
    </>
  );
}

export default CustomLayout;
