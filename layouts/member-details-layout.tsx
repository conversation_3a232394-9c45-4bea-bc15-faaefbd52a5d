/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable no-void */
/* eslint-disable react/jsx-props-no-multi-spaces */
import React, { useEffect } from 'react';
import Head from 'next/head';
import ErrorService from 'api/src/lib/error-service';
import { useModal } from 'contexts/modal-context/modal-context';
import { useLoading } from 'contexts/loading-context/loading-context';
import { useRouter } from 'next/router';
import MainLayout from 'layouts/main-layout';
import MemberInnerPageHeader from 'components/inner-page-header/member';

interface MemberLayoutProps {
  children: React.ReactNode;
  fullName: string;
  memberId: string;
  active: string;
  add?: boolean;
  addLabel?: string;
  add2?: boolean;
  addLabel2?: string;
  updateLabel: string;
  updateUrl?: any;
  rightSide?: React.ReactNode;
  replaceTabs?: any;
  replaceTab?: any;
  replaceTabClick?: (tab: any) => void;
  borderBottom?: boolean;
}

// eslint-disable-next-line react/prop-types
function MemberLayout({
  children,
  fullName = 'No Name',
  memberId = '',
  active,
  add,
  addLabel,
  add2,
  addLabel2,
  updateLabel,
  updateUrl,
  rightSide,
  replaceTabs,
  replaceTab,
  replaceTabClick,
  borderBottom = false,
}: MemberLayoutProps) {
  const { showModal, hideModal } = useModal();
  const { hideLoading, showLoading } = useLoading();
  const router = useRouter();

  useEffect(() => {
    if (ErrorService.mounted) return;
    ErrorService.mounted = true;
    ErrorService.setModalMethods({
      showModal,
      hideModal,
      showLoading,
      hideLoading,
    });
  }, []);

  return (
    <MainLayout
      buttonProps={{
        addButton: false,
        addButtonOnClick: () => {
          router.push('/members/add-edit/add');
        },
        addButtonLabel: 'Add Member',
      }}
    >
      <Head>
        <title>Member Details</title>
      </Head>
      <div
        style={{
          width: '100%',
          backgroundColor: '#fff',
          zIndex: 2,
        }}
        className="grid grid-cols-[1fr_auto] w-full items-center relative mb-3"
      >
        <MemberInnerPageHeader
          title="Members"
          memberId={memberId}
          active={active}
          fullName={fullName}
          addLabel={addLabel}
          updateLabel={updateLabel}
          add={add}
          updateUrl={updateUrl}
          rightSide={rightSide}
          replaceTabs={replaceTabs}
          replaceTab={replaceTab}
          replaceTabClick={replaceTabClick}
          add2={add2}
          addLabel2={addLabel2}
          borderBottom={borderBottom}
        />
      </div>
      <div className="grid grid-flow-row auto-cols-auto pr-10">{children}</div>
    </MainLayout>
  );
}

export default MemberLayout;
