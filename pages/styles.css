@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600&display=swap');
@tailwind components;
@tailwind utilities;
@tailwind base;

@layer components {
  .innerSpace {
    @apply pl-5 pr-5 pt-5;
  }

  .no-scrollbar::-webkit-scrollbar {
    display: none;
  }

  .no-scrollbar {
    scrollbar-width: none; /* For Firefox */
    -ms-overflow-style: none; /* For Internet Explorer 10+ */
  }
  .clamp-3 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
  }
}

@font-face {
  font-family: 'OpenRunde';
  src: url('/fonts/OpenRunde/OpenRunde-Regular.woff2') format('woff2'),
       url('/fonts/OpenRunde/OpenRunde-Regular.woff') format('woff');
  font-weight: 400;
  font-style: normal;
}

@font-face {
  font-family: 'OpenRunde';
  src: url('/fonts/OpenRunde/OpenRunde-Medium.woff2') format('woff2'),
       url('/fonts/OpenRunde/OpenRunde-Medium.woff') format('woff');
  font-weight: 500;
  font-style: normal;
}

@font-face {
  font-family: 'OpenRunde';
  src: url('/fonts/OpenRunde/OpenRunde-SemiBold.woff2') format('woff2'),
       url('/fonts/OpenRunde/OpenRunde-SemiBold.woff') format('woff');
  font-weight: 600;
  font-style: normal;
}

@font-face {
  font-family: 'OpenRunde';
  src: url('/fonts/OpenRunde/OpenRunde-Bold.woff2') format('woff2'),
       url('/fonts/OpenRunde/OpenRunde-Bold.woff') format('woff');
  font-weight: 700;
  font-style: normal;
}

body {
  font-family: 'Inter', sans-serif;
  overflow: auto;
  /* Hide scrollbar for WebKit browsers (Chrome, Safari) */
  scrollbar-width: none; /* For Firefox */
  -ms-overflow-style: none; /* For Internet Explorer and Edge */
  min-width: 768px;
}

button {
  font-family: inherit;
}

.edit-icon:hover path {
  --stroke-width: 1.9;
}


.borderLine {
  border: 1px solid #d5dce2;
  border-top: 0;
  border-left: 0;
  border-right: 0;
  padding-bottom: 20px;
}
.borderLineNoBottomPad {
  border: 1px solid #d5dce2;
  border-top: 0;
  border-left: 0;
  border-right: 0;
}
/* globalStyles.css */
.MuiModal-root {
  z-index: 1300 !important;
}

button.gridjs-sort-neutral {
  opacity: 0.3;
  background-image: url('data:image/svg+xml;base64,PHN2ZyB2ZXJzaW9uPSIxLjEiIGlkPSJDYXBhXzEiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyIgeG1sbnM6eGxpbms9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkveGxpbmsiIHg9IjBweCIgeT0iMHB4IgoJIHdpZHRoPSI0MDEuOTk4cHgiIGhlaWdodD0iNDAxLjk5OHB4IiB2aWV3Qm94PSIwIDAgNDAxLjk5OCA0MDEuOTk4IiBzdHlsZT0iZW5hYmxlLWJhY2tncm91bmQ6bmV3IDAgMCA0MDEuOTk4IDQwMS45OTg7IgoJIHhtbDpzcGFjZT0icHJlc2VydmUiPgo8Zz4KCTxnPgoJCTxwYXRoIGQ9Ik03My4wOTIsMTY0LjQ1MmgyNTUuODEzYzQuOTQ5LDAsOS4yMzMtMS44MDcsMTIuODQ4LTUuNDI0YzMuNjEzLTMuNjE2LDUuNDI3LTcuODk4LDUuNDI3LTEyLjg0NwoJCQljMC00Ljk0OS0xLjgxMy05LjIyOS01LjQyNy0xMi44NUwyMTMuODQ2LDUuNDI0QzIxMC4yMzIsMS44MTIsMjA1Ljk1MSwwLDIwMC45OTksMHMtOS4yMzMsMS44MTItMTIuODUsNS40MjRMNjAuMjQyLDEzMy4zMzEKCQkJYy0zLjYxNywzLjYxNy01LjQyNCw3LjkwMS01LjQyNCwxMi44NWMwLDQuOTQ4LDEuODA3LDkuMjMxLDUuNDI0LDEyLjg0N0M2My44NjMsMTYyLjY0NSw2OC4xNDQsMTY0LjQ1Miw3My4wOTIsMTY0LjQ1MnoiLz4KCQk8cGF0aCBkPSJNMzI4LjkwNSwyMzcuNTQ5SDczLjA5MmMtNC45NTIsMC05LjIzMywxLjgwOC0xMi44NSw1LjQyMWMtMy42MTcsMy42MTctNS40MjQsNy44OTgtNS40MjQsMTIuODQ3CgkJCWMwLDQuOTQ5LDEuODA3LDkuMjMzLDUuNDI0LDEyLjg0OEwxODguMTQ5LDM5Ni41N2MzLjYyMSwzLjYxNyw3LjkwMiw1LjQyOCwxMi44NSw1LjQyOHM5LjIzMy0xLjgxMSwxMi44NDctNS40MjhsMTI3LjkwNy0xMjcuOTA2CgkJCWMzLjYxMy0zLjYxNCw1LjQyNy03Ljg5OCw1LjQyNy0xMi44NDhjMC00Ljk0OC0xLjgxMy05LjIyOS01LjQyNy0xMi44NDdDMzM4LjEzOSwyMzkuMzUzLDMzMy44NTQsMjM3LjU0OSwzMjguOTA1LDIzNy41NDl6Ii8+Cgk8L2c+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPGc+CjwvZz4KPC9zdmc+');
}

button.gridjs-sort {
  display: flex;
  align-items: center;
  float: right;
  background-color: transparent;
  background-repeat: no-repeat;
  background-position-x: center;
  cursor: pointer;
  padding: 0;
  margin: 0;
  border: none;
  outline: none;
  background-size: contain;
}

/* Add styles for the sort asc and sort desc classes */
button.gridjs-sort-asc {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGNsYXNzPSJpY29uIj48cGF0aCBkPSJNNTQ0IDEyOEwxMjggNTEyaDc2OEw1NDQgMTI4eiIgZmlsbD0iIzAwMCIvPjwvc3ZnPg==') !important;
}

button.gridjs-sort-desc {
  background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTAiIGhlaWdodD0iMTAiIHZpZXdCb3g9IjAgMCAxMDI0IDEwMjQiIGNsYXNzPSJpY29uIj48cGF0aCBkPSJNNTI0IDg2NkgxMjhMMzg0IDMybDQzMiA0MzIgMzYyLTQzMiA0MzIgMzIweiIgZmlsbD0iIzAwMCIvPjwvc3ZnPg==') !important;
}

.gridjs-pagination {
  color: #3d4044;
  border: 1px solid #d2d6dc;
}
.gridjs-pagination::after {
  content: '';
  display: block;
  clear: both;
}
.gridjs-pagination .gridjs-summary {
  float: left;
  margin-top: 5px;
}
.gridjs-pagination .gridjs-pages {
  float: right;
}
.gridjs-pagination .gridjs-pages button {
  padding: 5px 14px;
  border: 1px solid #d2d6dc;
  background-color: #fff;
  border-right: none;
  outline: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.gridjs-pagination .gridjs-pages button:focus {
  box-shadow: 0 0 0 2px rgba(149, 189, 243, 0.5);
  position: relative;
  margin-right: -1px;
  border-right: 1px solid #d2d6dc;
}
.gridjs-pagination .gridjs-pages button:hover {
  background-color: #f7f7f7;
  color: rgb(60, 66, 87);
  outline: none;
}
.gridjs-pagination .gridjs-pages button:disabled,
.gridjs-pagination .gridjs-pages button[disabled],
.gridjs-pagination .gridjs-pages button:hover:disabled {
  cursor: default;
  background-color: #fff;
  color: #6b7280;
}
.gridjs-pagination .gridjs-pages button.gridjs-spread {
  cursor: default;
  box-shadow: none;
  background-color: #fff;
}
.gridjs-pagination .gridjs-pages button.gridjs-currentPage {
  background-color: #f7f7f7;
  font-weight: bold;
}
.gridjs-pagination .gridjs-pages button:last-child {
  border-bottom-right-radius: 6px;
  border-top-right-radius: 6px;
  border-right: 1px solid #d2d6dc;
}
.gridjs-pagination .gridjs-pages button:first-child {
  border-bottom-left-radius: 6px;
  border-top-left-radius: 6px;
}
.gridjs-pagination .gridjs-pages button:last-child:focus {
  margin-right: 0;
}

.skinny-select div {
  height: 50px;
  font-size: 0.97em;
}

.grey-btn {
  background: #F7F8F8 !important;
  height: 42px;
  transition: none;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 15px;
  white-space: nowrap;
}

.grey-btn:hover {
  background: #eef1f1 !important;
}

.no-child-p-margin p {
  margin: 0;
}

.card-upload {
  border: 1px solid #DCDFDF;
  border-radius: 6px;
  /* padding: 10px; */
}

.card-upload.no-hover:hover {
  border: 1px solid #DCDFDF !important;
}

.card-upload:hover {
  border: 1px solid #262D2D !important;
  background-color: #fcfcfc !important;
}

.border-all {
  border: 1px solid #DCDFDF;
}

.border-bottom {
  border-bottom: 1px solid #DCDFDF;
}

.border-top {
  border-top: 1px solid #DCDFDF;
}

@keyframes flicker {
  0% { opacity: 1; }
  50% { opacity: 0.4; }
  100% { opacity: 1; }
}

.flicker {
  animation: flicker 1.2s infinite ease-in-out;
}

.problem-type-card {
  width: 250px;
  height: 180px;
  background-color: #fafafa;
  cursor: pointer;
  border-radius: 10px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  font-size: 15px;
  gap: 8px;
  border: 1px solid #c7d0d8;
}

.problem-type-card:hover {
  background-color: #ffffff;
  border: 1px solid #ee7502;
}

.consent-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Arial', sans-serif;
  line-height: 1.6;
  color: #333;
}

.consent-container .consent-form {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.consent-container .form-header {
  text-align: center;
  margin-bottom: 30px;
  border-bottom: 3px solid #2c5aa0;
  padding-bottom: 20px;
}

.consent-container .form-header h1 {
  color: #2c5aa0;
  font-size: 28px;
  margin-bottom: 10px;
  font-weight: bold;
}

.consent-container .subtitle {
  font-size: 16px;
  color: #666;
  margin: 0;
}

.consent-container .form-section {
  margin-bottom: 25px;
}

.consent-container .form-section h2 {
  color: #2c5aa0;
  font-size: 20px;
  margin-bottom: 15px;
  border-bottom: 2px solid #e0e0e0;
  padding-bottom: 5px;
}

.consent-container .form-section p {
  margin-bottom: 10px;
}

.consent-container .form-section ul {
  margin: 10px 0;
  padding-left: 20px;
  list-style-type: disc;
}

.consent-container .form-section li {
  margin-bottom: 8px;
}

.consent-container .form-section ul ul {
  margin-top: 5px;
  margin-bottom: 5px;
}

.consent-container .contact-box {
  background: #f8f9fa;
  border: 1px solid #dee2e6;
  border-radius: 5px;
  padding: 15px;
  margin: 10px 0;
}

.consent-container .contact-box p {
  margin: 5px 0;
}

.consent-container .signature-section {
  margin-top: 40px;
  border-top: 2px solid #2c5aa0;
  padding-top: 30px;
}

.consent-container .participant-info {
  display: flex;
  gap: 30px;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.consent-container .input-group {
  flex: 1;
  min-width: 200px;
}

.consent-container .input-group label {
  display: block;
  font-weight: bold;
  margin-bottom: 5px;
  color: #2c5aa0;
}

.consent-container .input-group input {
  width: 100%;
  padding: 10px;
  border: 2px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
  transition: border-color 0.3s;
}

.consent-container .input-group input:focus {
  outline: none;
  border-color: #2c5aa0;
}

.consent-container .signature-area {
  margin-top: 20px;
}

.consent-container .signature-area label {
  display: block;
  font-weight: bold;
  margin-bottom: 10px;
  color: #2c5aa0;
}

.consent-container .signature-canvas-container {
  border: 2px solid #ddd;
  border-radius: 4px;
  display: inline-block;
  background: #ffffff;
}

.consent-container .signature-canvas {
  display: block;
  border-radius: 4px;
  background-color: #ffffff !important;
}

.consent-container .signature-line {
  margin-top: 10px;
  text-align: center;
  border-top: 1px solid #333;
  padding-top: 5px;
  width: 500px;
  font-size: 14px;
  color: #666;
}

.consent-container .form-controls {
  margin-top: 20px;
  text-align: center;
}

.consent-container .btn {
  padding: 12px 24px;
  border: none;
  border-radius: 5px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.consent-container .btn-primary {
  background: #2c5aa0;
  color: white;
  margin-left: 5px;
}

.consent-container .btn-primary:hover {
  background: #1e3f73;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(44, 90, 160, 0.3);
}

.consent-container .btn-secondary {
  background: #6c757d;
  color: white;
  margin-right: 5px;
}

.consent-container .btn-secondary:hover {
  background: #545b62;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3);
}

/* Print styles for PDF generation */
@media print {
  .consent-container .form-controls {
    display: none !important;
  }

  .consent-container .consent-form {
    box-shadow: none;
    border: none;
    padding: 20px;
  }

  .consent-container .signature-canvas-container {
    border: 1px solid #000;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .consent-container {
    padding: 10px;
  }

  .consent-container .consent-form {
    padding: 20px;
  }

  .consent-container .participant-info {
    flex-direction: column;
    gap: 15px;
  }

  .consent-container .signature-canvas-container,
  .consent-container .signature-line {
    width: 100%;
    max-width: 100%;
  }

  .consent-container .signature-canvas {
    width: 100%;
    max-width: 100%;
  }

  .consent-container .form-controls {
    flex-direction: column;
    align-items: center;
  }

  .consent-container .btn {
    width: 200px;
  }
}
