import { Button } from '@mui/material';
import { GetServerSideProps } from 'next/types';
import { parseCookies } from 'nookies';

export default function Index() {
  return (
    <div id="welcome">
      <h1>Welcome to the Next.js Boilerplate</h1>
      <Button variant="contained" color="primary" href="/login">
        Login
      </Button>

    </div>
  );
}

// serverside props

export const getServerSideProps: GetServerSideProps = async (context) => {
  const cookies = parseCookies(context);
  // eslint-disable-next-line prefer-destructuring
  const token = cookies.token;
  
  if (!token && context.req.url !== '/login') {
    return {
      redirect: {
        destination: '/login',
        permanent: false,
      },
    };
  }

  // Provide your own type for the return data
  return {
    props: {},
  };
};
