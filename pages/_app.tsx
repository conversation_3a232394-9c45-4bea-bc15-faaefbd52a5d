/* eslint-disable react/jsx-props-no-spreading */
/* eslint-disable no-use-before-define */
import { AppProps } from 'next/app';
import './styles.css';
import { CssBaseline } from '@mui/material';
import ThemeProvider from '@mui/system/ThemeProvider';
import theme from 'theme/mui-theme';
import ConstantsProvider from 'contexts/constant-context/constants-provider';
import AuthProvider from 'contexts/auth-context/auth-provider';
import ErrorBoundary from 'pages/_errorHandler';
import {
  LoadingProvider,
  useLoading,
} from 'contexts/loading-context/loading-context';
import { ModalProvider } from 'contexts/modal-context/modal-context';
import { useRouter } from 'next/router';
import { useEffect } from 'react';
import Script from 'next/script';
import API from 'api/src/lib/api';

function CustomApp({ Component, pageProps }: AppProps) {
  const isProd = process.env.NODE_ENV === 'production';
  return (
    <ThemeProvider theme={theme}>
      <ErrorBoundary>
        <LoadingProvider>
          <ModalProvider>
            <AuthProvider>
              <ConstantsProvider>
                <main className="app">
                  <CssBaseline />
                  {/* Wrap the component with RouteChangeHandler */}
                  <RouteChangeHandler>
                    {isProd && <Script src="/newrelic.js" />}
                    <Component {...pageProps} />
                  </RouteChangeHandler>
                </main>
              </ConstantsProvider>
            </AuthProvider>
          </ModalProvider>
        </LoadingProvider>
      </ErrorBoundary>
    </ThemeProvider>
  );
}

function RouteChangeHandler({ children }: any) {
  const { showLoading, hideLoading } = useLoading();
  const router = useRouter();

  const constantFetcher = async () => {
    const request = await API.CONSTANTS.fetchConstants();
    sessionStorage.setItem('constants', JSON.stringify(request));
  };

  useEffect(() => {
    if (typeof window !== 'undefined') {
      const storage = window && window?.sessionStorage.getItem('constants');
      if (!storage) {
        constantFetcher();
      }
    }
  }, []);
  useEffect(() => {
    const handleRouteChangeStart = () => {
      showLoading();
    };

    const handleRouteChangeComplete = () => {
      hideLoading();
    };

    router.events.on('routeChangeStart', handleRouteChangeStart);
    router.events.on('routeChangeComplete', handleRouteChangeComplete);
    router.events.on('routeChangeError', handleRouteChangeComplete);

    return () => {
      router.events.off('routeChangeStart', handleRouteChangeStart);
      router.events.off('routeChangeComplete', handleRouteChangeComplete);
      router.events.off('routeChangeError', handleRouteChangeComplete);
    };
  }, [router, showLoading, hideLoading]);

  // Render children within the context provider
  // eslint-disable-next-line react/jsx-no-useless-fragment
  return <>{children}</>;
}

export default CustomApp;
