/* eslint-disable max-len */
import { Button } from '@mui/material';
import API from 'api/src/lib/api';
import DateSelector from 'components/date-selector/date-selector';
import MainPageHeader from 'components/main-page-header';
import AssessmentData from 'components/reporting-sections/assessment-data';
import { FullDataReportProvider } from 'components/reporting-sections/context';
import MemberDemographics from 'components/reporting-sections/member-demographics';
import MemberLocationSection from 'components/reporting-sections/member-location';
import OperationalData from 'components/reporting-sections/operational-data';
import PlanSection from 'components/reporting-sections/plan-section';
import TaskData from 'components/reporting-sections/task-data';
import TabsView from 'components/tabs-view';
import { parse } from 'cookie';
import useAuth from 'hooks/use-auth';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { useEffect, useState } from 'react';
import { ReportingResponse } from 'types/reporting-types';
import { isEmptyObject } from 'utils/helper';

const defaultRange = {
  startDate: '2023-01-01',
  endDate: new Date().toISOString().slice(0, 10),
};
const Tabs = [{
  title: 'Member Demographics',
  key: 'member-demographics',
},
{
  title: 'Member Location & Need',
  key: 'member-location',
},
{
  title: ' Assessment Data',
  key: 'assessment-data',
},
{
  title: 'Plan Data',
  key: 'plan-data',
},
{
  title: 'Task Data',
  key: 'task-data',
},
{
  title: 'Operational Data',
  key: 'operational-data',

},
];
interface ReportingProps {
    reports: ReportingResponse;
}
function Reporting({ reports }: ReportingProps) {
  const [section, setSection] = useState('member-demographics');
  const [exportStartDate, setExportStartDate] = useState(defaultRange.startDate);
  const [exportEndDate, setExportEndDate] = useState(defaultRange.endDate);
  const [data, setData] = useState<ReportingResponse>(reports);
  const { user } = useAuth();

  const handleDateChange = async (endDate: string, startDate: string) => {
    const model = {
      startDate,
      endDate,
      org: user?.org?.id,
    };
    setExportStartDate(startDate);
    setExportEndDate(endDate);
    const request = await API.REPORTS.fetchReporting(model) as unknown as ReportingResponse;
    setData(request);
  };

  useEffect(() => {
    setData(reports);
  }, [reports]);

  if (isEmptyObject(reports)) {
    return <div />;
  }
  const handleExport = async () => {
    const model = {
      startDate: exportStartDate,
      endDate: exportEndDate,
      org: user?.org?.id,
    };
    const request = await API.REPORTS.exportReporting(model) as unknown as ReportingResponse;
    if (request) {
      window.open(request as unknown as string, '_blank');
    }
  };
  return (
    <FullDataReportProvider>
      <MainLayout buttonProps={{
        addButton: false,
        addButtonOnClick: () => {
        // router.push('/members/add-edit/add');
        },
        addButtonLabel: 'Add Member',
      }}
      >
        <MainPageHeader
          title="Reporting"
          subTitle={null}
          renderRight={(
            <div className="flex flex-row gap-2">
              <DateSelector
                onChange={handleDateChange as any}
                range
              />
              <Button onClick={handleExport} variant="contained" className="ml-2 rounded-md">Export</Button>
            </div>
        )}
        />
        <div className="mt-5 overflow-auto">
          <TabsView
            tabs={Tabs}
            className="grid grid-flow-col gap-0 auto-cols-max"
            value="member-demographics"
            callback={(value) => {
              setSection(value);
            }}
          />
          {section === 'member-demographics' && (
          <MemberDemographics reports={data.memberDemographics} />
          )}
          {section === 'member-location' && (
          <MemberLocationSection locations={data.locations.locations} />
          )}
          {
            section === 'assessment-data' && (
            <AssessmentData reports={data.assessments} />
            )
        }
          {
            section === 'plan-data' && (
            <PlanSection reports={data.plansData} />
            )
        }
          {
            section === 'task-data' && (
            <TaskData reports={data.taskData} />
            )
        }
          {
            section === 'operational-data' && (
            <OperationalData reports={data.operationalData} />
            )
        }
        </div>

      </MainLayout>
    </FullDataReportProvider>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req } = context;
  let token;
  let orgId;
  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token; // get cookie from context.req.headers.cookie named token
  }
  try {
    const request = token && orgId ? await API.REPORTS.fetchReporting({
      ...defaultRange,
      org: orgId,
    }, 'token') : null;
    return {
      props: {
        reports: request || [],
      },
    };
  } catch (error) {
    return {
      props: {
        reports: [],
      },
    };
  }
}

export default Reporting;
