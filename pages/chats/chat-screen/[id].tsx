/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import SafeDate from 'components/date-time/SafeDate';
import MainLayout from 'layouts/main-layout';
import ChatInnerPageHeader from 'components/inner-page-header/chat';
import {
  Avatar,
  AvatarGroup,
  Button,
  InputAdornment,
  TextField,
} from '@mui/material';
import useAuth from 'hooks/use-auth';
import { SendOutlined } from '@mui/icons-material';
import { ChatConversationTypes } from 'types/chat-conversation-types';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import InfoOutlinedIcon from '@mui/icons-material/InfoOutlined';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';

interface ChatProps {
  chats: ChatConversationTypes;
  chatDetails: {
    id: string;
    conversationFriendlyName?: string;
    title?: string;
  };
}
function ChatScreen({ chats, chatDetails }: ChatProps) {
  const { user } = useAuth();
  const [textMessage, setMessage] = useState('');
  const endOfMessagesRef = useRef(null);
  const { refresh } = useRefreshSSR();
  const { query } = useRouter();
  const [participants, setParticipants] = useState<
    Array<{ firstName: string }>
  >([]);
  const parseUserMetadata = (message: any) => {
    const stringifiedUser = JSON.parse(message.attributes);
    return stringifiedUser as any;
  };

  const sendMessage = async () => {
    if (textMessage === '') return;
    const model = {
      author: user?.id,
      body: textMessage,
      attributes: {
        auth: parseUserMetadata(chats.messages[0]).auth,
        profile: user?.profile || "",
        name: `${user?.firstName} ${user?.lastName}`,
        uuid: user?.id,
      },
    };
    await API.CHATS.sendMessage(model, query?.chatId as string);
    await refresh();
    setMessage('');
    // eslint-disable-next-line no-console
  };

  useEffect(() => {
    if (endOfMessagesRef.current) {
      (endOfMessagesRef as any).current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [chats.messages]);
  useEffect(() => {
    setParticipants(JSON.parse(sessionStorage.getItem('participants') || '[]'));
  }, []);
  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <div className="mt-4 pr-5">
        <ChatInnerPageHeader
          title="Chats"
          add={false}
          titleLink
          btnName="New Chat"
          chatTitle={
            chatDetails.conversationFriendlyName
            || participants.map((p) => p.firstName).join(', ')
          }
          rightSide={(
            <div className="flex items-center gap-3">
              <Button
                onClick={async () => {
                  await API.CHATS.deleteChat(chatDetails.id as string);
                  window.location.href = '/chats';
                }}
                component="label"
                className="rounded-md m-0 text-[15px] text-[#E42B57] grey-btn"
                sx={{
                  padding: '11px 15px !important', // Works correctly
                }}
              >
                <DeleteOutlinedIcon
                  style={{ width: '16px', height: '16px' }}
                  className="mr-2"
                />
                Delete Thread
              </Button>
              <Button
                onClick={() => {
                  window.location.href = `/chats/chat-details/${query?.id}`;
                }}
                component="label"
                className="rounded-md m-0 text-[15px] text-[#E97100] grey-btn"
                sx={{
                  padding: '11px 15px !important', // Works correctly
                }}
              >
                <InfoOutlinedIcon
                  style={{ width: '16px', height: '16px' }}
                  className="mr-2"
                />
                Chat Details
              </Button>
            </div>
          )}
        />
      </div>

      <div className="grid grid-flow-row gap-3 pl-5 pr-10 placeholder-teal-100 relative h-[54vh] mt-4">
        {/* Messages container with adjusted height and padding-bottom */}
        <div className="overflow-y-auto no-scrollbar">
          {' '}
          {/* Adjust the '200px' and 'pb-20' as per your requirements */}
          {chats.messages.map((message, index) => {
            const authorInfo = parseUserMetadata(message);
            return (
              <div
                key={message.sid}
                ref={
                  index === chats.messages.length - 1 ? endOfMessagesRef : null
                }
                className="flex justify-between pt-5 gap-2 borderLine items-start"
              >
                <div className="flex gap-2 flex-1">
                  <AvatarGroup max={2} spacing="small">
                    <Avatar src={authorInfo?.profile} />
                  </AvatarGroup>
                  <div className="flex flex-col w-full">
                    <p className="p-0 m-0 font-[500] text-sm">
                      {authorInfo?.name}
                    </p>
                    <p className="p-0 m-0 font-[300] text-sm break-words whitespace-pre-wrap w-full">
                      {message.body}
                    </p>
                  </div>
                </div>
                <p className="p-0 m-0 font-[300] text-xs whitespace-nowrap">
                  <SafeDate date={message.date_created} time />
                </p>
              </div>
            );
          })}
        </div>
      </div>

      <div className="bottom-0 left-0 py-5 pl-5 pr-10 m-0 mt-5 bg-[#F7F8FA] w-full">
        <TextField
          style={{ border: 'none', width: '100%' }}
          className="bg-white m-3"
          onChange={(e) => setMessage(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
              // This ensures that the function doesn't trigger when Shift+Enter is pressed
              e.preventDefault(); // Prevents adding a newline
              sendMessage(); // Replace with your actual send message function
            }
          }}
          value={textMessage}
          placeholder="Type your message here..."
          InputProps={{
            sx: {
              '& .MuiInputBase-input': {
                paddingLeft: '45px',
              },
            },
            startAdornment: (
              <InputAdornment position="start" className="absolute top-8">
                <Avatar
                  className="w-8 h-8"
                  alt="User Avatar"
                  src={user?.profile}
                />
              </InputAdornment>
            ),
          }}
          multiline
          rows={5}
        />
        <div className="grid grid-flow-col pl-3 auto-cols-auto place-content-between">
          <Button
            onClick={sendMessage}
            variant="contained"
            color="success"
            className="rounded-md w-[150px] justify-items-end float-right"
          >
            <SendOutlined className="text-[18px] pr-1" />
            Send
          </Button>
        </div>
      </div>
    </MainLayout>
  );
}
export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    // let org;
    // let orgId;
    const chatId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      //   org = cookies.orgID;
    }
    // eslint-disable-next-line max-len
    const request: any = await API.CHATS.fetchSingleChat(chatId, token);
    const chatDetails: any = await API.CHATS.fetchChatDetails(chatId, token);
    return {
      props: {
        chats: request || [],
        chatDetails,
      },
    };
  } catch (error) {
    return {
      props: {
        chats: [],
        chatDetails: [],
      },
    };
  }
}

export default ChatScreen;
