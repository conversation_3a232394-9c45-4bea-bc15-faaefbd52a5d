/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import { Avatar, AvatarGroup, Button } from '@mui/material';
import { ChatTypes } from 'types/chats-types';
import SafeDate from 'components/date-time/SafeDate';
import MainLayout from 'layouts/main-layout';
import InnerPageHeader from 'components/inner-page-header';

interface ChatsProps {
  chats: ChatTypes[];
}

function Chats({ chats = [] }: ChatsProps) {
  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <div className="mt-4">
        <InnerPageHeader
          title="Chats"
          add={false}
          backRoute="/chats"
          btnName="New Chat"
          titleLink={false}
          rightSide={(
            <Button
              onClick={() => {
                window.location.href = '/chats/new-chat';
              }}
              variant="contained"
              color="success"
              component="label"
              className="rounded-md m-0 p-0"
            >
              + New Chat
            </Button>
          )}
        />
      </div>
      <div className="grid grid-flow-row gap-3 placeholder-teal-100 mt-4 pr-10 pl-5">
        {chats.length > 0 ? (
          chats
            .sort((a: any, b: any) => a.createdAt - b.createdAt)
            .map((chat, index) => (
              // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
              <div
                // eslint-disable-next-line react/no-array-index-key
                key={`${chat.id}-${index}`}
                onClick={() => {
                  sessionStorage.setItem(
                    'participants',
                    JSON.stringify(chat.participants),
                  );
                  window.location.href = `/chats/chat-screen/${chat.conversationSid}?chatId=${chat.id}`;
                }}
                className="flex justify-between items-center gap-2 borderLine innerSpace cursor-pointer"
              >
                <div className="flex items-center gap-2 min-w-0">
                  <AvatarGroup
                    max={2}
                    spacing="small"
                    sx={{
                      '& .MuiAvatar-root + .MuiAvatar-root': {
                        marginLeft: '-8px',
                        marginTop: '-10px',
                      },
                      '& .MuiAvatarGroup-avatar': {
                        ':nth-of-type(2)': {
                          marginTop: '-10px',
                        },
                        ':nth-of-type(1)': {
                          marginTop: '10px',
                          backgroundColor: '#646F79',
                          width: '22px',
                          height: '22px',
                          fontSize: 13,
                          fontWeight: 500,
                        },
                        ':last-child': {
                          marginTop: '-10px',
                        },
                      },
                    }}
                  >
                    {chat.participants.map((participant) => (
                      <Avatar
                        className="w-6 h-6"
                        alt={participant.firstName}
                        key={participant.id}
                        src={participant.profile || 'null'}
                      />
                    ))}
                  </AvatarGroup>
                  <div className="flex flex-col min-w-0 max-w-[calc(100vw-370px)]">
                    <p className="p-0 m-0 font-[500] text-sm truncate">
                      {chat.title || chat.conversationFriendlyName}
                    </p>
                    <p className="p-0 m-0 font-[300] text-sm truncate">
                      {chat.latestMessage}
                    </p>
                  </div>
                </div>
                <p key={chat.id} className="p-0 m-0 font-[300] text-xs">
                  <SafeDate date={chat.updatedAt} />
                </p>
              </div>
            ))
        ) : (
          <p className="text-gray-500 italic text-sm ml-4">
            No chats found. Click 'New Chat' above to create a new chat.
          </p>
        )}
      </div>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let userId;
    // let orgId;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token v
      userId = cookies.userID as string;
    }
    // eslint-disable-next-line max-len
    const request: any = await API.CHATS.fetchChats(userId as string, token);
    return {
      props: {
        chats: request.items || [],
      },
    };
  } catch (error) {
    return {
      props: {
        chats: [],
      },
    };
  }
}

export default Chats;
