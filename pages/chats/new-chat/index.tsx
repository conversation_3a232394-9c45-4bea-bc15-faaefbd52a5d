/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import CustomLayout from 'layouts/custom-layout';
import { capitalizeWords } from 'utils/helper';
import {
  Avatar, Button, Checkbox, InputAdornment, TextField,
} from '@mui/material';
import useAuth from 'hooks/use-auth';
import { Check } from '@mui/icons-material';
import { useState } from 'react';
import { useRouter } from 'next/router';
import SelectBox from 'components/select-box/select-box';
import { Teams } from 'types/teams-types';
import { Navigator } from 'types/navigators-types';
import { useModal } from 'contexts/modal-context/modal-context';

interface MembersDetailProps {
    teams: Teams[];
}
function NewChat({ teams }: MembersDetailProps) {
  const { user } = useAuth();
  const [selectedNavigators, setSelectedNavigators] = useState<Navigator[]>([]);
  const [teamKey, setTeamKey] = useState('');
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const { showModal } = useModal();
  const router = useRouter();
  const isTeamLead = (navigatorId: string) => teams?.filter((team) => team.leads.filter((lead) => lead.id === navigatorId).length > 0).length > 0;

  const addNavigator = (navigator:Navigator) => {
    if (selectedNavigators.filter((x) => x.id === navigator.id).length > 0) {
      setSelectedNavigators(selectedNavigators.filter((x) => x.id !== navigator.id));
    } else {
      setSelectedNavigators([...selectedNavigators, navigator]);
    }
  };
  const handleTeamSelect = (value:string) => {
    setTeamKey(value);
    setSelectedNavigators([]);
  };

  const isChecked = (navigator: Navigator) => selectedNavigators.includes(navigator);

  const createChat = async () => {
    if (!title || !message || selectedNavigators.length === 0) {
      showModal('Error', 'Please fill all fields');
      return;
    }
    const model = {
      creatorID: user?.id,
      orgID: user?.org?.id,
      FriendlyName: title,
      title,
      participants: selectedNavigators.map((x) => x.id),
      message: {
        author: user?.id,
        body: message,
        attributes: {
          auth: title,
          profile: user?.profile || '',
          name: `${user?.firstName} ${user?.lastName}`,
          uuid: user?.id,
        },
      },
    };
    const response:any = await API.CHATS.createChat(model);

    // eslint-disable-next-line no-unused-expressions
    response?.conversationSid && router.replace(`/chats/chat-screen/${response?.conversationSid}?chatId=${response?.id}`);
  };

  return (
    <CustomLayout
      leftButtonOnClick={() => {
        window.history.back();
      }}
      subTitle={capitalizeWords('', true)}
      title="New Chat"
      button={false}
    >
      <div className="w-full innerSpace  pl-20 pr-20">
        <div>
          <div className="grid grid-flow-col w-1/3">
            <SelectBox
              onChange={(e) => {
                handleTeamSelect(e.target.value as string);
              }}
              defaultValue={teamKey}
              items={teams?.map((team) => ({ key: team.id, title: team.name }))}
              keyVal="select-team"
              label="Select Team"
            />
          </div>

          {
              teams.length > 0 && teams?.filter((x) => x.id?.toLowerCase() === teamKey)?.map((team) => (
                <>
                  <p className="font-[500]">
                    Select Teams Members
                    {' '}
                    <span className="font-[300]">
                      (
                      {team.name}
                      )
                    </span>
                  </p>
                  {team.navigators.map((navigator) => (
                    <div
                      key={navigator.id}
                      className="grid grid-flow-col items-center gap-3 pb-3 auto-cols-max"
                    >
                      {navigator.id !== user?.id && (
                      <Checkbox
                        checked={!!isChecked(navigator as any)}
                        onClick={() => { addNavigator(navigator as any); }}
                      />
                      )}
                      <Avatar src={navigator.profile} />
                      <div className="grid grid-flow-row items-center">
                        <p className="m-0 p-0 font-[500]">{capitalizeWords(`${navigator.firstName} ${navigator.lastName} ${navigator.id === user?.id ? '(you)' : ''}`, true)}</p>
                        <p className="m-0 p-0 font-[300] text-sm">
                          {' '}
                          {isTeamLead(navigator.id) ? 'Lead' : 'Support'}
                          {' '}
                        </p>
                      </div>
                    </div>
                  ))}
                </>
              ))
        }
        </div>
        <div className="w-full">
          <p className="font-[500]">Chat Details</p>
          <div className="grid grid-flow-row items-center gap-2 auto-cols-auto">
            <TextField
              label="Title"
              onChange={(e) => setTitle(e.target.value)}
              className="w-1/3"
              variant="filled"
            />
            <TextField
              multiline
              placeholder="Type your message here..."
              onChange={(e) => setMessage(e.target.value)}
              className="w-full"
              rows={4}
              variant="filled"
              InputProps={{
                sx: {
                  '& .MuiInputBase-input': {
                    paddingLeft: '45px',
                  },
                },
                startAdornment: (
                  <InputAdornment position="start" className="absolute top-5">
                    <Avatar className="w-8 h-8" alt="User Avatar" src={user?.profile} />
                  </InputAdornment>
                ),
              }}
            />
            <Button
              variant="contained"
              color="success"
              onClick={createChat}
              className="w-[140px] rounded-md grid grid-flow-col items-center justify-items-end float-right"
            >
              <Check className="text-[18px] pr-1" />
              Send
            </Button>
          </div>
        </div>
      </div>
    </CustomLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token:any;
    let orgId:any;
    let userId;
    // let orgId;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      orgId = cookies.orgID;
      userId = cookies.userID as string;
    }

    const request:any = await API.USER.getUser(userId as string, token, orgId);
    const teams:any = [];
    // eslint-disable-next-line no-restricted-syntax
    for await (const team of request.teams) {
      const tMember = await API.TEAMS.fetchSingleTeam(team.id, token as any, orgId as any);
      teams.push({ ...tMember });
    }
    return {
      props: {
        teams: teams || [],
      },
    };
  } catch (error) {
    return {
      props: {
        member: [],
        teams: [],
      },
    };
  }
}
export default NewChat;
