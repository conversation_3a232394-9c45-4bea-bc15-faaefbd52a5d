/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MainLayout from 'layouts/main-layout';
import ChatInnerPageHeader from 'components/inner-page-header/chat';
import { Button } from '@mui/material';
import useAuth from 'hooks/use-auth';
import { ChatTypes, Participant } from 'types/chats-types';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import EditChatTitle from 'components/chats/edit-chat-title';
import CustomTable from 'components/table-components/table';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';

interface ChatProps {
  chat: ChatTypes;
}
function ChatDetails({ chat }: ChatProps) {
  const { user } = useAuth();
  const { refresh } = useRefreshSSR();
  const { query } = useRouter();
  const [participants, setParticipants] = useState<
    Array<{ firstName: string }>
  >([]);
  const parseUserMetadata = (message: any) => {
    const stringifiedUser = JSON.parse(message.attributes);
    return stringifiedUser as any;
  };
  const [openEditChatTitle, setOpenEditChatTitle] = useState<boolean>(false);

  const handleEditChatTitle = () => {
    setOpenEditChatTitle((prev) => !prev);
  };

  useEffect(() => {
    setParticipants(JSON.parse(sessionStorage.getItem('participants') || '[]'));
  }, []);
  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <div className="mt-4 pr-5">
        <ChatInnerPageHeader
          title="Chats"
          add
          addLabel="Chat Details"
          updateUrl={`/chats/chat-screen/${query?.id}`}
          titleLink
          btnName="New Chat"
          chatTitle={
            chat.conversationFriendlyName || participants.map((p) => p.firstName).join(', ')
          }
          rightSide={(
            <div>
              <Button
                onClick={() => {
                  handleEditChatTitle();
                }}
                component="label"
                className="rounded-md m-0 text-[15px] text-[#E97100] grey-btn"
                sx={{
                  padding: '11px 15px !important',
                }}
              >
                <EditOutlinedIcon
                  style={{ width: '16px', height: '16px' }}
                  className="mr-2"
                />
                Edit Chat Title
              </Button>
            </div>
          )}
        />
      </div>
      <div className="pl-8 pr-10">
        <p className="m-0 mt-6 p-0 font-[600] text-[18px]">
          Chat Details
        </p>
        <div className="mt-5">
          <p className="m-0 p-0 font-[300] text-[13px] text-[#747A7A]">
            Chat Title
          </p>
          <p className="m-0 p-0 font-[500] text-[15px]">{chat.conversationFriendlyName || '-'}</p>
        </div>
        <div className="mt-[48px]">
          <p className="m-0 mt-5 p-0 font-[600] text-[18px] mb-4">
            Chat Members
          </p>
          <CustomTable
            source={participants as Participant[]}
            // eslint-disable-next-line @typescript-eslint/no-empty-function
            variant="chat-participant-table"
            style={{
              border: 'solid 1px lightgray',
              borderRadius: '10px',
              borderBottom: 'none',
            }}
            headCellStyle={{
              padding: '4px 16px',
              fontSize: '13px',
              color: '#747A7A',
            }}
            tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
          />
        </div>
      </div>
      <EditChatTitle
        open={openEditChatTitle}
        setOpen={setOpenEditChatTitle}
        chatTitle={chat.title}
        chatId={chat.id as string}
        refresh={refresh}
      />
    </MainLayout>
  );
}
export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    // let org;
    // let orgId;
    const chatId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      //   org = cookies.orgID;
    }
    // eslint-disable-next-line max-len
    const request: any = await API.CHATS.fetchChatDetails(chatId, token);
    return {
      props: {
        chat: request || [],
      },
    };
  } catch (error) {
    return {
      props: {
        chat: [],
      },
    };
  }
}

export default ChatDetails;
