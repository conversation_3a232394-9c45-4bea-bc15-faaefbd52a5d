/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import API from 'api/src/lib/api';
import HouseholdAddUpdate from 'components/households/add-update';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';

import { GetServerSidePropsContext } from 'next';
import { HouseHold } from 'types/household-types';

interface Props {
    household: HouseHold;
    add: boolean;
    }
function HouseholdAdd({ household, add }:Props) {
  return (
    <MainLayout buttonProps={{
      addButton: false,
    }}
    >
      <HouseholdAddUpdate household={household} add={add} />
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    // let orgId;
    const householdId:string = context.query.id as string;
    if (householdId === 'add') {
      return {
        props: {
          household: {},
          add: true,
        },
      };
    }
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
    }

    const request = await API.HOUSEHOLDS.fetchSingleHousehold(householdId, token);
    const household = request;

    return {
      props: {
        household: household || {},
        add: false,
      },
    };
  } catch (error) {
    return {
      props: {
        household: {},
        add: true,
      },
    };
  }
}

export default HouseholdAdd;
