/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import API from 'api/src/lib/api';
import Search from 'components/search/search';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { useEffect, useState } from 'react';
import { parse } from 'cookie';
import { Button } from '@mui/material';
import { Add } from '@mui/icons-material';
import { useRouter } from 'next/router';
import CustomTable from 'components/table-components/table';
import { HouseHold } from 'types/household-types';

interface PageInfo {
  per: number;
  total: number;
  page: number;
}

interface HouseholdPageProps {
  households: HouseHold[];
  pageInfo: PageInfo;
}

function HouseholdPage({ households, pageInfo }: HouseholdPageProps) {
  const [householdData, setHouseholdData] = useState<HouseHold[]>([]);
  const router = useRouter();
  const {
    sortBy = 'createdAt',
    sortDirection = 'desc',
    search = '',
  } = router.query;

  useEffect(() => {
    setHouseholdData(households);
  }, [households]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value;

    router.push({
      pathname: '/households',
      query: {
        ...router.query,
        page: 1, // reset to first page on search
        search: searchTerm || undefined,
      },
    });
  };

  return (
    <MainLayout buttonProps={{ addButton: false }}>
      <div style={{ gridTemplateColumns: 'min-content 1fr' }}>
        <div className="pl-3 pb-0 pt-[15px] max-h-screen grid grid-flow-col auto-cols-auto items-center overflow-auto pr-5">
          <p className="font-[600] text-[21px] p-0 m-0">
            Households
            {' '}
            <span className="font-[300]">
              (
              {pageInfo.total}
              )
            </span>
          </p>
          <div className="justify-self-end grid grid-flow-col auto-cols-max gap-2">
            <Search
              placeholder="Search Households"
              onChange={handleSearch}
            />
            <Button
              style={{ fontWeight: 500 }}
              variant="contained"
              className="rounded-md font-[500] text-sm"
              onClick={() => router.push('/households/add-edit/add')}
            >
              <Add style={{ color: 'white', fontSize: 21 }} />
              {' '}
              <span className="font-[500]">Add Household</span>
            </Button>
          </div>
        </div>

        <CustomTable
          source={householdData}
          variant="main-household"
          className="pr-1"
          pageInfo={pageInfo}
          sortBy={sortBy as string}
          sortDirection={sortDirection as 'asc' | 'desc'}
          onPageChange={(newPage) => {
            router.push({
              pathname: '/households',
              query: {
                ...router.query,
                page: newPage,
              },
            });
          }}
          onSortChange={(newSortBy, newDirection) => {
            router.push({
              pathname: '/households',
              query: {
                ...router.query,
                page: 1,
                sortBy: newSortBy,
                sortDirection: newDirection,
              },
            });
          }}
        />
      </div>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req, query } = context;
  const page = parseInt((query.page as string) || '1', 10);
  const sortBy = (query.sortBy as string) || 'createdAt';
  const sortDirection = (query.sortDirection as string) || 'desc';
  const search = (query.search as string) || '';
  const pageSize = 15;

  let token;
  let orgId;
  let teamId;

  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token;
    teamId = cookies.teamID === 'all' ? null : cookies.teamID;
  }

  const session: any = await API.AUTH.session(token);

  if (token && orgId) {
    const households: any = session?.roles?.includes('admin')
      ? await API.HOUSEHOLDS.fetchHouseholds(
        token,
        orgId,
        search,
        pageSize,
        page,
        sortBy,
        sortDirection,
      )
      : teamId
        ? await API.TEAMS.fetchTeamHouseholds(
          {
            teamId,
          },
          token,
          orgId,
          search,
          pageSize,
          page,
          sortBy,
          sortDirection,
        )
        : await API.HOUSEHOLDS.fetchHouseholds(
          token,
          orgId,
          search,
          pageSize,
          page,
          sortBy,
          sortDirection,
        );

    return {
      props: {
        households: households?.items || [],
        pageInfo: households?.metadata || { per: 0, total: 0, page: 0 },
      },
    };
  }

  return {
    props: {
      households: [],
      pageInfo: { per: 0, total: 0, page: 0 },
    },
  };
}

export default HouseholdPage;
