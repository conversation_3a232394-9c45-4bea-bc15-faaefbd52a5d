/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import API from 'api/src/lib/api';
import NetworkAddUpdate from 'components/networks/add-update';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';

import { GetServerSidePropsContext } from 'next';
import { Network } from 'types/networks-types';

interface Props {
  network: Network;
  add: boolean;
}
function NetworkAdd({ network, add }: Props) {
  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <NetworkAddUpdate add={add} network={network} />
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let orgId;
    const networkId: string = context.query.id as string;
    if (networkId === 'add') {
      return {
        props: {
          network: {},
          add: true,
        },
      };
    }
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      orgId = cookies.orgID;
    }

    const network: any = token && orgId
      ? await API.NETWORKS.fetchNetwork(token, networkId)
      : null;
    return {
      props: {
        network: network || {},
        add: false,
      },
    };
  } catch (error) {
    return {
      props: {
        network: {},
        add: true,
      },
    };
  }
}

export default NetworkAdd;
