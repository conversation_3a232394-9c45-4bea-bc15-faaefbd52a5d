import { Add } from '@mui/icons-material';
import { Button } from '@mui/material';
import API from 'api/src/lib/api';
import { Network } from 'api/src/types';
import Search from 'components/search/search';
import CustomTable from 'components/table-components/table';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';

interface PageInfo {
  per: number;
  total: number;
  page: number;
}

interface NetworksProps {
  networks: Network[];
  pageInfo: PageInfo;
}

function Networks({ networks, pageInfo }: NetworksProps) {
  const [networkData, setNetworkData] = useState<Network[]>([]);
  const router = useRouter();
  const { sortBy = 'createdAt', sortDirection = 'desc' } = router.query;

  useEffect(() => {
    setNetworkData(networks);
  }, [networks]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value;
    router.push({
      pathname: '/networks',
      query: {
        ...router.query,
        search: searchTerm || undefined,
        page: 1,
      },
    });
  };

  return (
    <MainLayout
      buttonProps={{
        addButton: false,
        addButtonOnClick: () => router.push('/networks/add-edit/add'),
        addButtonLabel: 'Add Network',
      }}
    >
      <div style={{ gridTemplateColumns: 'min-content 1fr' }}>
        <div className="pl-[15px] pb-0 pt-[15px] max-h-screen grid grid-flow-col auto-cols-auto items-center overflow-auto pr-5">
          <h1 className="font-[600] text-[21px] p-0 m-0">
            Networks
            <span className="font-[300]">
              {' '}
              (
              {pageInfo.total}
              )
            </span>
          </h1>
          <div className="justify-self-end grid grid-flow-col auto-cols-max gap-2 items-center">
            <Search placeholder="Search Network" onChange={handleSearch} />
            <Button
              style={{ fontWeight: 500 }}
              variant="contained"
              onClick={() => router.push('/networks/add-edit/add')}
              className="rounded-md font-[500] text-sm"
            >
              <Add style={{ color: 'white', fontSize: 21 }} />
              <span className="font-[500]">Add Network</span>
            </Button>
          </div>
        </div>

        <CustomTable
          source={networkData}
          className="pr-1"
          variant="main-network-table"
          headCellStyle={{
            paddingLeft: '16px',
            paddingBottom: '4px',
          }}
          pageInfo={pageInfo}
          sortBy={sortBy as string}
          sortDirection={sortDirection as 'asc' | 'desc'}
          onPageChange={(newPage) => {
            router.push({
              pathname: '/networks',
              query: {
                ...router.query,
                page: newPage,
                sortBy,
                sortDirection,
              },
            });
          }}
          onSortChange={(newSortBy, newDirection) => {
            router.push({
              pathname: '/networks',
              query: {
                ...router.query,
                page: 1,
                sortBy: newSortBy,
                sortDirection: newDirection,
              },
            });
          }}
        />
      </div>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req, query } = context;
  let token;
  let orgId;
  const page = parseInt((query.page as string) || '1', 10);
  const sortBy = (query.sortBy as string) || 'createdAt';
  const sortDirection = (query.sortDirection as string) || 'desc';
  const search = (query.search as string) || '';
  const pageSize = 15;

  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token;
  }

  const networks: any = token && orgId
    ? await API.NETWORKS.fetchNetworks(
      token,
      orgId,
      pageSize,
      page,
      sortBy,
      sortDirection,
      search,
    )
    : { items: [], metadata: { per: 0, total: 0, page: 0 } };

  return {
    props: {
      networks: networks.items || [],
      pageInfo: networks.metadata || { per: 0, total: 0, page: 0 },
    },
  };
}

export default Networks;
