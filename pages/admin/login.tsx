import { useEffect, useState } from 'react';
import { CircularProgress, Backdrop } from '@mui/material';

export default function AdminLoginPage() {
  const [html, setHtml] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  useEffect(() => {
    async function fetchHtml() {
      try {
        setIsLoading(true);
        // This will proxy to /api/proxy/admin/login
        const res = await fetch('/api/proxy/admin/login');
        if (!res.ok) {
          setError(`Failed to load admin login page: ${res.statusText}`);
          return;
        }
        const text = await res.text();
        setHtml(text);
      } catch (err) {
        setError('Error fetching admin login page');
      } finally {
        setIsLoading(false);
      }
    }

    fetchHtml();
  }, []);

  // Run scripts after HTML is actually rendered
  useEffect(() => {
    if (!html) return;

    const scriptEls = document.querySelectorAll('#admin-page-container script');
    scriptEls.forEach((oldScript: any) => {
      const newScript = document.createElement('script');
      if (oldScript.src) {
        newScript.src = oldScript.src;
      } else {
        newScript.textContent = oldScript.textContent;
      }
      document.body.appendChild(newScript);
      document.body.removeChild(newScript);
    });
  }, [html]);

  if (error) {
    return (
      <div>
        Error:
        {error}
      </div>
    );
  }

  if (!html) {
    return (
      <Backdrop open={isLoading} sx={{ color: '#fff', zIndex: 99999 }}>
        <CircularProgress color="inherit" />
      </Backdrop>
    );
  }

  return <div id="admin-page-container" dangerouslySetInnerHTML={{ __html: html }} />;
}
