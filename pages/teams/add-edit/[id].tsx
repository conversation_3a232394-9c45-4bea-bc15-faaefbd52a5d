/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import API from 'api/src/lib/api';
import TeamsAddUpdate from 'components/teams/add-update';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';

import { GetServerSidePropsContext } from 'next';
import { Teams } from 'types/teams-types';

interface Props {
    team: Teams;
    add: boolean;
    }
function TeamAdd({ team, add }:Props) {
  return (
    <MainLayout buttonProps={{
      addButton: false,
    }}
    >
      <TeamsAddUpdate add={add} team={team} />
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let orgId;
    const teamId:string = context.query.id as string;
    if (teamId === 'add') {
      return {
        props: {
          team: {},
          add: true,
        },
      };
    }
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      orgId = cookies.orgID;
    }

    const request = await API.TEAMS.fetchSingleTeam(teamId, token, orgId);
    const team = request;
    return {
      props: {
        team: team || {},
        add: false,
      },
    };
  } catch (error) {
    return {
      props: {
        team: {},
        add: true,
      },
    };
  }
}

export default TeamAdd;
