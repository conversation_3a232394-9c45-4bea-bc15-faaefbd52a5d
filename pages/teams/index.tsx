import { Add } from '@mui/icons-material';
import { Button } from '@mui/material';
import API from 'api/src/lib/api';
import Search from 'components/search/search';
import CustomTable from 'components/table-components/table';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import React, { useEffect, useState } from 'react';
import { Teams } from 'types/teams-types';

interface TeamsPageProps {
  teams: Teams[]
  pageInfo: { per: number; total: number; page: number }
  search: string
}

function TeamsPage({ teams, pageInfo, search }: TeamsPageProps) {
  const [teamData, setTeamData] = useState(teams);
  const [searchTerm, setSearchTerm] = useState(search);
  const router = useRouter();
  const { sortBy = 'createdAt', sortDirection = 'desc' } = router.query;

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    setSearchTerm(value);
    router.push({
      pathname: '/teams',
      query: {
        ...router.query,
        page: 1,
        search: value || undefined,
      },
    });
  };

  useEffect(() => {
    setTeamData(teams);
  }, [teams]);

  return (
    <MainLayout buttonProps={{
      addButton: false,
      addButtonLabel: 'Create Team',
      addButtonOnClick: () => router.push('/teams/add-edit/add'),
    }}
    >
      <div style={{ gridTemplateColumns: 'min-content 1fr' }}>
        <div className="pl-3 pt-4 max-h-screen grid grid-flow-col auto-cols-auto items-center overflow-auto pr-5">
          <h1 className="font-[600] text-[21px] p-0 m-0">
            Teams
            {' '}
            <span className="font-[300]">
              (
              {pageInfo.total}
              )
            </span>
          </h1>
          <div className="justify-self-end grid grid-flow-col auto-cols-max gap-2">
            <Search placeholder="Search Team" onChange={handleSearch} />
            <Button
              style={{ fontWeight: 500 }}
              variant="contained"
              onClick={() => router.push('/teams/add-edit/add')}
              className="rounded-md font-[500] text-sm"
            >
              <Add style={{ color: 'white', fontSize: 21 }} />
              {' '}
              <span>Create Team</span>
            </Button>
          </div>
        </div>
        <CustomTable
          source={teamData}
          variant="main-team-table"
          className="pr-1"
          pageInfo={pageInfo}
          sortBy={sortBy as string}
          sortDirection={sortDirection as 'asc' | 'desc'}
          onPageChange={(newPage) => {
            router.push({
              pathname: '/teams',
              query: {
                ...router.query,
                page: newPage,
              },
            });
          }}
          onSortChange={(newSortBy, newDirection) => {
            router.push({
              pathname: '/teams',
              query: {
                ...router.query,
                page: 1,
                sortBy: newSortBy,
                sortDirection: newDirection,
              },
            });
          }}
        />
      </div>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req, query } = context;
  const page = parseInt((query.page as string) || '1', 10);
  const search = (query.search as string) || '';
  const sortBy = (query.sortBy as string) || 'createdAt';
  const sortDirection = (query.sortDirection as string) || 'desc';
  const pageSize = 15;

  let token;
  let orgId;
  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token;
  }

  const teams: any = token && orgId
    ? await API.TEAMS.fetchTeams(
      token,
      orgId,
      pageSize,
      page,
      sortBy,
      sortDirection,
      search,
    )
    : null;

  return {
    props: {
      teams: teams?.items || [],
      pageInfo: teams?.metadata || { per: 0, total: 0, page: 0 },
      search,
    },
  };
}

export default TeamsPage;
