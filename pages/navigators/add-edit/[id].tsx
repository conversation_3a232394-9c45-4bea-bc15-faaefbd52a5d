/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import API from 'api/src/lib/api';
import NavigatorAddUpdate from 'components/navigators/add-update';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';

import { GetServerSidePropsContext } from 'next';
import { Navigator } from 'types/navigators-types';
import { Teams } from 'types/teams-types';

interface Props {
  navigator: Navigator;
  add: boolean;
  teams: Teams[];
}
function NavigatorAdd({ navigator, add, teams }: Props) {
  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <NavigatorAddUpdate add={add} navigator={navigator} teams={teams} />
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let orgId;
    const navigatorId: string = context.query.id as string;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      orgId = cookies.orgID;
    }
    const request = await API.USER.getUser(navigatorId, token, orgId);
    const teams = token && orgId ? await API.TEAMS.fetchTeams(token, orgId) : null as any;
    const navigator = request;

    if (navigatorId === 'add') {
      return {
        props: {
          navigator: {},
          add: true,
          teams: teams?.items || [],
        },
      };
    }

    return {
      props: {
        navigator: navigator || {},
        add: false,
        teams: teams?.items || [],
      },
    };
  } catch (error) {
    return {
      props: {
        navigator: {},
        add: true,
        teams: [],
      },
    };
  }
}

export default NavigatorAdd;
