import API from 'api/src/lib/api';
import AddEditNavigatorTask from 'components/navigators/tasks/add-edit';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { TaskObject } from 'types/tasks-response-type';
import NavigatorInnerPageHeader from 'components/inner-page-header/navigator';
import { capitalizeWords } from 'utils/helper';

function UpdateTask({ task }: { task: TaskObject }) {
  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <div className="mt-3 pr-5">
        <AddEditNavigatorTask
          add={false}
          task={task}
          renderHeader={(handleSubmit) => (
            <NavigatorInnerPageHeader
              title="Navigators"
              backRoute="/navigators"
              btnName="Save"
              updateLabel={`Edit Task (${capitalizeWords(task.title, true)})`}
              navigatorName={sessionStorage.getItem('navigatorName') || ''}
              navigatorId={sessionStorage.getItem('navigatorId') || ''}
              callback={handleSubmit}
            />
          )}
        />
      </div>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req } = context;
  let token;
  let orgId;
  // eslint-disable-next-line max-len
  // eslint-disable-next-line prefer-destructuring, no-unused-vars, @typescript-eslint/no-unused-vars
  const { navigatorId, taskId } = context.query;
  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token; // get cookie from context.req.headers.cookie named token
  }
  try {
    const request: TaskObject | unknown = token && orgId
      ? await API.TASKS.fetchSingleTask(taskId as string, orgId, token)
      : null;

    return {
      props: {
        task: request || {},
      },
    };
  } catch (error) {
    return {
      props: {
        task: {},
      },
    };
  }
}

export default UpdateTask;
