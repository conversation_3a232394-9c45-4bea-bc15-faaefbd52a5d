/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import API from 'api/src/lib/api';
import InnerPageHeader from 'components/inner-page-header';
import NavigatorDetail from 'components/navigators/NavigatorDetail';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { Navigator } from 'types/navigators-types';
import { capitalizeWords } from 'utils/helper';

interface Props {
  navigator: Navigator;
  navigatorTasks: {
    tasks: any;
  };
}

function NavigatorView({ navigator, navigatorTasks }: Props) {
  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <InnerPageHeader
        title="Navigators"
        add
        addLabel={`${capitalizeWords(navigator.firstName)} ${capitalizeWords(navigator.lastName)}`}
        backRoute="/navigators"
        btnName="Edit Task"
        updateLabel="Update"
        callback={() => Function.prototype}
        className="pl-4 pt-[22px] mb-10"
        rightSide={(<div />)}
      />
      <NavigatorDetail
        navigator={navigator as Navigator}
        tasks={navigatorTasks.tasks}
      />
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let orgId;
    const navigatorId: string = context.query.id as string;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      orgId = cookies.orgID;
    }

    // Fetch navigator
    const navigator = await API.USER.getUser(navigatorId, token, orgId);

    // Fetch member tasks
    const navigatorTasks = await API.USER.fetchNavigatorTasks(
      navigatorId,
      orgId,
      token,
    );

    return {
      props: {
        navigator: navigator || {},
        navigatorTasks,
      },
    };
  } catch (error) {
    return {
      props: {
        navigator: {},
        navigatorTasks: [],
      },
    };
  }
}

export default NavigatorView;
