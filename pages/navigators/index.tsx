import { Add } from '@mui/icons-material';
import { Button } from '@mui/material';
import API from 'api/src/lib/api';
import Search from 'components/search/search';
import CustomTable from 'components/table-components/table';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import React from 'react';
import { Navigator } from 'types/navigators-types';

interface PageInfo {
  per: number;
  total: number;
  page: number;
}

function Navigators({
  navigators,
  pageInfo,
}: {
  navigators: Navigator[];
  pageInfo: PageInfo;
}) {
  const router = useRouter();

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;
    router.push({
      pathname: '/navigators',
      query: { ...router.query, page: 1, search: value },
    });
  };

  const handlePageChange = (newPage: number) => {
    router.push({
      pathname: '/navigators',
      query: { ...router.query, page: newPage },
    });
  };

  const handleSortChange = (sortBy: string, sortDirection: 'asc' | 'desc') => {
    router.push({
      pathname: '/navigators',
      query: {
        ...router.query,
        page: 1,
        sortBy,
        sortDirection,
      },
    });
  };

  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <div style={{ gridTemplateColumns: 'min-content 1fr' }}>
        <div className="pl-[15px] pb-0 pt-[15px] max-h-screen grid grid-flow-col auto-cols-auto items-center overflow-auto pr-5">
          <p className="font-[600] text-[21px] p-0 m-0">
            Navigators
            {' '}
            <span className="font-[300]">
              (
              {pageInfo.total}
              )
            </span>
          </p>
          <div className="justify-self-end grid grid-flow-col auto-cols-max gap-2">
            <Search
              placeholder="Search Navigator"
              onChange={handleSearch}
            />
            <Button
              style={{ fontWeight: 500 }}
              variant="contained"
              onClick={() => router.push('/navigators/add-edit/add')}
              className="rounded-md font-[500] text-sm"
            >
              <Add style={{ color: 'white', fontSize: 21 }} />
              <span className="font-[500]">Add Navigator</span>
            </Button>
          </div>
        </div>
        <CustomTable
          variant="main-navigator-table"
          className="pr-1"
          source={navigators}
          pageInfo={pageInfo}
          onPageChange={handlePageChange}
          onSortChange={handleSortChange}
          headCellStyle={{
            paddingLeft: '16px',
            paddingBottom: '4px',
          }}
        />
      </div>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req, query } = context;
  let token;
  let orgId;
  const page = parseInt((query.page as string) || '1', 10);
  const sortBy = (query.sortBy as string) || 'createdAt';
  const sortDirection = (query.sortDirection as string) || 'desc';
  const pageSize = 14;
  const search = (query.search as string) || '';

  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token;
  }

  const navigators: any = token && orgId
    ? await API.USER.getAllUsers(
      token,
      orgId,
      pageSize,
      page,
      sortBy,
      sortDirection,
      search,
    )
    : null;

  return {
    props: {
      navigators: navigators?.items || [],
      pageInfo: navigators?.metadata || { per: 0, total: 0, page: 0 },
    },
  };
}

export default Navigators;
