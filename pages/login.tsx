import { But<PERSON>, TextField } from '@mui/material';
import Image from 'next/image';
import CheckIcon from '@mui/icons-material/Check';
import { useFormik } from 'formik';
import { createValidationSchema } from 'utils/helper';
import useAuth from 'hooks/use-auth';
import { BASE_URL } from 'api/src/lib/api';

const initialValues = { username: '', password: '' };

function LoginPage() {
  const { login } = useAuth();

  const formik = useFormik({
    initialValues,
    validationSchema: createValidationSchema(initialValues),
    onSubmit: (values) => {
      /// remove white space from values
      login({
        password: values.password.trim(),
        username: values.username.trim(),
      });
    },
  });

  return (
    <div className="bg-[#F5F5F5]  absolute grid items-center justify-center m-0 p-0 w-full h-screen">
      <div className="h-[581px] w-[648px] rounded-3xl shadow-2xl grid grid-flow-row auto-rows-min gap-7 justify-self-center bg-white">
        <div className="grid justify-center pt-16">
          <Image
            width={250}
            height={125}
            className="object-contain"
            src="/images/wellup-lockup-white.png"
            alt="wellup-logo"
          />
        </div>
        <div className="w-full auto-rows-min grid gap-3">
          <p className="text-xl pl-8">Sign In</p>
          <TextField
            onChange={formik.handleChange}
            name="username"
            className="w-[90%] justify-self-center"
            id="outlined-basic"
            label="Username"
            variant="outlined"
          />
          <TextField
            onChange={formik.handleChange}
            name="password"
            className="w-[90%] justify-self-center"
            type="password"
            id="outlined-basic"
            label="Password"
            variant="outlined"
          />
          <Button
            onClick={formik.submitForm}
            variant="contained"
            sx={{
              width: '90%',
              justifySelf: 'center',
              borderRadius: '6px',
              backgroundColor: '#BEC6CD',
            }}
            className="!h-[56px]"
          >
            <div className="grid grid-flow-col items-center">
              <CheckIcon className="absolute left-5" fontSize="medium" />
              <p className="text-lg text-center">Sign in</p>
            </div>
          </Button>
          <a
            href={`${BASE_URL}/password-reset/forgot`}
            className="text-sm text-center text-[#de7512] mt-3 no-underline hover:underline"
          >
            Forgot Password?
          </a>
        </div>
      </div>
    </div>
  );
}

export default LoginPage;
