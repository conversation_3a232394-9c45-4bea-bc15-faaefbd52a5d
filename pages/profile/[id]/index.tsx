import { Button } from '@mui/material';
import API from 'api/src/lib/api';
import PasswordUpdate from 'components/password-update';
import { useLoading } from 'contexts/loading-context/loading-context';
import { useModal } from 'contexts/modal-context/modal-context';
import useAuth from 'hooks/use-auth';
import MainLayout from 'layouts/main-layout';
import { useState } from 'react';
import EditIcon from '@mui/icons-material/Edit';
import AssignmentIcon from '@mui/icons-material/Assignment';
import { useRouter } from 'next/router';
import ProfileInfoNavigator from 'components/navigators/commons/profile-info';
import { Logout, Update } from '@mui/icons-material';

function Profile() {
  const { user, logout } = useAuth();
  const [open, setOpen] = useState(false);
  const router = useRouter();
  const { showModal } = useModal();
  const { showLoading, hideLoading } = useLoading();
  const handlePasswordChange = () => {
    setOpen(true);
  };

  // eslint-disable-next-line consistent-return
  const onComplete = async (values: any) => {
    try {
      showLoading();
      const checkOldPassword = await API.AUTH.checkOldPassword({ username: user?.email || '', password: values.oldPassword });
      if (checkOldPassword) {
        if (values.newPassword !== values.confirmPassword) {
          showModal('Error', 'New Password and Confirm Password does not match');
          return false;
        }
        const reset = await API.AUTH.resetPassword({ email: user?.email || '', password: values.newPassword }, user?.id || '');
        if (reset) {
          showModal('Success', 'Password Updated Successfully');
          logout();
          router.reload();
        }
        return true;
      }
      return false;
    } catch (error) {
      showModal('Error', 'Incorrect old password');
    } finally {
      hideLoading();
    }
  };
  return (
    <MainLayout buttonProps={{
      addButton: false,
      addButtonLabel: 'Add New Profile',
    }}
    >
      <div className="grid grid-flow-col auto-cols-auto justify-between h-auto items-center">
        <p className="font-semibold text-xl">My Account</p>
        <div className="grid grid-flow-col auto-cols-max gap-3">
          <Button
            className="rounded-md font-[500] h-[40px] bg-left bg-no-repeat grid grid-flow-col gap-2 border-red-700 h-[40px] text-red-700 text-sm"
            type="button"
            variant="outlined"
            onClick={() => {
              router.push('https://form.asana.com/?k=s8yfob0JrjYpV-TJ7aqo5Q&d=****************', '_blank');
            }}
            title="Report Issue"
          >
            <AssignmentIcon className="text-sm" />
            Report Issue
          </Button>
          <Button
            onClick={handlePasswordChange}
            variant="contained"
            style={{ backgroundColor: '#F7F8F8' }}
            className="rounded-md font-[500] h-[40px]  bg-icon-lock bg-left bg-no-repeat bg-origin-content text-secondary text-sm"
          >
            <Update className="text-sm" />
            <p className="pl-2 m-0 p-0">Update Password</p>
          </Button>

          <Button
            className="rounded-md font-[500] h-[40px] bg-left bg-no-repeat bg-origin-content bg-[#F7F8FA] text-primary text-sm"
            type="button"
            onClick={() => {
              router.push('/profile/me/edit');
            }}
            title="Edit"
          >
            <EditIcon className="text-sm" />
            <p className="pl-2 m-0 p-0">Edit Profile</p>
          </Button>
          <Button
            onClick={() => {
              showModal('Warning', 'You are going to be logout', () => {
                logout();
              });
            }}
            variant="contained"
            style={{ backgroundColor: '#F7F8F8' }}
            className="rounded-md font-[500] h-[40px] pl-3 bg-icon-logout bg-left bg-no-repeat bg-origin-content text-[#E42B57] text-sm"
          >
            <Logout className="text-sm" />
            <p className="pl-2 m-0 p-0"> Sign Out</p>
          </Button>
        </div>
      </div>
      <PasswordUpdate open={open} setOpen={setOpen} onComplete={onComplete} />
      <ProfileInfoNavigator member={user as any} />
    </MainLayout>
  );
}
export default Profile;
