import { TextField } from '@mui/material';
import API from 'api/src/lib/api';
import InnerPageHeader from 'components/inner-page-header';
import ProfilePicturePicker from 'components/profile-picture-picker/profile-picture-picker';
import UserAvatarWithOverlay from 'components/user-avatar-with-overlay copy';
import { useFormik } from 'formik';
import useAuth from 'hooks/use-auth';
import MainLayout from 'layouts/main-layout';
import { useRouter } from 'next/router';
import { useEffect, useRef, useState } from 'react';
import { getLatestImage } from 'utils/helper';

function EditProfile() {
  const {user}  = useAuth();
const router = useRouter();
  const formik = useFormik({
    initialValues: {
      image: '',
      firstName: '',
      lastName: '',
    },
    onSubmit: async (values) => {
        if(pickerOn) {
            if(picture?.startsWith('data:image')) {
                await API.USER.createUserAttachment(user?.id || '', {
                    type: 'userProfile',
                    kind: 'main',
                    name: `${user?.id}-profile`,
                    base64: picture,
                  });
            }
        }
        await API.USER.updateUser(user?.id || '', {
            firstName: values.firstName,
            lastName: values.lastName,
          } as any);
          await router.push('/profile/me');
          router.reload();
    },
  });

  useEffect(() => {
    formik.setValues({
      firstName: user?.firstName || '',
      lastName: user?.lastName || '',
    } as any);
  }, [user]);

  const [picture, setPicture] = useState<string | null>('');
  const [pickerOn, setPickerOn] = useState(false);
  const profilePickerRef = useRef<any>(null);

  useEffect(() => {
    if (user?.attachments) {
      setPicture(getLatestImage(user?.attachments, 'userProfile'));
    }
  }, [user]);

  const handleClick = (id:string, key:string) => {
    if (key === 'remove') {
      setPicture(null);
      setPickerOn(true);
    }
    if (key === 'new') {
      profilePickerRef.current?.clickInput();
    }
  };
  useEffect(() => {
    if (picture?.startsWith('data:image')) {
      setPickerOn(true);
    }
  }, [picture]);
  return (
    <MainLayout buttonProps={{
      addButton: false,
      addButtonLabel: 'Add New Profile',
    }}
    >
      <div className="pt-6 grid grid-cols-3 gap-3">
        <InnerPageHeader
          title="Profile"
          addLabel="Edit Profile"
          updateLabel="Update Profile"
          add={false}
          backRoute="/members"
          btnName="Save"
          callback={formik.submitForm}
        />
        <div className=" w-[192px]">
          <p className="font-semibold text-xl">My Account</p>
          <div style={{
        display: pickerOn ? 'block' : 'none',
      }}
      >
        <ProfilePicturePicker
          ref={profilePickerRef}
          picture={picture as string}
          setPicture={setPicture as any}
        />
      </div>
      <div style={{
        display: pickerOn ? 'none' : 'block',
      }}
      >
        <UserAvatarWithOverlay
          item={user as any || {}}
          handleClick={(id, key) => handleClick(id, key)}
        />
      </div>
        </div>
        <div className="col-span-3 grid grid-cols-3 gap-3 auto-cols-max">
          <TextField
            name="firstName"
            onChange={formik.handleChange}
            placeholder="Enter First Name"
            value={formik.values.firstName}
            id="filled-basic"
            label="First Name"
            variant="filled"
          />
          <TextField
            name="lastName"
            value={formik.values.lastName}
            onChange={formik.handleChange}
            placeholder="Enter Last Name"
            id="filled-basic"
            label="Last Name"
            variant="filled"
          />
        </div>

      </div>
    </MainLayout>
  );
}
export default EditProfile;
