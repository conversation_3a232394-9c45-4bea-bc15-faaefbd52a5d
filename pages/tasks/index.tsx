import Add from '@mui/icons-material/Add';
import { Button } from '@mui/material';
import API from 'api/src/lib/api';
import KanbanBoard from 'components/kanban-board';
import { parse } from 'cookie';
import useAuth from 'hooks/use-auth';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import {
  useEffect, useState, useCallback,
} from 'react';
import { TasksResponseProps } from 'types/tasks-response-type';
import Cookies from 'js-cookie';
import InfiniteScroll from 'react-infinite-scroll-component';

interface PageInfo {
  'pending': {
    page: number;
    perPage: number;
    total: number;
    totalPages: number;
  },
  'picked up': {
    page: number;
    perPage: number;
    total: number;
    totalPages: number;
  },
  completed: {
    page: number;
    perPage: number;
    total: number;
    totalPages: number;
  },
}
interface TaskProps {
  tasks: TasksResponseProps;
  pageInfo: PageInfo;
}
function Tasks({ tasks, pageInfo }: TaskProps) {
  const router = useRouter();
  const { user } = useAuth();
  const [assessmentTypes, setAssessmentTypes] = useState([]);

  const token = Cookies.get('token');
  const [allTasks, setAllTasks] = useState(tasks);
  const [currentPage, setCurrentPage] = useState(
    pageInfo?.['picked up']?.page || 1,
  );
  const [totalPages, setTotalPages] = useState(
    pageInfo?.['picked up']?.totalPages || 1,
  );
  const [isLoading, setIsLoading] = useState(false);

  const handleItems = async () => {
    const request = await API.ASSESSMENT.fetchTypes(user?.org?.id || '');
    const data = request.map((item: any) => ({
      key: item.key,
      title: item.name,
    }));
    setAssessmentTypes(data);
  };

  useEffect(() => {
    if (user && assessmentTypes.length === 0) {
      handleItems();
    }
  }, [user]);

  const fetchMoreTasks = useCallback(async () => {
    if (isLoading || currentPage >= totalPages) return;
    setIsLoading(true);

    const nextPage = currentPage + 1;
    const res = await API.TASKS.fetchTasksPage(
      Cookies.get('orgID') || '',
      Cookies.get('userID') || '',
      token || '',
      15,
      nextPage,
    );
    const newTasks = res.data.tasks;
    const newMetadata = res.data.metadata;

    const mergedTasks: any = { ...allTasks };
    ['picked up', 'pending', 'completed'].forEach((status) => {
      mergedTasks[status] = [
        ...(mergedTasks[status] || []),
        ...(newTasks[status] || []),
      ];
    });

    setAllTasks(mergedTasks);
    setCurrentPage(newMetadata['picked up']?.page || nextPage);
    setTotalPages(newMetadata['picked up']?.totalPages || totalPages);
    setIsLoading(false);
  }, [currentPage, totalPages, isLoading, allTasks, token]);

  return (
    <MainLayout
      buttonProps={{
        addButton: false,
        addButtonOnClick: () => {
          router.push('/members/add-edit/add');
        },
        addButtonLabel: 'Add Member',
      }}
    >
      <div style={{ gridTemplateColumns: 'min-content 1fr' }}>
        <div className="pl-3 pr-5 py-[17px] grid grid-flow-col auto-cols-auto items-center overflow-auto">
          <p className="font-[500] text-[21px] m-0">Tasks</p>
          <div className="justify-self-end grid grid-flow-col auto-cols-max gap-2">
            <Button
              variant="contained"
              onClick={() => router.push('/tasks/add')}
              className="rounded-md m-0 p-0"
            >
              <Add style={{ color: 'white', fontSize: 21 }} />
              <span className="font-[500]">Create Task</span>
            </Button>
          </div>
        </div>

        <div
          id="scrollableDiv"
          className="pr-10 pl-5 pb-3 pt-3 max-h-[calc(100vh-166px)] overflow-auto"
        >
          <InfiniteScroll
            dataLength={
              (allTasks['picked up']?.length || 0)
              + (allTasks.pending?.length || 0)
              + (allTasks.completed?.length || 0)
            }
            next={fetchMoreTasks}
            hasMore={currentPage < totalPages}
            loader={<p className="text-center text-sm text-gray-500 py-2 flicker">Loading more tasks...</p>}
            scrollableTarget="scrollableDiv"
          >
            <KanbanBoard data={allTasks} assessments={assessmentTypes} />
          </InfiniteScroll>
        </div>
      </div>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req } = context;
  let token;
  let orgId;
  const page = 1;
  const pageSize = 15;

  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token;
  }

  const request = token && orgId
    ? await API.TASKS.fetchTasksPage(orgId, token, Cookies.get('userID') || '', pageSize, page)
    : null;

  return {
    props: {
      tasks: request?.data.tasks || [],
      pageInfo: request?.data.metadata || {
        picked_up: {
          perPage: 0, total: 0, page: 0, totalPages: 0,
        },
        pending: {
          perPage: 0, total: 0, page: 0, totalPages: 0,
        },
        completed: {
          perPage: 0, total: 0, page: 0, totalPages: 0,
        },
      },
    },
  };
}

export default Tasks;
