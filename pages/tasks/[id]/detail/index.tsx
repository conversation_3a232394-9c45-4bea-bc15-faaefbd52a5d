/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import {
  Avatar, Button, Dialog, DialogContent,
} from '@mui/material';
import API from 'api/src/lib/api';
import InnerPageHeader from 'components/inner-page-header';
import PanToolAltOutlinedIcon from '@mui/icons-material/PanToolAltOutlined';
import SignLanguageOutlinedIcon from '@mui/icons-material/SignLanguageOutlined';
import MarkTaskComplete from 'components/tasks/commons/mark-complete';
import { parse } from 'cookie';
import useAuth from 'hooks/use-auth';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { TaskObject } from 'types/tasks-response-type';
import {
  addressFormatter,
  capitalizeWords,
  getLatestImage,
} from 'utils/helper';
import { useModal } from 'contexts/modal-context/modal-context';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';
import DeleteOutlinedIcon from '@mui/icons-material/DeleteOutlined';
import CheckIcon from '@mui/icons-material/Check';
import SafeDate from 'components/date-time/SafeDate';

function TaskDetail({ task }: { task: TaskObject }) {
  const [open, setOpen] = useState(false);
  const [openAttachment, setOpenAttachment] = useState(false);
  const [currentImage, setCurrentImage] = useState('');
  const { user } = useAuth();
  const router = useRouter();
  const { showModal } = useModal();
  const handleMarkComplete = async (values: object) => {
    const model = {
      orgID: user?.org?.id,
      status: 'completed',
      reason: {
        ...values,
        creatorID: user?.id,
      },
    };
    await API.TASKS.markTaskAsCompleted(task.id, model);
    await router.replace('/tasks');
  };
  const handleOpen = async () => {
    if (task.status === 'completed') {
      await API.TASKS.updateStatus(task.id, 'archived');
      await router.replace('/tasks');
    } else {
      setOpen(true);
    }
  };
  function handleDelete() {
    showModal(
      'Delete Task',
      'Are you sure you want to delete this task?',
      async () => {
        await API.TASKS.deleteTask(task.id);
        await router.push('/tasks');
      },
    );
  }
  const handleClickOpen = (imageUrl: string) => {
    setCurrentImage(imageUrl);
    if (imageUrl.endsWith('.pdf')) {
      window.open(imageUrl, '_blank');
      return;
    }
    setOpenAttachment(true);
  };
  const handleClose = () => {
    setOpen(false);
    setOpenAttachment(false);
  };
  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <div className="mt-4">
        <InnerPageHeader
          title="Task"
          add
          addLabel={`${capitalizeWords(task.title, true)}`}
          backRoute="/tasks"
          btnName="Edit Task"
          updateLabel="Update"
          rightSide={(
            <div className="flex items-center gap-3">
              <div
                onClick={handleDelete}
                className="p-3 px-4 cursor-pointer rounded-md grey-btn"
              >
                <span className="m-0 text-[#E42B57] font-semibold text-sm flex items-center">
                  <DeleteOutlinedIcon
                    style={{ width: '16px', height: '16px' }}
                    className="mr-2"
                  />
                  Delete Task
                </span>
              </div>
              {task.status !== 'completed' && (
                <div
                  onClick={() => {
                    router.push(`/tasks/${task.id}/edit`);
                  }}
                  className="p-3 px-4 cursor-pointer rounded-md grey-btn"
                >
                  <span className="m-0 text-primary font-semibold text-sm flex items-center">
                    <EditOutlinedIcon
                      style={{ width: '16px', height: '16px' }}
                      className="mr-2"
                    />
                    Edit Task
                  </span>
                </div>
              )}
              {task.assignee?.id === user?.id && (
                <div
                  onClick={async () => {
                    await API.TASKS.dropTask(task.id, {
                      status: 'pending',
                    });
                    await router.replace('/tasks');
                  }}
                  className="p-3 px-4 cursor-pointer rounded-md grey-btn"
                >
                  <span className="m-0 text-[#E42B57] font-semibold text-sm flex items-center">
                    <SignLanguageOutlinedIcon
                      style={{ width: '16px', height: '16px' }}
                      className="mr-2"
                    />
                    Drop Task
                  </span>
                </div>
              )}
              {task.completedBy?.some((nav: any) => nav.id === user?.id)
                && task.assignee?.id !== user?.id && (
                  <Button
                    variant="contained"
                    onClick={async () => {
                      await API.TASKS.pickupTask(task.id, {
                        status: 'picked up',
                        assigneeID: user?.id,
                      });
                      await router.replace('/tasks');
                    }}
                    className="rounded-md m-0 p-0 px-3 !min-w-[50px]"
                  >
                    <PanToolAltOutlinedIcon
                      style={{ color: 'white', fontSize: 21 }}
                    />
                    <span className="ml-1 font-[500]">Pickup Task</span>
                  </Button>
              )}

              <div
                onClick={handleOpen}
                className="bg-[#008390] hover:opacity-90 min-w-[163px] p-3 cursor-pointer rounded-md grid grid-flow-row items-center justify-center"
              >
                <span className="m-0 text-white font-semibold text-sm flex items-center">
                  <CheckIcon
                    style={{ width: '16px', height: '16px' }}
                    className="mr-2"
                  />
                  {task.status !== 'completed'
                    ? 'Mark Complete'
                    : 'Archive Task'}
                </span>
              </div>
            </div>
          )}
          callback={() => null}
        />
      </div>
      <div className="ml-8">
        <p className="font-semibold text-lg">Member Details</p>
        <div className="grid grid-flow-col auto-cols-max items-center">
          <div
            className="cursor-pointer flex gap-3 items-center pr-3"
            onClick={() => {
              router.push(`/members/profile/${task.receivers[0].id}`);
            }}
          >
            <Avatar
              className="w-8 h-8 cursor-pointer"
              src={
                task.receivers.length > 0
                  ? getLatestImage(task.receivers[0].attachments, 'profile')
                  : ''
              }
            />
            <p className="font-[500] text-[15px] text-dark-text">
              {task.receivers.length > 0
                ? `${capitalizeWords(
                  `${task.receivers[0].firstName} ${task.receivers[0].lastName}`,
                  true,
                )}`
                : ''}
            </p>
          </div>
        </div>
        <p className="font-semibold text-lg mt-10">Task Details</p>
        <div className="grid grid-cols-3 auto-cols-max gap-7 items-center">
          <div className="grid grid-flow-row auto-cols-max">
            <p className="m-0 font-normal text-xs text-grey">Task Type</p>
            <p className="m-0 font-semibold text-sm text-dark-text">
              {capitalizeWords(task.type, true)}
            </p>
          </div>
          <div className="grid grid-flow-row auto-cols-max">
            <p className="m-0 font-normal text-xs text-grey">Type</p>
            <p className="m-0 font-semibold text-sm text-dark-text">
              {capitalizeWords(task.taskDetail?.type.replace(/_/g, ' '), true)}
            </p>
          </div>
          <div className="grid grid-flow-row auto-cols-max">
            <p className="m-0 font-normal text-xs text-grey">Location Type</p>
            <p className="m-0 font-semibold text-sm text-dark-text">
              {task.remote ? 'Remote' : 'Location Based'}
            </p>
            <p className="m-0 font-normal text-sm text-dark-text">
              {task.remote
                ? task.taskDetail?.meta?.data?.remote
                : addressFormatter(task.location)}
            </p>
          </div>
          <div className="grid grid-flow-row auto-cols-max col-span-3">
            <p className="m-0 font-normal text-xs text-grey">Date Type</p>
            <p className="m-0 font-semibold text-sm text-dark-text">
              {capitalizeWords(
                task.taskDetail.meta?.data?.dateType?.replace(/_/g, ' '),
                true,
              )}
              {task.taskDetail.meta?.data?.dateType && '-'}
              <span className="font-normal">
                <SafeDate
                  date={new Date(task.dueAtEpoc * 1000).toISOString()}
                  time={task.taskDetail.meta?.data?.dateType === 'datetime'}
                />
              </span>
            </p>
          </div>
          <div className="grid grid-flow-row auto-cols-auto col-span-3">
            <p className="m-0 font-normal text-xs text-grey">Description</p>
            <p className="m-0 font-normal text-sm text-dark-text">
              {task.desc}
            </p>
          </div>
        </div>
        {task.attachments && task.attachments.length > 0 && (
          <p className="font-semibold text-lg mt-12">Attachments</p>
        )}
        <div className="grid grid-flow-col gap-5 auto-cols-max">
          {task.attachments
            && task.attachments.map((attachment) => (
              // eslint-disable-next-line max-len
              // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
              <div
                onClick={() => {
                  handleClickOpen(attachment?.url);
                }}
                style={{
                  border: '1px solid #e0e0e0',
                }}
                className="cursor-pointer grid grid-flow-row auto-cols-max w-[200px] items-start h-[200px] rounded-md overflow-hidden"
              >
                <Avatar
                  src={attachment?.url}
                  alt="attachment"
                  className="w-[200px] h-[150px] rounded-none object-fill"
                />
                <p className="text-xs pl-1 font-semibold m-0">
                  <SafeDate date={attachment?.createdAt} />
                </p>
                <p className="text-xs pl-1 font-normal m-0">
                  {attachment?.name}
                </p>
              </div>
            ))}
        </div>
        {/* <AttachmentPicker
          multiple={false}
          attachments={task.attachments || []}
          attachmentsToRemove={[]}
          setPicture={() => {}}
          setAttachmentsToRemove={() => {}}
        /> */}
        <p className="font-semibold text-lg mt-12">Completed By</p>
        {task.completedBy.map((completed) => (
          <div className="grid grid-flow-col auto-cols-max gap-3 items-center">
            <div
              className="flex gap-3 items-center pr-3 cursor-pointer"
              onClick={() => {
                router.push(`/navigators/${completed.id}`);
              }}
            >
              <Avatar src={completed.profile} />
              <p className="font-semibold text-sm text-dark-text">
                {`${capitalizeWords(
                  `${completed.firstName} ${completed.lastName}`,
                  true,
                )}`}
              </p>
            </div>
          </div>
        ))}
      </div>
      <MarkTaskComplete
        task={task}
        open={open}
        setOpen={setOpen}
        onComplete={handleMarkComplete}
      />
      <Dialog
        open={openAttachment}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
      >
        <DialogContent>
          <Avatar
            className="rounded-md  w-[100%] h-auto object-contain"
            src={currentImage}
          />
        </DialogContent>
      </Dialog>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req } = context;
  let token;
  let orgId;
  // eslint-disable-next-line prefer-destructuring
  const id = context.query.id;
  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token; // get cookie from context.req.headers.cookie named token
  }
  try {
    const request: TaskObject | unknown = token && orgId
      ? await API.TASKS.fetchSingleTask(id as string, orgId, token)
      : null;

    return {
      props: {
        task: request || {},
      },
    };
  } catch (error) {
    return {
      props: {
        task: {},
      },
    };
  }
}
export default TaskDetail;
