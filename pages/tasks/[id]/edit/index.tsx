import API from 'api/src/lib/api';
import AddEditTask from 'components/tasks/add-edit';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { TaskObject } from 'types/tasks-response-type';

function UpdateTask({ task }: {task: TaskObject}) {
  // const router = useRouter();

  return (
    <MainLayout buttonProps={{
      addButton: false,
    }}
    >
      <AddEditTask add={false} task={task} />
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req } = context;
  let token;
  let orgId;
  // eslint-disable-next-line prefer-destructuring
  const id = context.query.id;
  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token; // get cookie from context.req.headers.cookie named token
  }
  try {
    const request:TaskObject | unknown = token && orgId
      ? await API.TASKS.fetchSingleTask(id as string, orgId, token) : null;

    return {
      props: {
        task: request || {},
      },
    };
  } catch (error) {
    return {
      props: {
        task: {},
      },
    };
  }
}

export default UpdateTask;
