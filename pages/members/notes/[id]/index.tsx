/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import {
  Avatar, Button, Menu, MenuItem,
} from '@mui/material';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { NotesTypes } from 'types/notes-types';
import { capitalizeWords } from 'utils/helper';
import React, { useContext, useState } from 'react';
import { useRouter } from 'next/router';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { MoreHoriz } from '@mui/icons-material';
import { useModal } from 'contexts/modal-context/modal-context';
import SafeDate from 'components/date-time/SafeDate';

interface NotesProps {
  member: any;
  notes: NotesTypes[];
}
function Notes({ member, notes = [] }: NotesProps) {
  const { refresh } = useRefreshSSR();
  const router = useRouter();
  const constants = useContext(ConstantsContext);
  const { showModal } = useModal();
  const handleNoteClick = (noteId: string, status: string) => {
    sessionStorage.setItem('member', JSON.stringify(member));
    if (status === 'draft') {
      router.push(`/members/notes/${member.id}/${noteId}/draft`);
    } else {
      router.push(`/members/notes/${member.id}/${noteId}/`);
    }
  };

  const tabs = [
    {
      label: 'Published',
      value: 'published',
    },
    {
      label: 'Drafts',
      value: 'drafts',
    },
  ];
  const [activeTab, setActiveTab] = useState('published');
  const published = notes.filter((note) => note.status === 'active');
  const drafts = notes.filter((note) => note.status === 'draft');
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);
  const [menuNote, setMenuNote] = useState<NotesTypes | undefined>(undefined);
  const open = Boolean(anchorEl);
  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };
  const handleClose = () => {
    setAnchorEl(null);
  };
  async function handleDelete() {
    const attachmentIds: string[] = [];
    if (menuNote?.id) {
      await API.NOTES.deleteNote(menuNote.id);
      if (menuNote?.attachments?.length > 0) {
        menuNote.attachments.forEach((attachment) => {
          attachmentIds.push(attachment.id);
        });
        await API.NOTES.deleteAttachments(attachmentIds);
      }
      handleClose();
      refresh();
    }
  }
  function handleClickDelete() {
    showModal(
      'Warning',
      'Are you sure you want to delete this draft? It will be permanently removed from the system.',
      async () => {
        await handleDelete();
      },
    );
  }

  return (
    <MemberLayout
      memberId={member.id}
      active="notes"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Notes"
    >
      <div className="w-full pt-1 pb-1 justify-between pl-10 grid grid-flow-col auto-cols-max items-center">
        <p className="m-0 p-0 font-[600] text-lg">
          Notes (
          {notes?.length}
          )
        </p>
        <Button
          onClick={() => router.push(`/members/notes/${member.id}/new`)}
          variant="contained"
          color="success"
          component="label"
          className="rounded-md m-0 p-0 h-[40px] float-right"
          // style={{ background: 'linear-gradient(to bottom, #FD8205, #E97100)' }}
        >
          + New Note
        </Button>
      </div>
      <div
        className="grid grid-flow-col gap-0 pl-10 pt-1 pb-1"
        style={{
          justifyContent: 'start', // Ensures items stay aligned to the start
          width: 'fit-content', // Ensures the grid only takes as much space as its content
        }}
      >
        {tabs.map((tab, index) => (
          <div
            key={tab.value}
            onClick={() => setActiveTab(tab.value)}
            style={{
              background:
                activeTab !== tab.value
                  ? 'linear-gradient(180deg, #F7F8F8 0%, #DCDFDF 100%)'
                  : 'linear-gradient(180deg, #404848 0%, #868B8B 100%)',
            }}
            className={`cursor-pointer w-[144px] h-[38px] grid content-center 
              ${index === 0 ? 'rounded-l-lg' : ''} 
              ${index === tabs.length - 1 ? 'rounded-r-lg' : ''}
              ${
                index !== 0 && index !== tabs.length - 1
                  ? 'border-l-0 border-r-0'
                  : ''
              }`}
          >
            <p
              className={`text-center font-[500] text-sm ${
                activeTab === tab.value ? 'text-white' : 'text-[#262D2D]'
              }`}
            >
              {`${tab.label} (${
                tab.label === 'Published' ? published.length : drafts.length
              })`}
            </p>
          </div>
        ))}
      </div>
      <div className="grid grid-flow-row gap-3 placeholder-teal-100 mt-3">
        {activeTab === 'published'
          && published.map((note) => (
            <div
              onClick={() => {
                handleNoteClick(note.id, 'published');
              }}
              key={note.id}
              className="grid grid-flow-col cursor-pointer innerSpace pl-10 auto-cols-max place-content-between w-full gap-2 borderLine items-center"
            >
              <div className="grid grid-flow-col gap-2 auto-cols-max items-center relative">
                <div className="grid grid-flow-row items-center auto-cols-max gap-3">
                  <p className="p-0 m-0 font-[500] text-sm">
                    {note.title}
                    <span className="text-primary first-letter:uppercase pl-1">
                      (
                      {capitalizeWords(
                        constants?.notesSubcategory.find(
                          (x) => x.key === note.type,
                        )?.title || 'General',
                        true,
                      )}
                      )
                    </span>
                  </p>
                  <p className="p-0 m-0 text-sm max-w-screen-lg overflow-hidden text-ellipsis clamp-3">
                    {note.msg}
                  </p>
                  <div className="grid grid-flow-col auto-cols-max gap-2 ">
                    {note.tags.map((tag) => (
                      <div
                        style={{
                          backgroundColor: `#${tag.color}`,
                        }}
                        className="grid grid-flow-row auto-cols-auto p-2 items-center rounded-md text-white"
                      >
                        <p className="p-0 m-0 text-sm">
                          {capitalizeWords(tag.name)}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="grid-flow-col grid items-center gap-2 self-start mt-[-6px]">
                <Avatar
                  src={note.creator.profile}
                  className="bg-primary h-8 w-8 p-0 m-0"
                />
                <p className="p-0 m-0 font-[500] text-sm ">
                  {`${capitalizeWords(
                    `${note.creator.firstName} ${note.creator.lastName}`,
                    true,
                  )}`}
                </p>
                {note.attachments && note.attachments.length > 0 && (
                  <div className="bg-icon-attachment bg-center bg-no-repeat w-8 h-8" />
                )}

                <p className="p-0 m-0 text-sm">
                  <SafeDate date={note.updatedAt} time />
                </p>
              </div>
            </div>
          ))}
        {activeTab === 'drafts'
          && drafts.map((note) => (
            <div
              onClick={() => {
                handleNoteClick(note.id, 'draft');
              }}
              key={note.id}
              className="grid grid-flow-col cursor-pointer innerSpace pl-10 auto-cols-max place-content-between w-full gap-2 borderLine items-center"
            >
              <div className="grid grid-flow-col gap-2 auto-cols-max items-center relative">
                <div className="grid grid-flow-row items-center auto-cols-max gap-3">
                  <p className="p-0 m-0 font-[500] text-sm">
                    {note.title}
                    <span className="text-primary first-letter:uppercase pl-1">
                      (
                      {capitalizeWords(
                        constants?.notesSubcategory.find(
                          (x) => x.key === note.type,
                        )?.title || 'General',
                        true,
                      )}
                      )
                    </span>
                  </p>
                  <p className="p-0 m-0 text-sm max-w-screen-lg overflow-hidden text-ellipsis clamp-3">
                    {note.msg}
                  </p>
                  <div className="grid grid-flow-col auto-cols-max gap-2 ">
                    {note.tags.map((tag) => (
                      <div
                        style={{
                          backgroundColor: `#${tag.color}`,
                        }}
                        className="grid grid-flow-row auto-cols-auto p-2 items-center rounded-md text-white"
                      >
                        <p className="p-0 m-0 text-sm">
                          {capitalizeWords(tag.name)}
                        </p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
              <div className="grid-flow-col grid items-center gap-2 self-start mt-[-6px]">
                <Avatar
                  src={note.creator.profile}
                  className="bg-primary h-8 w-8 p-0 m-0"
                />
                <p className="p-0 m-0 font-[500] text-sm ">
                  {`${capitalizeWords(
                    `${note.creator.firstName} ${note.creator.lastName}`,
                    true,
                  )}`}
                </p>
                {note.attachments && note.attachments.length > 0 && (
                  <div className="bg-icon-attachment bg-center bg-no-repeat w-8 h-8" />
                )}

                <p className="p-0 m-0 text-sm">
                  <SafeDate date={note.updatedAt} time />
                </p>
                <MoreHoriz
                  className="text-[#747A7A] hover:text-[#0a0a0a] cursor-pointer w-10 h-10 p-2 z-10"
                  onClick={(e) => {
                    setMenuNote(note);
                    e.stopPropagation();
                    handleClick(e as any);
                  }}
                  aria-controls={open ? 'basic-menu' : undefined}
                  aria-haspopup="true"
                  aria-expanded={open ? 'true' : undefined}
                />
              </div>
            </div>
          ))}
        <Menu
          id="basic-menu"
          sx={{
            '& .MuiPaper-root': {
              minWidth: '250px',
              minHeight: '',
              backgroundColor: '#001018',
              boxShadow: 0,
              borderRadius: '18px',
              color: 'white',
              cursor: 'pointer',
            },
          }}
          anchorEl={anchorEl}
          open={open}
          onClose={handleClose}
          MenuListProps={{
            'aria-labelledby': 'basic-button',
          }}
        >
          <MenuItem
            className="text-sm justify-center hover:text-red-500"
            onClick={() => handleClickDelete()}
          >
            Delete Note
          </MenuItem>
        </Menu>
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    // let orgId;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      org = cookies.orgID;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    // eslint-disable-next-line max-len
    const request: any = await API.MEMBERS.fetchMemberNotes(memberId, token);
    return {
      props: {
        notes: request.items || [],
        member: member || [],
      },
    };
  } catch (error) {
    return {
      props: {
        notes: [],
        member: [],
      },
    };
  }
}

export default Notes;
