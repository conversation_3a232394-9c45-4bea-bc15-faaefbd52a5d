/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useContext, useState } from 'react';
import {
  Button, TextField, MenuItem, Checkbox,
} from '@mui/material';
import { Check, EditOutlined } from '@mui/icons-material';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import SelectBox from 'components/select-box/select-box';
import { Member } from 'types/members-response-type';
import API from 'api/src/lib/api';
import AttachmentPicker from 'components/attachment-picker/attachment-picker';
import { useModal } from 'contexts/modal-context/modal-context';
import MemberLayout from 'layouts/member-details-layout';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import { useRouter } from 'next/router';

interface NewNoteProps {
  navigatorId: string;
  member: Member;
}

// eslint-disable-next-line react/function-component-definition
const NewNote = ({ navigatorId, member }: NewNoteProps) => {
  const constant = useContext(ConstantsContext);
  const router = useRouter();
  const [attachments, setAttachments] = useState<string[]>([]);
  const [desc, setDesc] = useState('');
  const [type, setType] = useState('');
  const [title, setTitle] = useState('');
  const { showModal } = useModal();
  const defaultValue = '';
  const [selectedItems, setSelectedItems] = useState<string[]>([
    ...defaultValue,
  ]);
  const handleAdd = async (status: string) => {
    if (desc === '' || selectedItems.length === 0 || title === '') {
      setTimeout(() => {
        showModal('Warning', 'Please fill all fields');
      }, 200);
      return;
    }
    if (desc === '') return;
    if (selectedItems.length === 0) return;
    const tags = selectedItems.map((tagKey) => {
      const tag = constant?.noteTags?.find((t) => t.key === tagKey);
      return {
        name: tag?.title,
        key: tag?.key,
        color: tag?.color,
      };
    });

    const model = {
      creator: navigatorId,
      memberID: member.id,
      title,
      type: type || 'basic',
      msg: desc,
      tags, // This now includes multiple tags
      attachments: attachments.map((att: any) => ({
        type: 'note',
        name: `Note-${title}`,
        category: att.url.startsWith('data:application/pdf') ? 'pdf' : 'image',
        base64: att.url,
      })),
      status,
    };
    await API.NOTES.createNote(model);
    router.push(`/members/notes/${member.id}`);
  };

  return (
    <MemberLayout
      memberId={member.id}
      active="notes"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Notes"
      updateUrl={`/members/notes/${member.id}`}
      addLabel="New Note"
      add
      rightSide={(
        <div style={{ display: 'flex', flexWrap: 'nowrap' }}>
          <Button
            onClick={() => handleAdd('draft')}
            component="label"
            className="rounded-md m-0 p-0 h-[40px] bg-[#F7F8F8] hover:bg-[#f0f0f0] text-[#008390] mr-3 min-w-[164px] transition-none"
          >
            <EditOutlined style={{ fontSize: 18, marginRight: 8 }} />
            Save as Draft
          </Button>
          <Button
            onClick={() => handleAdd('active')}
            variant="contained"
            color="success"
            component="label"
            className="rounded-md m-0 p-0 h-[40px] bg-gradient-to-b hover:opacity-80"
            style={{
              background: 'linear-gradient(to bottom, #FD8205, #E97100)',
            }}
          >
            <Check style={{ fontSize: 18, marginRight: 8 }} />
            Save
          </Button>
        </div>
      )}
    >
      <h3 className="font-bold pl-5">Note Details</h3>
      <div className="grid grid-flow-row h-full pl-5">
        <div className="flex items-center w-full">
          <TextField
            variant="filled"
            value={title}
            label="Title"
            className="w-[400px] mr-[30px]"
            onChange={(e) => {
              setTitle(e.target.value);
            }}
          />
          <div className="w-[400px]">
            <SelectBox
              keyVal="category"
              items={constant?.notesSubcategory}
              defaultValue={type}
              label="Category"
              onChange={(e) => {
                setType(e.target.value);
              }}
            />
          </div>
        </div>
        <div className="borderLine ">
          <TextField
            onChange={(e) => {
              setDesc(e.target.value);
            }}
            multiline
            variant="filled"
            minRows={10}
            inputProps={{
              style: { height: 300 },
            }}
            className="w-full overflow-scroll h-[200px] mt-2 font-[300] placeholder:text-[12px]"
            label="Enter a note..."
          />
        </div>
        <div className="pt-2 w-1/5">
          <AttachmentPicker
            multiple
            attachments={attachments}
            attachmentsToRemove={[]}
            setPicture={setAttachments}
            setAttachmentsToRemove={() => {
              // do nothing
            }}
            edit
          />
        </div>

        <div>
          <h3 className="font-bold">Tags</h3>
          {constant?.noteTags.map((item) => (
            <MenuItem
              className="p-0"
              key={item.key}
              onClick={() => {
                setSelectedItems((prevSelectedItems) => {
                  const currentIndex = prevSelectedItems.indexOf(
                    item.key.toLowerCase(),
                  );
                  const newSelectedItems = [...prevSelectedItems];

                  if (currentIndex === -1) {
                    newSelectedItems.push(item.key.toLowerCase());
                  } else {
                    newSelectedItems.splice(currentIndex, 1);
                  }

                  return newSelectedItems; // Return the updated state
                });
              }}
            >
              <Checkbox
                checked={selectedItems.includes(item.key.toLowerCase())}
              />
              <span className="first-letter:uppercase">{item.title}</span>
            </MenuItem>
          ))}
        </div>
      </div>
    </MemberLayout>
  );
};

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    let navigatorId;
    // let orgId;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      org = cookies.orgID;
      navigatorId = cookies.userID;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    return {
      props: {
        member: member || [],
        navigatorId,
      },
    };
  } catch (error) {
    return {
      props: {
        member: [],
        nagivatorId: '',
      },
    };
  }
}

export default NewNote;
