/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useContext, useEffect, useState } from 'react';
import {
  Button, TextField, MenuItem, Checkbox,
} from '@mui/material';
import { Check, DeleteOutline, EditOutlined } from '@mui/icons-material';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import SelectBox from 'components/select-box/select-box';
import { Member } from 'types/members-response-type';
import { capitalizeWords } from 'utils/helper';
import API from 'api/src/lib/api';
import AttachmentPicker from 'components/attachment-picker/attachment-picker';
import { useModal } from 'contexts/modal-context/modal-context';
import MemberLayout from 'layouts/member-details-layout';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import { useRouter } from 'next/router';
import { NotesTypes } from 'types/notes-types';

interface SelectSocialPlanProps {
  navigatorId: string;
  member: Member;
  note: NotesTypes;
}

// eslint-disable-next-line react/function-component-definition
const DraftNote = ({ navigatorId, member, note }: SelectSocialPlanProps) => {
  const constant = useContext(ConstantsContext);
  const router = useRouter();
  const [attachments, setAttachments] = useState<string[]>([]);
  const [attachmentsToRemove, setAttachmentsToRemove] = useState<string[]>([]);
  const [desc, setDesc] = useState('');
  const [type, setType] = useState('');
  const [title, setTitle] = useState('');
  const { showModal } = useModal();
  const defaultValue = '';
  const [selectedItems, setSelectedItems] = useState<string[]>([
    ...defaultValue,
  ]);

  useEffect(() => {
    setTitle(note.title);
    setDesc(note.msg);
    setType(note.type);
    setSelectedItems(note.tags.map((tag) => tag.key));

    // Populate attachments with existing note attachments
    if (note.attachments && note.attachments.length > 0) {
      setAttachments(note.attachments.map((att) => att));
    }
  }, [note]);

  async function handleDelete() {
    const attachmentIds: string[] = [];
    if (note?.id) {
      await API.NOTES.deleteNote(note.id);
      if (note?.attachments?.length > 0) {
        note.attachments.forEach((att) => {
          attachmentIds.push(att.id);
        });
        await API.NOTES.deleteAttachments(attachmentIds);
      }
      router.push(`/members/notes/${member.id}`);
    }
  }

  const handleSave = async (status: string) => {
    if (!title) {
      showModal('Warning', 'Title is required.');
      return;
    }
    if (!desc) {
      showModal('Warning', 'Description is required.');
      return;
    }
    if (!selectedItems?.length) {
      showModal('Warning', '1+ note tags is required.');
      return;
    }

    const tags = selectedItems.map((tagKey) => {
      const tag = constant?.noteTags?.find((t) => t.key === tagKey);
      return {
        name: tag?.title,
        key: tag?.key,
        color: tag?.color,
      };
    });

    const model = {
      creator: navigatorId,
      memberID: member.id,
      title,
      type: type || 'basic',
      msg: desc,
      tags, // This now includes multiple tags
      attachments: attachments
        .filter((a: any) => !a.id)
        .map((att: any) => ({
          type: 'note',
          name: `Note-${title}`,
          category: att.url.startsWith('data:application/pdf')
            ? 'pdf'
            : 'image',
          base64: att.url,
        })),
      status,
    };
    const attachmentIds = attachmentsToRemove.map((a: any) => a.id);

    await API.NOTES.updateNote(note.id, model);

    // only perform if we have attachments to remove
    if (attachmentIds?.length) await API.NOTES.deleteAttachments(attachmentIds);

    router.push(`/members/notes/${member.id}`);
  };

  function handleClickDelete() {
    showModal(
      'Warning',
      'Are you sure you want to delete this draft? It will be permanently removed from the system.',
      async () => {
        await handleDelete();
      },
    );
  }

  function handleClickPublish() {
    showModal(
      'Confirm',
      'Are you sure you want to publish this note?',
      async () => {
        await handleSave('active');
      },
    );
  }

  return (
    <MemberLayout
      memberId={member.id}
      active="notes"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Notes"
      updateUrl={`/members/notes/${member.id}`}
      addLabel={
        note.title ? `${capitalizeWords(note.title)} (Draft)` : 'Edit Draft'
      }
      add
      rightSide={(
        <div style={{ display: 'flex', flexWrap: 'nowrap' }}>
          <Button
            onClick={() => handleClickDelete()}
            component="label"
            className="rounded-md m-0 p-0 h-[40px] bg-[#F7F8F8] hover:bg-[#f0f0f0] text-[#E42B57] mr-3 min-w-[164px] transition-none"
          >
            <DeleteOutline style={{ fontSize: 18, marginRight: 8 }} />
            Delete Draft
          </Button>
          <Button
            onClick={() => handleSave('draft')}
            component="label"
            className="rounded-md m-0 p-0 h-[40px] bg-[#F7F8F8] hover:bg-[#f0f0f0] text-[#008390] mr-3 min-w-[164px] transition-none"
          >
            <EditOutlined style={{ fontSize: 18, marginRight: 8 }} />
            Save as Draft
          </Button>
          <Button
            onClick={() => handleClickPublish()}
            variant="contained"
            color="success"
            component="label"
            className="rounded-md m-0 p-0 h-[40px] bg-gradient-to-b hover:opacity-80"
            style={{
              background: 'linear-gradient(to bottom, #FD8205, #E97100)',
            }}
          >
            <Check style={{ fontSize: 18, marginRight: 8 }} />
            Publish
          </Button>
        </div>
      )}
    >
      <h3 className="font-bold pl-5">Note Details</h3>
      <div className="grid grid-flow-row h-full pl-5">
        <div className="flex items-center w-full">
          <TextField
            variant="filled"
            value={title}
            label="Title"
            className="w-[400px] mr-[30px]"
            onChange={(e) => {
              setTitle(e.target.value);
            }}
          />
          <div className="w-[400px]">
            <SelectBox
              keyVal="category"
              items={constant?.notesSubcategory}
              defaultValue={type}
              label="Category"
              onChange={(e) => {
                setType(e.target.value);
              }}
            />
          </div>
        </div>
        <div className="borderLine ">
          <TextField
            onChange={(e) => {
              setDesc(e.target.value);
            }}
            value={desc}
            multiline
            variant="filled"
            minRows={10}
            inputProps={{
              style: { height: 300 },
            }}
            className="w-full overflow-scroll h-[200px] mt-2 font-[300] placeholder:text-[12px]"
            label="Enter a note..."
          />
        </div>

        <div className="pt-2 w-1/5">
          <AttachmentPicker
            multiple
            attachments={attachments}
            attachmentsToRemove={attachmentsToRemove}
            setPicture={setAttachments}
            setAttachmentsToRemove={setAttachmentsToRemove}
            edit
          />
        </div>

        <div>
          <h3 className="font-bold">Tags</h3>
          {constant?.noteTags.map((item) => (
            <MenuItem
              className="p-0"
              key={item.key}
              onClick={() => {
                setSelectedItems((prevSelectedItems) => {
                  const currentIndex = prevSelectedItems.indexOf(
                    item.key.toLowerCase(),
                  );
                  const newSelectedItems = [...prevSelectedItems];

                  if (currentIndex === -1) {
                    newSelectedItems.push(item.key.toLowerCase());
                  } else {
                    newSelectedItems.splice(currentIndex, 1);
                  }

                  return newSelectedItems; // Return the updated state
                });
              }}
            >
              <Checkbox
                checked={selectedItems.includes(item.key.toLowerCase())}
              />
              <span className="first-letter:uppercase">{item.title}</span>
            </MenuItem>
          ))}
        </div>
      </div>
    </MemberLayout>
  );
};

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;

    let token;
    let org;
    let navigatorId;

    const memberId: string = context.query.id as string;
    const noteId: string = context.query['note-id'] as string;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);

      token = cookies.token; // get cookie from context.req.headers.cookie named token
      org = cookies.orgID;
      navigatorId = cookies.userID;
    }

    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    const note: any = await API.NOTES.getNote(noteId, token);

    return {
      props: {
        member: member || [],
        navigatorId,
        note: note || [],
      },
    };
  } catch (error) {
    return {
      props: {
        member: [],
        nagivatorId: '',
        note: [],
      },
    };
  }
}

export default DraftNote;
