import { Avatar, Dialog, DialogContent } from '@mui/material';
import API from 'api/src/lib/api';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { parse } from 'cookie';
import MemberLayout from 'layouts/member-details-layout';
import { GetServerSidePropsContext } from 'next';
import { useContext, useState, useEffect } from 'react';
import { NotesTypes } from 'types/notes-types';
import { capitalizeWords } from 'utils/helper';
import ReactMarkdown from 'react-markdown';
import SafeDate from 'components/date-time/SafeDate';

interface NoteDetailProps {
  note: NotesTypes;
}

interface MemberProps {
  id: any,
  firstName: string,
  lastName: string
}

function NoteDetail({ note }: NoteDetailProps) {
  const [open, setOpen] = useState(false); // State to handle Dialog open/close
  const [currentImage, setCurrentImage] = useState(''); // State to handle current image
  const constants = useContext(ConstantsContext);
  const handleClickOpen = (imageUrl: string) => {
    setCurrentImage(imageUrl);
    if (imageUrl.endsWith('.pdf')) {
      window.open(imageUrl, '_blank');
      return;
    }
    setOpen(true);
  };
  const handleClose = () => {
    setOpen(false);
  };
  const [member, setMember] = useState<MemberProps>();

  useEffect(() => {
    const memberData = JSON.parse(sessionStorage.getItem('member') || '{}');
    setMember(memberData);
  }, []);
  // eslint-disable-next-line max-len
  const formattedTeamRole = (role: string[]) => constants?.roles.find((x) => role.includes(x.key))?.title || role;
  return (
    <MemberLayout
      memberId={member?.id || ''}
      active="notes"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Notes"
      updateUrl={`/members/notes/${member?.id}`}
      addLabel={capitalizeWords(note.title, true)}
      add
      rightSide={<div />}
    >
      <div className="grid grid-flow-row pl-10 placeholder-teal-100 relative">
        <div>
          <h3 className="font-bold my-2 text-lg">Note</h3>
          <p className="mt-0 text-[15px] max-w-[70%] min-w-[600px]">
            <ReactMarkdown>{note.msg}</ReactMarkdown>
          </p>
        </div>

        <div>
          <h3 className="font-bold mt-6 text-lg">Note Details</h3>
          <div className="grid grid-cols-3 gap-4">
            <div>
              <div className="text-[13px] text-[#747A7A]">Title</div>
              <div className="text-[15px]">
                {capitalizeWords(note.title, true)}
              </div>
            </div>
            <div className="">
              <div>
                <div className="text-[13px] text-[#747A7A]">Created by</div>
                <div className="text-[15px] flex">
                  {/* <Avatar
                    src={note.creator.profile}
                    className="bg-primary h-8 w-8 p-0 m-0"
                  /> */}
                  <p className="p-0 m-0">
                    {`${capitalizeWords(
                      `${note.creator.firstName} ${note.creator.lastName}`,
                      true,
                    )} - ${formattedTeamRole(note.creator.roles)}`}
                  </p>
                </div>
              </div>
            </div>
            <div>
              <div className="text-[13px] text-[#747A7A]">Date</div>
              <div className="text-[15px]">
                <SafeDate date={note.updatedAt} time />
              </div>
            </div>
          </div>
          <div className="mt-[20px]">
            <div className="text-[13px] text-[#747A7A]">Category</div>
            <div className="text-[15px]">
              {capitalizeWords(
                constants?.notesSubcategory.find((x) => x.key === note.type)
                  ?.title || 'General',
                true,
              )}
            </div>
          </div>
          <div className="mt-[20px]">
            <div className="text-[13px] text-[#747A7A] mb-1">Tags</div>
            <div className="grid grid-flow-col auto-cols-max gap-2 ">
              {note.tags.map((tag) => (
                <div
                  style={{
                    backgroundColor: `#${tag.color}`,
                  }}
                  className="grid grid-flow-row auto-cols-auto p-2 items-center rounded-md text-white"
                >
                  <p className="p-0 m-0 text-sm">{capitalizeWords(tag.name)}</p>
                </div>
              ))}
            </div>
          </div>
        </div>
        <div className="mt-[24px]">
          {note.attachments.length > 0 && (
            <div className="text-[13px] text-[#747A7A] mb-1">Attachments</div>
          )}
          <div className="grid grid-flow-col gap-5 auto-cols-max">
            {note.attachments
              && note.attachments.map((attachment) => (
                // eslint-disable-next-line max-len
                // eslint-disable-next-line jsx-a11y/click-events-have-key-events, jsx-a11y/no-static-element-interactions
                <div
                  onClick={() => {
                    handleClickOpen(attachment?.url);
                  }}
                  style={{
                    border: '1px solid #e0e0e0',
                  }}
                  className="cursor-pointer grid grid-flow-row auto-cols-max w-[200px] items-start h-[200px] rounded-md overflow-hidden"
                >
                  <Avatar
                    src={attachment?.url}
                    alt="attachment"
                    className="w-[200px] h-[150px] rounded-none object-fill"
                  />
                  <p className="text-xs pl-1 font-semibold m-0">
                    <SafeDate date={attachment?.createdAt} />
                  </p>
                  <p className="text-xs pl-1 font-normal m-0">
                    {attachment?.name}
                  </p>
                </div>
              ))}
          </div>
        </div>
        <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
          <DialogContent>
            <Avatar
              className="rounded-md  w-[100%] h-auto object-contain"
              src={currentImage}
            />
          </DialogContent>
        </Dialog>
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    // let org;
    // let orgId;
    const noteId: string = context.query['note-id'] as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      //   org = cookies.orgID;
    }
    // eslint-disable-next-line max-len
    const request: any = await API.NOTES.getNote(noteId, token);
    return {
      props: {
        note: request || [],
      },
    };
  } catch (error) {
    return {
      props: {
        note: [],
      },
    };
  }
}

export default NoteDetail;
