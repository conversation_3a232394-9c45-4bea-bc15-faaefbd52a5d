/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */
import API from 'api/src/lib/api';
import MembersAddUpdate from 'components/members/add-update';
import { parse } from 'cookie';
import MainLayout from 'layouts/main-layout';
import { GetServerSidePropsContext } from 'next';
import { MemberStatusProps } from 'types/member-status-types';
import { Member } from 'types/members-response-type';

interface Props {
    member: Member;
    status: MemberStatusProps[];
    add: boolean;
    }
function MemberAdd({ member, add, status }:Props) {
  return (
    <MainLayout buttonProps={{
      addButton: false,
    }}
    >
      <MembersAddUpdate add={add} status={status} member={member} />
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let orgId;
    const memberId:string = context.query.id as string;
    if (memberId === 'add') {
      return {
        props: {
          member: {},
          status: [],
          add: true,
        },
      };
    }
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      orgId = cookies.orgID;
    }

    const request = await API.MEMBERS.fetchMember(memberId, token, orgId);
    const status = await API.MEMBERS.fetchMemberStatus(memberId, token);

    const member = request;

    return {
      props: {
        member: member || {},
        status: status || [],
        add: false,
      },
    };
  } catch (error) {
    return {
      props: {
        member: {},
        status: [],
        add: true,
      },
    };
  }
}

export default MemberAdd;
