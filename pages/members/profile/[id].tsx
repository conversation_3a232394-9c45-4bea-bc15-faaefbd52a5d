/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { GetServerSidePropsContext } from 'next';
import { Member } from 'types/members-response-type';
import { parse } from 'cookie';
import { useContext, useEffect, useState } from 'react';
import API, { ORG_ID, TOKEN } from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import ProfileInfo from 'components/members/commons/profile-info';
import AddMemberDrawer from 'components/members/add-update/add-members-drawer';
import { MemberStatusProps } from 'types/member-status-types';
import AddRow from 'components/members/profile/AddRow';
import AddItemModal from 'components/members/profile/AddItemModal';
import useAuth from 'hooks/use-auth';
import { getLatestImage } from 'utils/helper';
import AddInsuranceModal from 'components/members/insurance/AddInsuranceModal';
import AssignTeamPrimaryNavigator from 'components/members/profile/AddTeamPrimaryNavigator';
import useRefreshSSR from 'hooks/use-refresh-ssr';

interface MembersDetailProps {
  member: Member;
  status: MemberStatusProps[];
  diagnoses: any[];
  medications: any[];
  problems: any[];
  teams: any[];
  insurances: any[];
  contacts: any[];
}

function MembersDetail({
  member,
  status,
  diagnoses,
  medications,
  problems,
  teams,
  insurances,
  contacts,
}: MembersDetailProps) {
  const [open, setOpen] = useState(false);
  const [updatedMember, setUpdatedMember] = useState({} as Member);
  const [sections, setSections] = useState<any>({
    teams: teams || [],
    contacts: contacts || [],
    diagnoses: diagnoses || [],
    medications: medications || [],
    problems: problems || [],
    insurances: insurances || [],
  });
  const constants = useContext(ConstantsContext);
  const [openModalType, setOpenModalType] = useState<string | null>(null);
  const { user } = useAuth();
  const { refresh } = useRefreshSSR();
  useEffect(() => {
    setSections({
      teams,
      contacts,
      diagnoses,
      medications,
      problems,
      insurances,
    });
  }, [diagnoses, medications, problems, teams, insurances, contacts]);

  useEffect(() => {
    const fetchPrimaryNavigator = async () => {
      const primaryNavigator = await API.USER.getUser(
        member.primaryUser.id,
        TOKEN(),
        ORG_ID(),
      );

      setSections((prev: any) => ({
        ...prev,
        teams: teams.map((team: any) => ({
          ...team,
          primaryNavigator,
        })),
      }));
    };

    if (member?.primaryUser?.id) {
      fetchPrimaryNavigator();
    }
  }, [teams, member]);

  const handleEdit = () => {
    setUpdatedMember(member as any);
    setOpen(true);
  };

  const formatTitle = (key: string) => {
    switch (key) {
      case 'diagnoses':
        return 'Diagnoses';
      case 'medications':
        return 'Medications';
      case 'problems':
        return 'Problems List';
      case 'teams':
        return 'Teams';
      case 'insurances':
        return 'Insurances';
      case 'contacts':
        return 'Emergency Contact & Caregivers';
      default:
        return '';
    }
  };

  const handleOpenModal = (type: string) => {
    setOpenModalType(type);
  };

  function toISOStringWithoutMillis(date: Date) {
    return date.toISOString().replace(/\.\d{3}Z$/, 'Z');
  }

  const handleAddItem = async (type: string, fields: any) => {
    const updatedFields = {
      ...fields,
      ...(type === 'problems' && {
        confirmedBy: `${user?.firstName} ${user?.lastName}`,
        authorProfile: getLatestImage(user?.attachments as any, 'userProfile'),
      }),
    };

    const createModel = () => {
      if (type === 'diagnoses') {
        return {
          icdCode: updatedFields.icdCode,
          description:
            updatedFields.description || updatedFields.clinicalNote || '',
          diagnosisDate: updatedFields?.dateIdentified
            ? toISOStringWithoutMillis(new Date(updatedFields?.dateIdentified))
            : toISOStringWithoutMillis(new Date()),
          status: updatedFields.status || 'active',
          severity: updatedFields.severity,
          notes: updatedFields.notes,
          dateIdentified: updatedFields?.dateIdentified
            ? toISOStringWithoutMillis(new Date(updatedFields?.dateIdentified))
            : toISOStringWithoutMillis(new Date()),
          source: updatedFields.source,
          title: updatedFields.title,
        };
      }
      if (type === 'medications') {
        return {
          rxNormCode: updatedFields.rxcui,
          medicationName: updatedFields.name,
          medicationType: updatedFields.type.title || 'Medical',
          dosage: updatedFields.dosage || '',
          frequency: updatedFields.frequency || '',
          startDate: updatedFields?.startDate
            ? toISOStringWithoutMillis(new Date(updatedFields?.startDate))
            : toISOStringWithoutMillis(new Date()),
          endDate: updatedFields?.endDate
            ? toISOStringWithoutMillis(new Date(updatedFields?.endDate))
            : null,
          prescribedBy: `${user?.firstName} ${user?.lastName}` || '',
          status: 'active',
          adherenceNotes: updatedFields.description || '',
          route: updatedFields.route || '',
          source: updatedFields.source || 'care team',
        };
      }
      if (type === 'problems') {
        return {
          clinicalNote: updatedFields.clinicalNote,
          status: 'active',
          dateIdentified: updatedFields?.dateIdentified
            ? toISOStringWithoutMillis(new Date(updatedFields?.dateIdentified))
            : toISOStringWithoutMillis(new Date()),
          source: updatedFields.source,
          confirmedBy: updatedFields.confirmedBy,
          description: updatedFields.description,
          title: updatedFields.title,
          type: updatedFields.type.title,
        };
      }
      if (type === 'contacts') {
        return {
          full_name: updatedFields.name,
          email: updatedFields.email,
          phone: updatedFields.phone,
          relationship: updatedFields.relationship,
          role: updatedFields.role.key,
        };
      }
      return null;
    };

    const model = createModel();
    if (!model) return;

    const createMemberItem = async (
      sectionType: string,
      sectionFields: any,
    ) => {
      let urlType;
      if (sectionType === 'contacts') {
        urlType = 'associated-persons';
      } else {
        urlType = sectionType;
      }
      const response = await API.MEMBERS.createMemberItemByType(
        member.id,
        urlType,
        sectionFields,
      );
      return response;
    };

    const response = await createMemberItem(type, model);

    if (response && response.status === 200) {
      // merge into state
      setSections((prev: any) => ({
        ...prev,
        [type]: [...(prev[type] || []), response.data],
      }));
    }
  };

  const handleAddInsurance = async (insurance: any) => {
    const response = await API.MEMBERS.createMemberInsurance(insurance);
    if (response) {
      setSections((prev: any) => ({
        ...prev,
        insurances: [...(prev.insurances || []), response],
      }));
    }
  };

  const handleDelete = async (id: string, type: string) => {
    if (type === 'contacts') {
      const response = await API.MEMBERS.deleteContact(member.id, id);
      if (response.status === 200 || response.status === 204) {
        setSections((prev: any) => ({
          ...prev,
          [type]: prev[type].filter((item: any) => item.id !== id),
        }));
      }
    }

    if (type === 'medications') {
      const response = await API.MEMBERS.updateMedication(member.id, id, {
        status: 'discontinued',
      });

      if (response.status === 200 || response.status === 204) {
        setSections((prev: any) => ({
          ...prev,
          [type]: prev[type].map((item: any) => (item.id === id ? { ...item, status: 'discontinued' } : item)),
        }));
      }
    }
  };

  const handleAddTeamPrimaryNavigator = async (
    selectedTeam: any,
    selectedPrimaryNavigator: any,
  ) => {
    if (sections?.teams?.length > 0) {
      await API.TEAMS.removeTeamFromHousehold(
        member?.households[0]?.id,
        sections?.teams[0]?.id,
      );
    }
    await API.TEAMS.assignTeamToHousehold(
      member?.households[0]?.id,
      selectedTeam.id,
    );
    if (sections?.teams?.length > 0) {
      await API.MEMBERS.deletePrimaryNavigator(member.id);
    }
    await API.MEMBERS.addPrimaryNavigator(
      member?.id,
      selectedPrimaryNavigator.id,
    );
    refresh();
  };

  const handleRemoveTeamPrimaryNavigator = async () => {
    if (sections?.teams?.length > 0) {
      await API.TEAMS.removeTeamFromHousehold(
        member?.households[0]?.id,
        sections.teams[0].id,
      );
      await API.MEMBERS.deletePrimaryNavigator(member?.id);
    }
    refresh();
  };

  return (
    <MemberLayout
      memberId={member?.id}
      active="profile"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Profile"
    >
      <div className="w-full innerSpace pl-10 pr-0 pb-[100px]">
        <ProfileInfo
          member={member}
          constants={constants}
          handleEdit={handleEdit}
        />
        {Object.keys(sections).map((key) => (
          <AddRow
            key={key}
            title={formatTitle(key)}
            items={sections?.[key]}
            setOpenAddItem={() => handleOpenModal(key)}
            name={key}
            handleDelete={(id) => handleDelete(id, key)}
            handleRemoveTeamPrimaryNavigator={handleRemoveTeamPrimaryNavigator}
          />
        ))}
        {openModalType
          && openModalType !== 'insurances'
          && openModalType !== 'teams' && (
            <AddItemModal
              open={!!openModalType}
              setOpen={() => setOpenModalType(null)}
              handleAdd={handleAddItem}
              type={openModalType}
              sections={sections}
            />
        )}
        {openModalType === 'insurances' && (
          <AddInsuranceModal
            open={!!openModalType}
            setOpen={() => setOpenModalType(null)}
            member={member}
            handleAdd={handleAddInsurance}
          />
        )}
        {openModalType === 'teams' && (
          <AssignTeamPrimaryNavigator
            open={!!openModalType}
            setOpen={() => setOpenModalType(null)}
            handleAddTeamPrimaryNavigator={handleAddTeamPrimaryNavigator}
            team={sections.teams[0]}
            primaryNavigator={sections?.teams[0]?.primaryNavigator}
          />
        )}
        <AddMemberDrawer
          status={status}
          member={updatedMember}
          setMember={setUpdatedMember}
          open={open}
          setOpen={setOpen}
        />
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token);
    const diagnoses = await API.MEMBERS.fetchMemberDiagnoses(memberId, token);
    const medications = await API.MEMBERS.fetchMemberMedications(
      memberId,
      token,
    );
    const problems = await API.MEMBERS.fetchMemberProblems(memberId, token);
    const teams = await API.MEMBERS.fetchTeamsByMemberId(memberId, token);
    const status = await API.MEMBERS.fetchMemberStatus(memberId, token);
    const insurances: any = await API.MEMBERS.getMemberInsurance(
      memberId,
      token,
    );
    const contacts: any = await API.MEMBERS.fetchMemberAssociatedPersons(
      memberId,
      token,
    );

    return {
      props: {
        member: member || {},
        status: status || [],
        diagnoses: diagnoses || [],
        medications: medications || [],
        problems: problems || [],
        teams: teams || [],
        insurances: insurances.items || [],
        contacts: contacts || [],
      },
    };
  } catch (error) {
    return {
      props: {
        member: {},
        status: [],
        diagnoses: [],
        medications: [],
        problems: [],
        teams: [],
        insurances: [],
        contacts: [],
      },
    };
  }
}

export default MembersDetail;
