/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { Member } from 'types/members-response-type';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import useScore from 'hooks/use-score';
import { Avatar, Button } from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import { HouseHold } from 'types/household-types';
import HouseHoldMembers from 'components/households/household-members';
import AddHouseholdDrawer from 'components/households/add-update/add-household-drawer';
import { useState } from 'react';
import { capitalizeWords, getLatestImage, isEmptyObject } from 'utils/helper';
import EditOutlinedIcon from '@mui/icons-material/EditOutlined';

interface MembersDetailProps {
  household: HouseHold;
  member: Member;
}

function MembersDetail({ household, member }: MembersDetailProps) {
  const {
    members, attachments, title, address, teams, householdScore,
  } = household;
  const { getScoreTitle } = useScore();
  const [addHouseholdDrawer, setHouseholdDrawer] = useState(false);
  const [editHousehold] = useState<any>(household);
  // const router = useRouter();
  return (
    <MemberLayout
      memberId={member?.id}
      active="household"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Households"
    >
      <div className="pl-10 mt-2">
        {isEmptyObject(household) ? (
          <div className="justify-center grid">
            <p>No household assigned</p>
            <Button
              onClick={() => {
                window.location.href = '/households';
              }}
            >
              Go to Households
            </Button>
          </div>
        ) : (
          <>
            <div
              className="flex pb-8"
              style={{ borderBottom: '1px solid #DCDFDF' }}
            >
              <Avatar
                className="rounded-md w-[400px] h-[266px]"
                src={getLatestImage(attachments, 'householdProfile')}
              />
              <div className="pl-20 pt-10 w-full flex flex-col">
                <div className="flex justify-between">
                  <p className="p-0 m-0 font-[500] text-3xl">{title}</p>
                  <Button
                    onClick={() => setHouseholdDrawer(true)}
                    className="grey-btn rounded-md text-primary"
                    type="button"
                    title="Edit"
                  >
                    <EditOutlinedIcon className="text-sm mr-2" />
                    Edit Household
                  </Button>
                </div>
                <div className="flex gap-[160px] mt-[48px]">
                  <div className="flex gap-5">
                    <div className="text-[15px] font-[400] flex flex-col no-child-p-margin">
                      <p>Address:</p>
                      <p>Team:</p>
                      <p>Household Score:</p>
                    </div>
                    <div className="text-[15px] font-[500] flex flex-col no-child-p-margin">
                      <p>
                        {' '}
                        {address
                          && `${address[0].street}, ${
                            address[0]?.street2 || ''
                          } ${capitalizeWords(
                            address[0].city,
                          )}, ${capitalizeWords(
                            address[0].state,
                          )}, ${capitalizeWords(address[0].zip)} `}
                      </p>
                      <p>
                        {' '}
                        {`${teams?.map((x) => `${x.name}, `)}`}
                      </p>
                      <p
                        style={{
                          color: getScoreTitle(householdScore)?.color,
                        }}
                        className="font-[500]"
                      >
                        {` ${getScoreTitle(householdScore)?.title}`}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="pt-6">
              <p className="font-[600] text-[18px] mb-5">
                Household Members
                {`(${members?.length})`}
              </p>
              <HouseHoldMembers
                members={members as unknown as Member[]}
                headId={household?.headOfHouse?.id}
                householdId={household?.id}
              />
            </div>
          </>
        )}
        <AddHouseholdDrawer
          household={editHousehold}
          setHousehold={() => null}
          open={addHouseholdDrawer}
          setOpen={setHouseholdDrawer}
        />
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    // let orgId;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
    }
    // eslint-disable-next-line max-len
    const requestMember: any = await API.MEMBERS.fetchMember(memberId, token);

    const household = await API.HOUSEHOLDS.fetchSingleHousehold(
      requestMember.households[0]?.id,
      token,
    );
    return {
      props: {
        household: household || [],
        member: requestMember || [],
      },
    };
  } catch (error) {
    return {
      props: {
        household: [],
        member: [],
      },
    };
  }
}

export default MembersDetail;
