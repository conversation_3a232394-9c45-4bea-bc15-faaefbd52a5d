/* eslint-disable no-restricted-syntax */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import {
  Answers,
  MemberProfileResponseDetail,
  Questions,
  Section,
} from 'types/members-response-type';
import { parse } from 'cookie';
import { useContext, useEffect, useState } from 'react';
import API from 'api/src/lib/api';
import {
  capitalizeWords, sleep,
} from 'utils/helper';
import CustomLayout from 'layouts/custom-layout';
import Add from '@mui/icons-material/Add';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import useAuth from 'hooks/use-auth';
import SelectiveModal from 'components/selective-modal';
import useScore from 'hooks/use-score';
import SeeMore from 'components/see-more-text';
import SafeDate from 'components/date-time/SafeDate';

const processQuestions = (questions: Section[], answers: Answers[]) => {
  const result: any[] = [];

  for (const section of questions) {
    for (const question of section.questions) {
      const filteredAnswers = answers.filter((a) => a.parentID.toLowerCase() === question.id.toLowerCase());
      if (filteredAnswers.length > 0) {
        const existingQuestion = result.find((r) => r.id === question.id);
        if (existingQuestion) {
          existingQuestion.nestedAnswer = existingQuestion.nestedAnswer.concat(
            filteredAnswers.map((answer) => ({
              id: answer.parentID,
              answer: answer.answer,
              parentID: answer.parentID,
              questions: question.questions,
            })),
          );
        } else {
          result.push({
            section: section.title,
            id: question.id,
            question: question.message,
            score: question.score,
            answerId: filteredAnswers[0].id,
            answer: filteredAnswers[0].answer || 'no answer',
            questions: question.questions,
            nestedAnswer: filteredAnswers.slice(1).map((answer) => ({
              id: answer.parentID,
              answer: answer.answer,
              parentID: answer.parentID,
              questions: question.questions,
            })),
          });
        }
      }
    }
  }
  return result;
};

const getDeeper = (answers: Answers[], question:Questions, answer: string) => {
  const filteredQuestions = question?.questions?.find((q) => answer === q.message && q.id.toLowerCase() === answers.find((a) => a.parentID.toLowerCase() === q.id.toLowerCase())?.parentID);
  const nestedId = filteredQuestions?.id;
  const filteredAnswers = answers.filter((a) => a.parentID?.toLowerCase() === nestedId?.toLowerCase() && a.parentID !== a.questionID);
  return filteredAnswers;
};

interface MembersDetailProps {
  profile: MemberProfileResponseDetail;
  title:string;
}

interface ResultTypes {
  section: string;
  question: string;
  score: number;
  answer: string;
  nestedAnswer: {
    id: string;
    answer: string;
    parentID: string;
  }[];
}

function EncounterCheck({
  profile: {
    member, survey, answers, scored,
  }, title,
}: MembersDetailProps) {
  const [result, setResult] = useState<ResultTypes[] | null>(null);
  const { getSurveyScore } = useScore();
  const [openSocialPlanType, setOpenSocialPlanType] = useState(false);
  const [type, setType] = useState('');
  const socialPlanTypes = useContext(ConstantsContext)?.carepackageTypes;
  const { refresh } = useRefreshSSR();
  const { user } = useAuth();
  const [openNewSocialPlan, setOpenNewSocialPlan] = useState(false);
  const handleOnChangeType = async (key:string) => {
    setType(key);
    setOpenSocialPlanType(false);
    setOpenNewSocialPlan(true);
  };
  const prepareData = async () => {
    if (!survey || !answers) return;
    const data = processQuestions(survey?.sections, answers);
    setResult(data as ResultTypes[]);
  };

  const handleNewSocialPlan = async (data:any) => {
    const model = {
      title: data.title,
      status: 'active',
      reciever: member.id,
      creator: user?.id,
      orgID: user?.org.id,
      type: data.packageType,
    };
    const modelItem:any[] = [];

    const newSocialPlan:any = await API.SOCIALPLANS.createSocialPlan(model);
    data.networkList.forEach((item:any) => {
      item.network.forEach((network:any) => {
        modelItem.push({
          item: {
            networkID: network.id,
            creatorID: user?.id,
            type: item.type,
          },
        });
      });
    });

    modelItem.forEach(async (item:any) => {
      await API.SOCIALPLANS.createItemSocialPlan(item, newSocialPlan.id);
      sleep(1000);
    });
    // setOpen(false);
    await refresh();
  };

  useEffect(() => {
    prepareData();
  }, []);

  return (
    <CustomLayout
      leftButtonOnClick={() => {
        window.history.back();
      }}
      subTitle={title}
      title="Assessment - "
      button
      ButtonIcon={Add}
      rightButtonOnClick={() => { setOpenSocialPlanType(true); }}
      buttonText="Create Social Plan"
    >
      <div className="w-full innerSpace   pl-20 pr-20">
        <p className="text-secondary font-[500]">
          {`${capitalizeWords(survey?.status)}`}
          <span className="font-semibold text-dark-text">
            {' - '}
            <SafeDate date={survey?.updatedAt} />
          </span>
        </p>
        { scored && (
        <p className="text-dark-text text-xl font-semibold">
          Total Score:
          {` (${parseFloat(survey?.score).toFixed(2)} Points)`}
        </p>
        )}
        {result && result.map((section, key) => (
          <div key={section.section} className="innerSpace">
            <div key={section.question} className="borderLine p-2 flex-row flex justify-between">
              <div className="w-11/12 flex flex-col gap-2">
                <p className="m-0 font-semibold">
                  {`Q${key + 1}. ${section.question}`}
                </p>
                <SeeMore text={`${capitalizeWords(section.answer)}`} maxLength={500}>
                  <p className="pl-3 m-0 pt-2 font-normal">
                    {getDeeper(answers, section as unknown as Questions, section.answer).map((x) => x.answer).join(', ')}
                  </p>
                </SeeMore>
                <p className="m-0 font-normal">
                  {section.nestedAnswer && section.nestedAnswer.map((nestedAnswer) => (
                    <SeeMore
                      maxLength={500}
                      key={nestedAnswer.id}
                      nested
                      // eslint-disable-next-line no-mixed-operators
                      text={!nestedAnswer?.answer?.includes(section.answer) && `${capitalizeWords(nestedAnswer.answer)}` || ''}
                    >
                      {` ${getDeeper(answers, nestedAnswer as unknown as Questions, nestedAnswer.answer).map((x) => x.answer).join(', ')}`}
                      <p className="pl-3 pt-2 m-0 font-normal" />
                    </SeeMore>
                  ))}
                </p>
              </div>
              { scored && (
              <div
                key={key as number}
                style={{
                  backgroundColor: getSurveyScore(section.score)?.color,
                }}
                className="flex justify-center items-center pl-2 pr-2 rounded-md w-[84px] h-[31px]"
              >
                <p className="m-0 p-0 font-[400] text-sm text-white">
                  <span className="font-semibold m-0 p-0 text-sm text-white">{`Score: ${section.score}`}</span>
                </p>
              </div>
              )}
            </div>
          </div>
        ))}

      </div>
      <SelectiveModal
        open={openSocialPlanType}
        setOpen={setOpenSocialPlanType}
        title="Select Social Plan Type"
        itemTypes={socialPlanTypes as any}
        onChange={handleOnChangeType}
      />
      {/* <CreateSocialPlan
        member={member as any}
        type={type}
        open={openNewSocialPlan}
        setOpen={setOpenNewSocialPlan}
        callback={handleNewSocialPlan}
      /> */}
    </CustomLayout>
  );
}
export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const req:any = context;
    if (!req.query?.sections) {
      return {
        redirect: {
          destination: '/members',
          permanent: false,
        },
      };
    }

    // eslint-disable-next-line no-underscore-dangle
    const _sections = JSON.parse(req.query?.sections);
    let token;
    let orgId;
    if (req.req.headers.cookie) {
      const cookies = parse(req.req.headers.cookie);
      orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
    }
    // console.log(_sections.survey);
    const request:any = await API.MEMBERS.fetchCheckSurveyById(_sections.survey, token, orgId);
    console.log(_sections.survey);
    return {
      props: {
        profile: request || [],
        title: _sections.title,
        sKey: _sections.sKey,
      },
    };
  } catch (error) {
    return {
      props: {
        profile: [],
        title: '',
        sKey: '',
      },
    };
  }
}
export default EncounterCheck;
