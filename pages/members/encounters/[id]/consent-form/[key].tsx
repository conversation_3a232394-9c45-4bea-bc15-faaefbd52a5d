/* eslint-disable no-underscore-dangle */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { Member } from 'components/members/members-table/type';
import ConsentForm from 'components/consents/ConsentForm';

interface ConsentFormPageProps {
  member: Member;
  consentData: any;
}

function ConsentFormPage({
  member,
  consentData,
}: ConsentFormPageProps) {
  return (
    <MemberLayout
      memberId={member.id}
      active="encounters"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Encounters"
    >
      <div className="pl-10">
        <ConsentForm
          consentData={consentData}
          memberId={member.id}
        />
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let orgId;
    const memberId: string = context.query.id as string;
    const key: string = context.query.key as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
    }
    const _member = await API.MEMBERS.fetchMember(memberId, token, orgId);
    const consentTemplates = await API.ASSESSMENT.fetchConsentFormJson(orgId as string, token);
    const consentTemplate = consentTemplates.find((template: any) => template.key === key);
    const member = _member;

    return {
      props: {
        member: member || [],
        consentData: JSON.parse(consentTemplate?.template || '{}'),
      },
    };
  } catch (error) {
    return {
      props: {
        member: [],
        consentData: [],
      },
    };
  }
}

export default ConsentFormPage;
