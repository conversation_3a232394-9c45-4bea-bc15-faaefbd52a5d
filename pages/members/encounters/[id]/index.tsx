/* eslint-disable no-underscore-dangle */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import EncounterTable from 'components/members/encounters/encounters-table/encounters-table';
import { encounterTableColumns } from 'components/members/encounters/encounters-table/columns-keys';
import { Encounter } from 'components/members/encounters/encounters-table/type';
import { Member } from 'components/members/members-table/type';
import { Button } from '@mui/material';
import AddIcon from '@mui/icons-material/Add'; // Import Add icon
import { useEffect, useState } from 'react';
import CreateEncounter from 'components/members/encounters/create-encounter';
import useAuth from 'hooks/use-auth';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import TabsView from 'components/tabs-view';
import CustomTable from 'components/table-components/table';
import router from 'next/router';

interface PageInfo {
  per: number;
  total: number;
  page: number;
}

interface MembersDetailProps {
  encounters: Encounter[];
  member: Member;
  surveyTypes: {
    title: string;
    key: string;
  }[];
  pageInfo: PageInfo;
  consents: any[];
}

function MemberEncounters({
  encounters,
  member,
  surveyTypes,
  pageInfo,
  consents,
}: MembersDetailProps) {
  const [open, setOpen] = useState(false);
  const { refresh } = useRefreshSSR();
  const { user } = useAuth();
  const [encounterType, setEncounterType] = useState('assessment');

  return (
    <MemberLayout
      memberId={member.id}
      active="encounters"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Encounters"
    >
      <div className="pl-10">
        <div className="w-full pt-3 pb-1 justify-between grid grid-flow-col auto-cols-max items-center mb-2">
          <p className="m-0 p-0 font-[600] text-lg">
            Encounters
            {` (${encounters.length + pageInfo.total})`}
          </p>
          <Button
            onClick={() => setOpen(true)}
            variant="contained"
            color="success"
            component="label"
            className="rounded-md m-0 p-0 h-[40px] float-right"
          >
            <AddIcon style={{ color: 'white' }} />
            {encounterType === 'assessment' ? 'New Assessment' : 'New Consent'}
          </Button>
        </div>
        <TabsView
          tabs={[
            {
              key: 'assessment',
              title: `Assessments (${encounters.length})`,
            },
            {
              key: 'consent',
              title: `Consents (${pageInfo.total})`,
            },
          ]}
          className="grid grid-flow-col auto-cols-max gap-0 mb-5"
          value={encounterType}
          callback={setEncounterType}
        />

        {encounterType === 'assessment' && (
          <EncounterTable
            EncounterData={encounters}
            tableHeaders={encounterTableColumns}
          />
        )}
        {encounterType === 'consent' && (
          <CustomTable
            source={consents}
            variant="consents-table"
            pageInfo={pageInfo}
            // sortBy={sortBy as string}
            // sortDirection={sortDirection as 'asc' | 'desc'}
            style={{
              border: 'solid 1px lightgray',
              borderRadius: '10px',
              borderBottom: 'none',
            }}
            headCellStyle={{
              padding: '4px 16px',
              fontSize: '13px',
              color: '#747A7A',
            }}
            tableCellStyle={{ padding: '2px 16px', fontSize: '15px' }}
            onPageChange={(newPage) => {
              router.push({
                pathname: '/members/encounters/[id]',
                query: {
                  ...router.query,
                  page: newPage,
                },
              });
            }}
            onSortChange={(newSortBy, newDirection) => {
              router.push({
                pathname: '/members/encounters/[id]',
                query: {
                  ...router.query,
                  page: 1,
                  sortBy: newSortBy,
                  sortDirection: newDirection,
                },
              });
            }}
          />
        )}
        <CreateEncounter
          memberId={member?.id}
          orgId={user?.org.id as string}
          navigatorId={user?.id as string}
          surveyTypes={surveyTypes}
          open={open}
          refresh={refresh}
          setOpen={setOpen}
          type={encounterType}
        />
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req, query } = context;
    let token;
    let orgId;
    const memberId: string = context.query.id as string;
    const page = parseInt((query.page as string) || '1', 10);
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
    }
    const _member = API.MEMBERS.fetchMember(memberId, token, orgId);
    const _surveyTypes = API.MEMBERS.fetchSurveyTypes(token, orgId);
    // eslint-disable-next-line max-len
    const _encounters: any = API.MEMBERS.fetchMemberEncounters(
      memberId,
      undefined,
      undefined,
      undefined,
      token,
      orgId,
    );
    const _consents: any = API.MEMBERS.fetchMemberConsents(
      memberId,
      token,
      page,
      10,
    );

    const [member, surveyTypes, encounters, consents] = await Promise.all([
      _member,
      _surveyTypes,
      _encounters,
      _consents,
    ]);

    return {
      props: {
        encounters: encounters?.items || [],
        member: member || [],
        surveyTypes: surveyTypes || [],
        consents: consents.items || [],
        pageInfo: consents.metadata || { per: 0, total: 0, page: 0 },
      },
    };
  } catch (error) {
    return {
      props: {
        encounters: [],
        member: [],
        surveyTypes: [],
        consents: [],
        pageInfo: { per: 0, total: 0, page: 0 },
      },
    };
  }
}

export default MemberEncounters;
