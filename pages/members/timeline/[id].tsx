import API from 'api/src/lib/api';
import TimelineFilter from 'components/members/member-status-widget/filter';
import TimeLineItems from 'components/members/member-status-widget/items';
import { parse } from 'cookie';
import MemberLayout from 'layouts/member-details-layout';
import { GetServerSidePropsContext } from 'next';
import { Member } from 'types/members-response-type';

interface Props {
  timelines: any;
  member: Member;
}
function TimelineHistory({ timelines, member }: Props) {
  return (
    <MemberLayout
      memberId={member.id}
      active="timeline"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Timeline"
    >
      <div className="ml-10">
        <div className="flex flex-row justify-between items-center mt-5">
          <p className="text-lg font-semibold m-0 p-0">Timeline History</p>
          {/* <TimelineFilter /> */}
        </div>
        <TimeLineItems initialData={timelines || []} memberId={member.id} />
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    // let orgId;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
    }
    const page = 1;
    const per = 30;
    const timelines: any = await API.MEMBERS.fetchMemberTimeLines(
      memberId,
      token,
      `per=${per}&page=${page}`,
    );

    const member = await API.MEMBERS.fetchMember(memberId, token);
    return {
      props: {
        member: member || [],
        timelines: timelines || [],
      },
    };
  } catch (error) {
    return {
      props: {
        member: [],
        timelines: [],
      },
    };
  }
}

export default TimelineHistory;
