import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API, { TOKEN } from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { Button } from '@mui/material';
import CustomTable from 'components/table-components/table';
import { useRouter } from 'next/router';
import { Member } from 'types/members-response-type';
import { useMemo, useState } from 'react';
import useRefreshSSR from 'hooks/use-refresh-ssr';

interface CarePlansProps {
  member: Member;
  carePlans: any[];
}

function CarePlans({ member, carePlans = [] }: CarePlansProps) {
  const router = useRouter();
  const { refresh } = useRefreshSSR();
  const [sortBy, setSortBy] = useState<string>('createdAt'); // or whatever column you want by default
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('desc');
  const sortedCarePlans = useMemo(() => {
    const sorted = [...carePlans].sort((a, b) => {
      const aVal = a[sortBy];
      const bVal = b[sortBy];

      if (aVal == null) return 1;
      if (bVal == null) return -1;

      if (typeof aVal === 'string' && typeof bVal === 'string') {
        return sortDirection === 'asc'
          ? aVal.localeCompare(bVal)
          : bVal.localeCompare(aVal);
      }

      if (aVal instanceof Date && bVal instanceof Date) {
        return sortDirection === 'asc'
          ? aVal.getTime() - bVal.getTime()
          : bVal.getTime() - aVal.getTime();
      }

      // fallback for numbers or other types
      return sortDirection === 'asc' ? aVal - bVal : bVal - aVal;
    });

    return sorted;
  }, [carePlans, sortBy, sortDirection]);
  const [page, setPage] = useState(1);
  const perPage = 10;
  const paginatedCarePlans = useMemo(() => {
    const start = (page - 1) * perPage;
    return sortedCarePlans.slice(start, start + perPage);
  }, [sortedCarePlans, page]);

  const handleDelete = async (carePlanId: string) => {
    await API.CAREPLANS.deleteCarePlan(carePlanId, TOKEN());
    await refresh();
  };

  return (
    <MemberLayout
      memberId={member.id}
      active="care-plans"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Care Plans"
    >
      <div className="w-full pl-10">
        <div className="flex items-center justify-between mt-1 mb-4">
          <p className="font-[500] text-[18px] m-0">
            Care Plans (
            {sortedCarePlans.length}
            )
          </p>
          <Button
            variant="contained"
            onClick={() => router.push(`/members/care-plans/${member.id}/new`)}
            color="success"
            className="rounded-md"
          >
            + New Care Plan
          </Button>
        </div>
        {sortedCarePlans.length > 0 ? (
          <div className="mt-3">
            <CustomTable
              source={paginatedCarePlans}
              variant="care-plans-table"
              fixedLayout
              style={{
                border: 'solid 1px lightgray',
                borderRadius: '10px',
                borderBottom: 'none',
              }}
              headCellStyle={{
                padding: '4px 16px',
                fontSize: '13px',
                color: '#747A7A',
              }}
              tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
              handleDelete={handleDelete}
              pageInfo={{
                page,
                per: perPage,
                total: carePlans.length,
              }}
              onPageChange={setPage}
              sortBy={sortBy}
              sortDirection={sortDirection}
              onSortChange={(key, direction) => {
                setSortBy(key);
                setSortDirection(direction);
                setPage(1);
              }}
              firstColumnStaticWidth={25}
              secondColumnStaticWidth={15}
              thirdColumnStaticWidth={20}
              fourthColumnStaticWidth={10}
              fifthColumnStaticWidth={10}
            />
          </div>
        ) : (
          <p className="text-[14px] font-[400] italic text-[#262D2D] p-0 m-0">
            No care plans found for member.
          </p>
        )}
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;

    let token;
    let org;
    const memberId: string = context.query.id as string;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }

    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    const carePlans: any = await API.CAREPLANS.fetchMemberCarePlans(
      memberId,
      token,
      org,
    );

    return {
      props: {
        carePlans: carePlans.carePlans || [],
        member: member || {},
      },
    };
  } catch (error) {
    return {
      props: {
        carePlans: [],
        member: {},
      },
    };
  }
}

export default CarePlans;
