/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API, { TOKEN } from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { Button, TextField } from '@mui/material';
import { useRouter } from 'next/router';
import { Member } from 'types/members-response-type';
import Image from 'next/image';
import CheckIcon from '@mui/icons-material/Check';
import { useEffect, useState } from 'react';
import DateSelector from 'components/date-selector/date-selector';
import dayjs from 'dayjs';
import AddRow from 'components/members/care-plans/AddRow';
import AddModal from 'components/members/care-plans/AddModal';
import useAuth from 'hooks/use-auth';
import SelectTeams from 'components/teams/select-teams/select-teams';
import { Teams } from 'types/teams-types';
import { getLatestImage } from 'utils/helper';

interface NewCarePlansProps {
  member: Member;
}

const sectionKeys = [
  'goals',
  'interventions',
  'team',
  'reviews',
  'follow-ups',
  'network-appointments',
  'notes',
  'problems',
];

function formatTitle(key: string) {
  if (key === 'follow-ups') {
    return 'Follow-ups';
  }
  return key.replace(/-/g, ' ').replace(/\b\w/g, (char) => char.toUpperCase());
}

function NewCarePlan({ member }: NewCarePlansProps) {
  const router = useRouter();
  const [carePlan, setCarePlan] = useState<any>(null);
  const { user } = useAuth();
  const [title, setTitle] = useState('');
  const [startDate, setStartDate] = useState('');
  const [carePlanSections, setCarePlanSections] = useState<
    Record<string, any[]>
  >(sectionKeys.reduce((acc, key) => ({ ...acc, [key]: [] }), {}));
  const [openModalType, setOpenModalType] = useState<string | null>(null);
  const [openTeamModal, setOpenTeamModal] = useState(false);
  const [selectedTeams, setSelectedTeams] = useState<Teams[]>([]);

  const createCarePlanDraft = async (planTitle?: string, date?: string) => {
    const data = {
      title: planTitle || 'New Care Plan',
      startDate: date || dayjs().format('YYYY-MM-DD HH:mm:ss'),
      status: 'draft',
    };
    const response = await API.CAREPLANS.createCarePlan(
      member.id,
      data,
      TOKEN() || '',
    );
    setCarePlan(response);
  };

  useEffect(() => {
    createCarePlanDraft();
  }, []);

  function toISOStringWithoutMillis(date: Date) {
    return date.toISOString().replace(/\.\d{3}Z$/, 'Z');
  }

  const getUrlType = (type: string) => {
    let urlType = type;
    if (type === 'team') {
      urlType = 'team-members/from-team';
    } else if (type === 'follow-ups') {
      urlType = 'followups';
    } else if (type === 'network-appointments') {
      urlType = 'services';
    }
    return urlType;
  };

  const handleAddItem = async (type: string, fields: any) => {
    const updatedFields = {
      ...fields,
      ...(type === 'reviews' && {
        author: `${user?.firstName} ${user?.lastName}`,
        authorProfile: getLatestImage(user?.attachments as any, 'userProfile'),
      }),
      ...(type === 'follow-ups' && {
        by: `${user?.firstName} ${user?.lastName}`,
        authorProfile: getLatestImage(user?.attachments as any, 'userProfile'),
      }),
      ...(type === 'notes' && {
        author: `${user?.firstName} ${user?.lastName}`,
        authorProfile: getLatestImage(user?.attachments as any, 'userProfile'),
        date: dayjs().format('YYYY-MM-DD'),
      }),
      ...(type === 'problems' && {
        confirmedBy: `${user?.firstName} ${user?.lastName}`,
        authorProfile: getLatestImage(user?.attachments as any, 'userProfile'),
        dateIdentified: dayjs().format('YYYY-MM-DD'),
      }),
    };

    const createModel = () => {
      if (type === 'goals') {
        return {
          title: updatedFields.title,
          targetDate: toISOStringWithoutMillis(
            new Date(updatedFields?.targetDate),
          ),
          measurementCriteria: updatedFields.measurementCriteria,
          description: updatedFields.description || 'No description provided',
          type: updatedFields.goalType.key,
          status: 'active',
          outcome: null,
          objective: updatedFields.goalObjective,
          achievabilityNote: updatedFields.achievabilityNote,
          barriers: updatedFields.barriers,
        };
      }
      if (type === 'interventions') {
        return {
          title: updatedFields.title,
          action: updatedFields.goal.title,
          responsiblePartyId: updatedFields.responsibleParty.key,
          responsibleParty: updatedFields.responsibleParty.title,
          dueDate: toISOStringWithoutMillis(new Date(updatedFields?.dueDate)),
          autoAssignTask: updatedFields.autoAssignAsTask.key === 'yes',
          status: 'active',
          note: updatedFields.note,
        };
      }
      if (type === 'reviews') {
        return {
          title: updatedFields.title,
          reviewDate: toISOStringWithoutMillis(new Date(updatedFields?.date)),
          notes: updatedFields.note,
          reviewerName: `${user?.firstName} ${user?.lastName}`,
          reviewerRole:
            user?.roles[0] === 'drop_all' ? user?.roles[1] : user?.roles[0],
        };
      }
      if (type === 'follow-ups') {
        return {
          datetime: toISOStringWithoutMillis(new Date(updatedFields?.date)),
          type: updatedFields?.type?.key,
          outcome: updatedFields?.outcome?.key,
          notes: updatedFields.note,
          responsibleParty: updatedFields.responsibleParty,
          action: updatedFields.action,
          staffName: `${user?.firstName} ${user?.lastName}`,
          staffRole: user?.roles[0],
        };
      }
      if (type === 'notes') {
        return {
          title: updatedFields.title,
          type: updatedFields?.type?.key,
          subtitle: updatedFields.subtitle,
          msg: updatedFields.note,
        };
      }
      if (type === 'problems') {
        return {
          title: updatedFields.title,
          icdCode: updatedFields.icdCode,
          description:
            updatedFields.description || updatedFields.clinicalNote || '',
          dateIdentified: toISOStringWithoutMillis(
            new Date(updatedFields?.dateIdentified),
          ),
          source: updatedFields.source,
          confirmedBy: updatedFields.confirmedBy,
          clinicalNote: updatedFields.clinicalNote,
          status: 'active',
          type: updatedFields.type?.title,
        };
      }
      if (type === 'team') {
        return {
          teamID: updatedFields.team[0].id,
        };
      }
      if (type === 'network-appointments') {
        return {
          cboName: updatedFields.networkName,
          staffName: updatedFields.providerName || '-',
          addedBy: `${user?.firstName} ${user?.lastName}`,
          status: 'pending',
          appointmentDate: toISOStringWithoutMillis(
            new Date(updatedFields?.appointmentDateTime),
          ),
          outcomeReasonType: null,
          outcomeReasonDescription: null,
          appointmentNote: updatedFields.appointmentNote,
        };
      }
      return null;
    };

    const model = createModel();

    const createCarePlanSection = async (
      sectionType: string,
      sectionFields: any,
    ) => {
      const response = await API.CAREPLANS.createCarePlanSection(
        carePlan.id,
        sectionType,
        sectionFields,
        TOKEN() || '',
      );
      return response;
    };

    const response: any = await createCarePlanSection(getUrlType(type), model);
    if (response?.id) {
      const updatedFieldsWithId = {
        ...updatedFields,
        id: response.id,
      };
      setCarePlanSections((prev) => ({
        ...prev,
        [type]: [...(prev[type] || []), updatedFieldsWithId],
      }));
    }
  };

  async function cancelDraft() {
    // eslint-disable-next-line no-unused-vars
    const response = await API.CAREPLANS.cancelCarePlanDraft(
      carePlan.id,
      TOKEN() || '',
    );
    router.push(`/members/care-plans/${member.id}`);
  }

  async function handleSaveAsDraft() {
    const data = {
      title: title || 'New Care Plan',
      startDate: startDate
        ? toISOStringWithoutMillis(new Date(startDate))
        : toISOStringWithoutMillis(new Date()),
      status: 'draft',
    };
    // eslint-disable-next-line no-unused-vars
    const response = await API.CAREPLANS.updateCarePlan(
      carePlan.id,
      data,
      TOKEN() || '',
    );
    router.push(`/members/care-plans/${member.id}`);
  }

  async function handlePublishCarePlan() {
    const data = {
      title: title || 'New Care Plan',
      startDate: startDate
        ? toISOStringWithoutMillis(new Date(startDate))
        : toISOStringWithoutMillis(new Date()),
      status: 'active',
    };
    // eslint-disable-next-line no-unused-vars
    const response = await API.CAREPLANS.updateCarePlan(
      carePlan.id,
      data,
      TOKEN() || '',
    );
    router.push(`/members/care-plans/${member.id}`);
  }

  const handleOpenModal = (type: string) => {
    if (type === 'team') {
      setOpenTeamModal(true);
    } else {
      setOpenModalType(type);
    }
  };
  const handleDelete = async (id: any, type: string) => {
    try {
      const response = await API.CAREPLANS.deleteCarePlanSection(
        carePlan.id,
        type === 'team' ? 'team-members' : getUrlType(type) || type,
        id.toUpperCase(),
        TOKEN() || '',
      );

      if (response.status === 204) {
        setCarePlanSections((prev) => ({
          ...prev,
          [type]: prev[type].filter(
            (item: any) => String(item.id).toLowerCase() !== String(id).toLowerCase(),
          ),
        }));
      }
    } catch (error) {
      console.error('Delete error:', error);
    }
  };

  useEffect(() => {
    setCarePlanSections((prev) => ({
      ...prev,
      team: selectedTeams,
    }));
  }, [selectedTeams]);

  return (
    <MemberLayout
      memberId={member.id}
      active="care-plans"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Care Plans"
      rightSide={(
        <div className="flex flex-row gap-2 items-center">
          <Button
            onClick={() => cancelDraft()}
            className="rounded-md m-0 text-[15px] text-[#262D2D] grey-btn"
          >
            Cancel
          </Button>
          <Button
            onClick={() => handleSaveAsDraft()}
            color="success"
            className="rounded-md m-0 text-[15px] text-[#008390] grey-btn"
          >
            <Image
              src="/icons/icon-save.svg"
              alt="save as draft"
              width={16}
              height={16}
              className="mr-2"
            />
            Save as Draft
          </Button>
          <Button
            variant="contained"
            onClick={() => handlePublishCarePlan()}
            color="success"
            className="rounded-md"
          >
            <CheckIcon
              className="mr-[6px]"
              style={{ width: '20px', height: '20px' }}
            />
            Save & Publish
          </Button>
        </div>
      )}
    >
      <div className="w-full pl-10 pb-10">
        <div className="flex items-center mt-2 mb-4">
          <p className="font-[500] text-[18px] m-0">Plan Details</p>
        </div>
        <div className="flex flex-wrap w-full mt-3 gap-x-4 gap-y-4">
          <TextField
            name="title"
            placeholder="Enter Title"
            value={title}
            id="filled-basic"
            label="Title"
            variant="filled"
            onChange={(e) => setTitle(e.target.value)}
            className="md-lg:w-[30%] min-w-[280px] w-[48%]"
          />
          <div className="md-lg:w-[30%] min-w-[280px] w-[48%]">
            <DateSelector
              name="coverageStartDate"
              defaultValue={startDate}
              pickerProps={{
                label: startDate ? 'Start date' : 'Select start date',
                slotProps: {
                  field: {
                    clearable: true,
                    onClear: () => setStartDate(''),
                  },
                },
              }}
              onChange={(e) => {
                const date = dayjs(e.target.value).format('YYYY-MM-DD');
                setStartDate(date);
              }}
            />
          </div>
        </div>

        {/* Dynamically render all AddRow sections */}
        {sectionKeys.map((key) => (
          <AddRow
            key={key}
            title={formatTitle(key)}
            items={carePlanSections[key]}
            setOpenAddItem={() => handleOpenModal(key)}
            name={key}
            handleDelete={(id) => handleDelete(id, key)}
          />
        ))}
      </div>

      {/* Dynamic Modal */}
      {openModalType && (
        <AddModal
          open={!!openModalType}
          setOpen={() => setOpenModalType(null)}
          handleAdd={handleAddItem}
          type={openModalType}
          carePlanSections={carePlanSections}
        />
      )}
      <SelectTeams
        selectedTeams={selectedTeams}
        setSelectTeams={setSelectedTeams}
        open={openTeamModal}
        setOpen={setOpenTeamModal}
        newCarePlan
        handleAdd={handleAddItem}
      />
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    const memberId: string = context.query.id as string;

    let token;
    let org;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }

    const member = await API.MEMBERS.fetchMember(memberId, token, org);

    return {
      props: {
        member: member || {},
      },
    };
  } catch (error) {
    return {
      props: {
        member: {},
      },
    };
  }
}

export default NewCarePlan;
