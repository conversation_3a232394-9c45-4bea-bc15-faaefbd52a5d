/* eslint-disable max-len */
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API, { TOKEN } from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { useRouter } from 'next/router';
import { Member } from 'types/members-response-type';
import { useEffect, useState } from 'react';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import ViewCarePlanHeader from 'components/members/care-plans/view/ViewCarePlanHeader';
import ViewCarePlanSection from 'components/members/care-plans/view/ViewCarePlanSection';
import AddModal from 'components/members/care-plans/AddModal';
import useAuth from 'hooks/use-auth';
import { getLatestImage } from 'utils/helper';
import dayjs from 'dayjs';
import CarePlanTimeline from 'components/members/care-plans/view/CarePlanTimeline';
import { TimeLineResponse } from 'types/timeline-types';

interface CarePlansProps {
  member: Member;
  carePlan: any;
  team: any;
  carePlanSections: any;
  timelines: TimeLineResponse[];
}

function ViewCarePlan({
  member,
  carePlan = {},
  team = {},
  carePlanSections = {},
  timelines = [],
}: CarePlansProps) {
  const router = useRouter();
  const { refresh } = useRefreshSSR();
  const [tab, setTab] = useState('status');
  const [openModalType, setOpenModalType] = useState<string | null>(null);
  const [openTeamModal, setOpenTeamModal] = useState(false);
  const { user } = useAuth();
  const [carePlanSectionsNew, setCarePlanSectionsNew] = useState<any>(carePlanSections);

  const handleOpenModal = (type: string) => {
    if (type === 'team') {
      setOpenTeamModal(true);
    } else if (type === 'followups') {
      setOpenModalType('follow-ups');
    } else if (type === 'services') {
      setOpenModalType('network-appointments');
    } else {
      setOpenModalType(type);
    }
  };

  function toISOStringWithoutMillis(date: Date) {
    return date.toISOString().replace(/\.\d{3}Z$/, 'Z');
  }

  const getUrlType = (type: string) => {
    let urlType = type;
    if (type === 'team') {
      urlType = 'team-members/from-team';
    } else if (type === 'follow-ups') {
      urlType = 'followups';
    } else if (type === 'network-appointments') {
      urlType = 'services';
    }
    return urlType;
  };

  const handleAddItem = async (type: string, fields: any) => {
    const updatedFields = {
      ...fields,
      ...(type === 'reviews' && {
        author: `${user?.firstName} ${user?.lastName}`,
        authorProfile: getLatestImage(user?.attachments as any, 'userProfile'),
      }),
      ...(type === 'follow-ups' && {
        by: `${user?.firstName} ${user?.lastName}`,
        authorProfile: getLatestImage(user?.attachments as any, 'userProfile'),
      }),
      ...(type === 'notes' && {
        author: `${user?.firstName} ${user?.lastName}`,
        authorProfile: getLatestImage(user?.attachments as any, 'userProfile'),
        date: dayjs().format('YYYY-MM-DD'),
      }),
      ...(type === 'problems' && {
        confirmedBy: `${user?.firstName} ${user?.lastName}`,
        authorProfile: getLatestImage(user?.attachments as any, 'userProfile'),
      }),
    };

    const createModel = () => {      
      if (type === 'goals') {
        return {
          title: updatedFields.title,
          targetDate: toISOStringWithoutMillis(
            new Date(updatedFields?.targetDate),
          ),
          measurementCriteria: updatedFields.measurementCriteria,
          description: updatedFields.description || 'No description provided',
          type: updatedFields.goalType.key,
          status: 'active',
          outcome: null,
          objective: updatedFields.goalObjective,
          achievabilityNote: updatedFields.achievabilityNote,
          barriers: updatedFields.barriers,
        };
      }
      if (type === 'interventions') {
        return {
          title: updatedFields.title,
          action: updatedFields.goal.title,
          responsiblePartyId: updatedFields.responsibleParty.key,
          responsibleParty: updatedFields.responsibleParty.title,
          dueDate: toISOStringWithoutMillis(new Date(updatedFields?.dueDate)),
          autoAssignTask: updatedFields.autoAssignAsTask.key === 'yes',
          status: 'active',
          note: updatedFields.note,
        };
      }
      if (type === 'reviews') {
        return {
          title: updatedFields.title,
          reviewDate: toISOStringWithoutMillis(new Date(updatedFields?.date)),
          notes: updatedFields.note,
          reviewerName: `${user?.firstName} ${user?.lastName}`,
          reviewerRole:
            user?.roles[0] === 'drop_all' ? user?.roles[1] : user?.roles[0],
        };
      }
      if (type === 'follow-ups') {
        return {
          datetime: toISOStringWithoutMillis(new Date(updatedFields?.date)),
          type: updatedFields?.type?.key,
          outcome: updatedFields?.outcome?.key,
          notes: updatedFields.note,
          responsibleParty: updatedFields.responsibleParty,
          action: updatedFields.action,
          staffName: `${user?.firstName} ${user?.lastName}`,
          staffRole: user?.roles[0],
        };
      }
      if (type === 'notes') {
        return {
          title: updatedFields.title,
          type: updatedFields?.type?.key,
          subtitle: updatedFields.subtitle,
          msg: updatedFields.note,
        };
      }
      if (type === 'problems') {
        return {
          title: updatedFields.title,
          icdCode: updatedFields.icdCode,
          description:
            updatedFields.description || updatedFields.clinicalNote || '',
          dateIdentified: updatedFields?.dateIdentified
            ? toISOStringWithoutMillis(new Date(updatedFields?.dateIdentified))
            : toISOStringWithoutMillis(new Date()),
          source: updatedFields.source,
          confirmedBy: updatedFields.confirmedBy,
          clinicalNote: updatedFields.clinicalNote,
          status: 'active',
          type: updatedFields.type,
        };
      }
      if (type === 'team') {
        return {
          teamID: updatedFields.team[0].id,
        };
      }
      if (type === 'network-appointments') {
        return {
          cboName: updatedFields.networkName,
          staffName: updatedFields.providerName || '-',
          addedBy: `${user?.firstName} ${user?.lastName}`,
          status: 'pending',
          appointmentDate: toISOStringWithoutMillis(
            new Date(updatedFields?.appointmentDateTime),
          ),
          outcomeReasonType: null,
          outcomeReasonDescription: null,
          appointmentNote: updatedFields.appointmentNote,
        };
      }
      return null;
    };

    const model = createModel();

    const createCarePlanSection = async (
      sectionType: string,
      sectionFields: any,
    ) => {
      const response = await API.CAREPLANS.createCarePlanSection(
        carePlan.id,
        sectionType,
        sectionFields,
        TOKEN() || '',
      );
      return response;
    };

    const response: any = await createCarePlanSection(getUrlType(type), model);
    if (response?.id) {
      const updatedFieldsWithId = { ...updatedFields, id: response.id };
      let finalUpdatedFields = updatedFieldsWithId;
      if (type === 'goals') {
        finalUpdatedFields = {
          ...updatedFieldsWithId,
          status: response.status,
          type: response.type,
        };
      }
      if (type === 'interventions') {
        finalUpdatedFields = {
          ...updatedFieldsWithId,
          action: response.action,
        };
      }
      if (type === 'reviews') {
        finalUpdatedFields = {
          ...updatedFieldsWithId,
          reviewerName: response.reviewerName,
          reviewerRole: response.reviewerRole,
          reviewDate: response.reviewDate,
          notes: response.notes,
        };
      }
      if (type === 'follow-ups') {
        finalUpdatedFields = {
          ...updatedFieldsWithId,
          staffName: response.staffName,
          staffRole: response.staffRole,
          datetime: response.datetime,
          type: response.type,
        };
      }
      if (type === 'network-appointments') {
        finalUpdatedFields = {
          ...updatedFieldsWithId,
          cboName: response.cboName,
          staffName: response.staffName,
          appointmentDate: response.appointmentDate,
          status: response.status,
          appointmentNote: response.appointmentNote,
        };
      }
      if (type === 'notes') {
        finalUpdatedFields = {
          ...updatedFieldsWithId,
          creator: { id: user?.id },
          createdAt: response.createdAt,
          msg: response.msg,
        };
      }

      if (type === 'follow-ups') {
        setCarePlanSectionsNew((prev: any) => ({
          ...prev,
          followups: [...(prev.followups || []), finalUpdatedFields],
        }));
      } else if (type === 'network-appointments') {
        setCarePlanSectionsNew((prev: any) => ({
          ...prev,
          services: [...(prev.services || []), finalUpdatedFields],
        }));
      } else {
        setCarePlanSectionsNew((prev: any) => ({
          ...prev,
          [type]: [...(prev[type] || []), finalUpdatedFields],
        }));
      }
    }
  };

  const handleClick = (type: string, id: string) => {
    router.push(
      `/members/care-plans/${member.id}/${carePlan.id}/${type}/${id}`,
    );
  };

  async function handleDelete(
    carePlanID: string,
    type: string,
    itemID: string,
  ) {
    try {
      await API.CAREPLANS.deleteCarePlanItem(carePlanID, type, itemID);
      setCarePlanSectionsNew((prev: any) => ({
        ...prev,
        [type]: prev[type].filter((item: any) => item.id !== itemID),
      }));
    } catch (err) {
      console.error('Failed to delete care plan item:', err);
    }
  }

  return (
    <MemberLayout
      memberId={member.id}
      active="care-plans"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Care Plans"
      updateUrl={`/members/care-plans/${member.id}`}
      add
      addLabel={carePlan.title}
      replaceTabs={[
        {
          key: 'status',
          title: 'Status',
        },
        { key: 'timeline', title: 'Timeline' },
      ]}
      replaceTab={tab}
      replaceTabClick={(key: any) => {
        setTab(key);
      }}
    >
      {tab === 'status' && (
        <div className="w-full pl-10 pb-[100px]">
          <ViewCarePlanHeader carePlan={carePlan} member={member} team={team} />
          {Object.entries(carePlanSectionsNew).map(([key, items]) => (
            <ViewCarePlanSection
              key={key}
              section={{ title: key, items }}
              setOpenAddItem={() => handleOpenModal(key)}
              handleClick={(type: string, id: string) => handleClick(type, id)}
              handleDelete={(carePlanID: string, type: string, id: string) => handleDelete(carePlanID, type, id)}
            />
          ))}
        </div>
      )}
      {tab === 'timeline' && (
        <div className="w-full pl-10 pb-[100px]">
          <CarePlanTimeline
            carePlan={carePlan}
            member={member}
            timelines={timelines}
          />
        </div>
      )}

      {openModalType && (
        <AddModal
          open={!!openModalType}
          setOpen={() => setOpenModalType(null)}
          handleAdd={handleAddItem}
          type={openModalType}
          carePlanSections={carePlanSectionsNew}
        />
      )}
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;

    let token: string | undefined;
    let org: string | undefined;
    const memberId: string = context.query.id as string;
    const carePlanId: string = context.query.cid as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }

    const carePlanSectionTitles = [
      'goals',
      'interventions',
      'reviews',
      'followups',
      'services',
      'notes',
      'problems',
    ];

    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    const carePlan: any = await API.CAREPLANS.getCarePlan(carePlanId, token);
    const timelines = await API.CAREPLANS.getCarePlanTimelineItems(
      carePlanId,
      token,
    );
    const team = await API.CAREPLANS.getCarePlanSection(
      carePlanId,
      'team-members',
      token,
    );
    const carePlanSectionsEntries = await Promise.all(
      carePlanSectionTitles.map(async (section) => {
        const items = await API.CAREPLANS.getCarePlanSection(
          carePlanId,
          section,
          token,
        );
        return [section, items];
      }),
    );

    const carePlanSections = Object.fromEntries(carePlanSectionsEntries);
    return {
      props: {
        carePlan: carePlan || {},
        member: member || {},
        team: team || {},
        carePlanSections: carePlanSections || {},
        timelines: timelines || [],
      },
    };
  } catch (error) {
    return {
      props: {
        carePlan: {},
        member: {},
        team: {},
        carePlanSections: {},
        timelines: [],
      },
    };
  }
}

export default ViewCarePlan;
