/* eslint-disable no-nested-ternary */
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { parse } from 'cookie';
import { GetServerSidePropsContext } from 'next';
import { capitalizeWords } from 'utils/helper';
import { useRouter } from 'next/router';
import { Button } from '@mui/material';
import { useModal } from 'contexts/modal-context/modal-context';
import CarePlanItemStatus from 'components/members/care-plans/view/item/CarePlanItemStatus';
import CarePlanItemDetails from 'components/members/care-plans/view/item/CarePlanItemDetails';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useContext } from 'react';

export default function ViewCarePlanItem({
  carePlanItem,
}: {
  carePlanItem: any;
}) {
  const constant = useContext(ConstantsContext);
  const { showModal } = useModal();
  const router = useRouter();
  const { type } = router.query;
  const tempType = type === 'followups' ? 'follow-up' : type === 'services' ? 'network appointment' : type;
  const typeTitle = capitalizeWords((tempType as string)?.replace(/s$/, '') || '', true);
  return (
    <MemberLayout
      memberId={carePlanItem?.carePlan?.member?.id}
      active="care-plans"
      fullName={`${carePlanItem?.carePlan?.member?.firstName} ${carePlanItem?.carePlan?.member?.lastName}`}
      updateLabel={
        carePlanItem?.carePlan?.title
        || `${capitalizeWords(carePlanItem?.carePlan?.member?.firstName)}'s Care Plan`
      }
      updateUrl={`/members/care-plans/${carePlanItem?.carePlan?.member?.id}/view/${carePlanItem?.carePlan?.id}`}
      add
      addLabel={`${typeTitle} (${type === 'followups' ? constant?.followUpTypes.find((g: any) => g.key === carePlanItem?.type)
        ?.title : type === 'services' ? carePlanItem?.cboName : capitalizeWords(carePlanItem?.title)})`}
      borderBottom
      rightSide={(
        <Button
          component="label"
          className="rounded-md m-0 text-[15px] text-[#E42B57] grey-btn"
          sx={{ padding: '11px 15px !important' }}
          onClick={() => {
            showModal(
              'Warning',
              'Are you sure to delete this item?',
              async () => {
                await API.CAREPLANS.deleteCarePlanItem(
                  carePlanItem?.carePlan?.id,
                  type as string,
                  carePlanItem.id as string,
                );
                router.replace(
                  `/members/care-plans/${carePlanItem?.carePlan?.member?.id}/view/${carePlanItem?.carePlan?.id}`,
                );
              },
            );
          }}
        >
          Delete
        </Button>
      )}
    >
      <div className="w-full pl-10 pb-[100px]">
        {type === 'goals' || type === 'interventions' ? (
          <CarePlanItemStatus
            type={type as string}
            status={carePlanItem?.status}
          />
        ) : null}
        <CarePlanItemDetails
          type={type as string}
          item={carePlanItem}
        />
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;

    let token: string | undefined;
    let org: string | undefined;
    const carePlanId: string = context.query.cid as string;
    const type: string = context.query.type as string;
    const carePlanItemId: string = context.query.itemId as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }

    const carePlanItem: any = await API.CAREPLANS.getCarePlanItem(
      carePlanId,
      type,
      carePlanItemId,
      token,
    );

    return {
      props: {
        carePlanItem: carePlanItem || {},
      },
    };
  } catch (error) {
    return {
      props: {
        carePlanItem: {},
      },
    };
  }
}
