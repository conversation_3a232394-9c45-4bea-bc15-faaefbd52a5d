/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { Avatar } from '@mui/material';
import API from 'api/src/lib/api';
import MarkTaskComplete from 'components/tasks/commons/mark-complete';
import { parse } from 'cookie';
import useAuth from 'hooks/use-auth';
import MemberLayout from 'layouts/member-details-layout';
import { GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { TaskObject } from 'types/tasks-response-type';
import { useModal } from 'contexts/modal-context/modal-context';
import {
  addressFormatter,
  capitalizeWords,
  getLatestImage,
} from 'utils/helper';
import SafeDate from 'components/date-time/SafeDate';

interface MemberTaskProps {
  member: any;
  task: TaskObject;
}

function MemberTaskDetail({ member, task }: MemberTaskProps) {
  const [open, setOpen] = useState(false);
  const { user } = useAuth();
  const router = useRouter();
  const { showModal } = useModal();
  const handleMarkComplete = async (values: object) => {
    const model = {
      orgID: user?.org?.id,
      status: 'completed',
      reason: {
        ...values,
        creatorID: user?.id,
      },
    };
    await API.TASKS.markTaskAsCompleted(task.id, model);
    await router.replace(`/members/tasks/${member.id}`);
  };
  const handleOpen = async () => {
    if (task.status === 'completed') {
      await API.TASKS.updateStatus(task.id, 'archived');
      await router.replace('/tasks');
    } else {
      setOpen(true);
    }
  };

  const handleDeleteTask = () => {
    showModal(
      'Warning',
      'This task will be deleted permanently. Are you sure you want to delete this task?',
      async () => {
        await API.TASKS.deleteTask(task.id);
        await router.push(`/members/tasks/${member.id}`);
      },
    );
  };

  return (
    <MemberLayout
      memberId={member.id}
      active="tasks"
      fullName={`${member?.firstName} ${member?.lastName}`}
      addLabel={capitalizeWords(task.title, true)}
      add
      updateLabel="Tasks"
      updateUrl={`/members/tasks/${member.id}`}
      rightSide={(
        <div className="flex items-center gap-3">
          <div
            onClick={async () => {
              handleDeleteTask();
            }}
            className="bg-[#F7F8F8] p-3 cursor-pointer rounded-md"
          >
            <p className="m-0 text-[#E42B57] font-semibold text-sm">
              Delete Task
            </p>
          </div>
          {task.status !== 'completed' && (
            <div
              onClick={() => {
                router.push(`/members/tasks/${member.id}/task/${task.id}/edit`);
              }}
              className="bg-[#F7F8F8] bg-icon-edit bg-left bg-no-repeat bg-origin-content pl-2 p-3 cursor-pointer rounded-md"
            >
              <p className="m-0 text-primary font-semibold text-sm pl-6">
                Edit Task
              </p>
            </div>
          )}

          <div
            onClick={handleOpen}
            className="bg-[#008390] min-w-[163px] bg-icon-check-white bg-no-repeat bg-origin-content bg-left p-3 cursor-pointer rounded-md grid grid-flow-row items-center justify-center"
          >
            <p className="m-0 text-white font-semibold text-sm pl-6">
              {task.status !== 'completed' ? 'Mark Complete' : 'Archive Task'}
            </p>
          </div>
        </div>
      )}
    >
      <div className="pl-10">
        <p className="font-semibold text-md">Member Details</p>
        <div className="grid grid-flow-col auto-cols-max gap-3 items-center">
          <Avatar
            src={
              task.receivers.length > 0
                ? getLatestImage(task.receivers[0].attachments, 'profile')
                : ''
            }
          />
          <p className="font-semibold text-sm text-dark-text">
            {task.receivers.length > 0
              ? `${capitalizeWords(
                `${task.receivers[0].firstName} ${task.receivers[0].lastName}`,
                true,
              )}`
              : ''}
          </p>
        </div>
        <p className="font-semibold text-md">Task Details</p>
        <div className="grid grid-cols-3 auto-cols-max gap-3 items-center">
          <div className="grid grid-flow-row auto-cols-max">
            <p className="m-0 font-normal text-xs text-grey">Task Type</p>
            <p className="m-0 font-semibold text-sm text-dark-text">
              {capitalizeWords(task.type, true)}
            </p>
          </div>
          <div className="grid grid-flow-row auto-cols-max">
            <p className="m-0 font-normal text-xs text-grey">Type</p>
            <p className="m-0 font-semibold text-sm text-dark-text">
              {capitalizeWords(task.taskDetail?.type.replace(/_/g, ' '), true)}
            </p>
          </div>
          <div className="grid grid-flow-row auto-cols-max">
            <p className="m-0 font-normal text-xs text-grey">Location Type</p>
            <p className="m-0 font-semibold text-sm text-dark-text">
              {task.remote ? 'Remote' : 'Location Based'}
            </p>
            <p className="m-0 font-normal text-sm text-dark-text">
              {task.remote
                ? task.taskDetail?.meta?.data?.remote
                : addressFormatter(task.location)}
            </p>
          </div>
          <div className="grid grid-flow-row auto-cols-max col-span-3">
            <p className="m-0 font-normal text-xs text-grey">Date Type</p>
            <p className="m-0 font-semibold text-sm text-dark-text">
              {capitalizeWords(
                task.taskDetail.meta?.data?.dateType?.replace(/_/g, ' '),
                true,
              )}
              {' '}
              {task.taskDetail.meta?.data?.dateType && '-'}
              {' '}
              <span className="font-normal">
                <SafeDate date={new Date(task.dueAtEpoc * 1000).toISOString()} time={task.taskDetail.meta?.data?.dateType === 'datetime'} />
              </span>
            </p>
          </div>
          <div className="grid grid-flow-row auto-cols-auto col-span-3">
            <p className="m-0 font-normal text-xs text-grey">Description</p>
            <p className="m-0 font-normal text-sm text-dark-text">
              {task.desc}
            </p>
          </div>
        </div>
        <p className="font-semibold text-md">Completed By</p>
        {task.completedBy.map((completed) => (
          <div className="grid grid-flow-col auto-cols-max gap-3 items-center">
            <Avatar src={completed.profile} />
            <p className="font-semibold text-sm text-dark-text">
              {`${capitalizeWords(
                `${completed.firstName} ${completed.lastName}`,
                true,
              )}`}
            </p>
          </div>
        ))}
      </div>
      <MarkTaskComplete
        task={task}
        open={open}
        setOpen={setOpen}
        onComplete={handleMarkComplete}
      />
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let orgId;
    // eslint-disable-next-line prefer-destructuring
    const { id: memberId, 'task-id': taskId } = context.query as {
      id: string;
      'task-id': string;
    };
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, orgId);
    const request: TaskObject | unknown = token && orgId
      ? await API.TASKS.fetchSingleTask(taskId as string, orgId, token)
      : null;

    return {
      props: {
        task: request || {},
        member: member || [],
      },
    };
  } catch (error) {
    return {
      props: {
        task: {},
        member: [],
      },
    };
  }
}
export default MemberTaskDetail;
