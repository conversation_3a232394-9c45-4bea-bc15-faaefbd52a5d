/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MainLayout from 'layouts/main-layout';
import AddEditMemberTask from 'components/members/tasks/add-edit';
import { TaskObject } from 'types/tasks-response-type';

interface AddTaskProps {
  member: any;
  task: TaskObject;
}

function EditTask({ member, task }: AddTaskProps) {
  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <AddEditMemberTask add={false} task={task} member={member} />
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let orgId;
    // eslint-disable-next-line prefer-destructuring
    const { id: memberId, 'task-id': taskId } = context.query as {
      id: string;
      'task-id': string;
    };
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, orgId);
    const request: TaskObject | unknown = token && orgId
      ? await API.TASKS.fetchSingleTask(taskId as string, orgId, token)
      : null;

    return {
      props: {
        task: request || {},
        member: member || [],
      },
    };
  } catch (error) {
    return {
      props: {
        task: {},
        member: [],
      },
    };
  }
}

export default EditTask;
