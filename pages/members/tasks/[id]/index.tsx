/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { Button } from '@mui/material';
import { useState } from 'react';
import { useRouter } from 'next/router';
import TaskRow from 'components/tasks/TaskRow';

interface TasksProps {
  member: any;
  tasks: any;
}
function Tasks({ member, tasks }: TasksProps) {
  const router = useRouter();
  const tabs = [
    {
      label: 'Pending',
      value: 'pending',
    },
    {
      label: 'Picked up',
      value: 'picked up',
    },
  ];
  const [activeTab, setActiveTab] = useState('pending');
  const pickedUp = tasks['picked up'] ?? [];
  const pending = tasks.pending ?? [];

  return (
    <MemberLayout
      memberId={member.id}
      active="tasks"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Tasks"
    >
      <div className="w-full pt-1 pb-1 justify-between pl-10 grid grid-flow-col auto-cols-max items-center">
        <p className="m-0 p-0 font-[500]">
          Tasks (
          {pickedUp.length + pending.length}
          )
        </p>
        <Button
          onClick={() => router.push(`/members/tasks/add/${member.id}`)}
          variant="contained"
          color="success"
          component="label"
          className="rounded-md m-0 p-0"
        >
          + New Task
        </Button>
      </div>

      <div
        className="grid grid-flow-col gap-0 pl-10 pt-1 pb-1"
        style={{
          justifyContent: 'start', // Ensures items stay aligned to the start
          width: 'fit-content', // Ensures the grid only takes as much space as its content
        }}
      >
        {tabs.map((tab, index) => (
          <div
            key={tab.value}
            onClick={() => setActiveTab(tab.value)}
            style={{
              background:
                activeTab !== tab.value
                  ? 'linear-gradient(180deg, #F7F8F8 0%, #DCDFDF 100%)'
                  : 'linear-gradient(180deg, #404848 0%, #868B8B 100%)',
            }}
            className={`cursor-pointer w-[144px] h-[38px] grid content-center 
              ${(index === 0) ? 'rounded-l-lg' : ''} 
              ${(index === tabs.length - 1) ? 'rounded-r-lg' : ''}
              ${index !== 0 && index !== tabs.length - 1 ? 'border-l-0 border-r-0' : ''}`}
          >
            <p
              className={`text-center font-[500] text-sm ${
                activeTab === tab.value ? 'text-white' : 'text-[#262D2D]'
              }`}
            >
              {`${tab.label} (${tasks[tab.value]?.length ?? 0})`}
            </p>
          </div>
        ))}
      </div>

      <div className="grid grid-flow-row gap-0 placeholder-teal-100 mr-5 mt-1">
        {tasks[activeTab].map((task:any) => (
          <TaskRow task={task} rowClickUrl={`/members/tasks/${member.id}/task/${task.id}`} />
        ))}
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    const memberId: string = context.query.id as string;

    // Extract cookies
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token; // Retrieve token from cookies
      org = cookies.orgID; // Retrieve orgID from cookies
    }

    // Fetch member details
    const member = await API.MEMBERS.fetchMember(memberId, token, org);

    // Fetch member tasks
    const request: any = await API.MEMBERS.fetchMemberTasks(
      memberId,
      org,
      token,
    );

    // Extract tasks object directly from the response
    const tasks = request.tasks || {}; // Safely access the tasks property

    return {
      props: {
        tasks, // Pass the tasks object directly
        member: member || {}, // Member data fallback
      },
    };
  } catch (error) {
    return {
      props: {
        tasks: {}, // Return an empty object if tasks are unavailable
        member: {}, // Return an empty object for member in case of error
      },
    };
  }
}

export default Tasks;
