/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MainLayout from 'layouts/main-layout';
import AddEditMemberTask from 'components/members/tasks/add-edit';

interface AddTaskProps {
  member: any;
}

function AddTask({ member }: AddTaskProps) {
  return (
    <MainLayout
      buttonProps={{
        addButton: false,
      }}
    >
      <AddEditMemberTask add member={member} />
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      org = cookies.orgID;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    // eslint-disable-next-line max-len
    const request: any = await API.MEMBERS.fetchMemberNotes(memberId, token);
    return {
      props: {
        notes: request.items || [],
        member: member || [],
      },
    };
  } catch (error) {
    return {
      props: {
        notes: [],
        member: [],
      },
    };
  }
}

export default AddTask;
