/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import React, { useEffect } from 'react';
import { <PERSON><PERSON>, TextField } from '@mui/material';
import { useRouter } from 'next/router';
import useAddAttachmentLogic from 'logics/attachments/use-form-handler';
import BannerPicker from 'components/banner-picker/banner-picker';
import MainLayout from 'layouts/main-layout';
import { isEmptyObject } from 'utils/helper';
import { useModal } from 'contexts/modal-context/modal-context';

interface Props {
    attachment: any;
    add: boolean;
}

function AttachmentsAddUpdate({ attachment = [], add = true }: Props) {
  const router = useRouter();

  const { setPicture, picture, formik } = useAddAttachmentLogic(true, router?.query?.id as string);
  const { showModal } = useModal();

  useEffect(() => {
    if (!isEmptyObject(formik.errors)) {
      showModal('Missing Fields', `
            ${Object.keys(formik.errors).map((key) => `${(formik as any).errors[key]}`)}
          `);
    }
  }, [JSON.stringify(formik.errors)]);

  return (
    <MainLayout buttonProps={{
      addButton: false,
    }}
    >
      <div className="pt-6 grid grid-cols-3 gap-7">
        <div className="col-span-3 items-center grid grid-flow-col auto-cols-auto place-content-between w-full">
          <p className="text-lg p-0 m-0 font-[500] col-span-3">
            <span
              className="font-[300] cursor-pointer"
              onClick={() => {
                router.back();
              }}
            >
              Attachments
            </span>
            {` > ${add ? 'Add Attachment' : `Update Attachment (${attachment?.title})`}`}
          </p>
          <Button
            onClick={formik.submitForm}
            variant="contained"
            className="rounded-md h-11 font-[500] text-sm"
          >
            Save
          </Button>
        </div>
        <p className="text-lg font-[500] col-span-3">Attachment Details</p>
        <BannerPicker picture={picture} setPicture={setPicture} />
        <TextField
          name="title"
          className="col-span-3 w-1/3"
          onChange={formik.handleChange}
          placeholder="Enter Title"
          value={formik.values.title}
          label="Title"
          variant="filled"
        />
        {/* Add more fields as necessary */}
      </div>
    </MainLayout>
  );
}

export default AttachmentsAddUpdate;
