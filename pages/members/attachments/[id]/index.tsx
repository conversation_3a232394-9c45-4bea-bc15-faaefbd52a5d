/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import {
  Avatar,
  Button,
  ButtonBase,
  Dialog,
  DialogContent,
} from '@mui/material';
import { AttachmentResponse } from 'types/attachment-types';
import SafeDate from 'components/date-time/SafeDate';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useState } from 'react';
import ActionContainer from 'components/action-container/action-container';
import { useModal } from 'contexts/modal-context/modal-context';
import { useRouter } from 'next/router';
import Image from 'next/image';

interface AttachmentsProps {
  member: any;
  attachments: AttachmentResponse[];
}
function Attachments({ member, attachments = [] }: AttachmentsProps) {
  const [open, setOpen] = useState(false); // State to handle Dialog open/close
  const [currentImage, setCurrentImage] = useState(''); // State to handle current image
  const { refresh } = useRefreshSSR();
  const { showModal } = useModal();
  const router = useRouter();
  const handleClickOpen = (imageUrl: string) => {
    setCurrentImage(imageUrl);
    if (imageUrl.endsWith('.pdf')) {
      window.open(imageUrl, '_blank');
      return;
    }
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  const handleOnDelete = (item: AttachmentResponse) => {
    showModal(
      'Warning',
      'This process will permanently alter or delete data, settings, or configurations. Once initiated, there is no possibility of recovery or restoration. Are you sure you want to delete this image?',
      async () => {
        await API.MEMBERS.deleteMemberAttachment(item.id);
        refresh();
      },
    );
  };
  // const router = useRouter();
  return (
    <MemberLayout
      memberId={member.id}
      active="attachments"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Attachments"
    >
      <div className="w-full pt-1 pb-1 justify-between pl-10 grid grid-flow-col auto-cols-max items-center">
        <p className="m-0 h-0 font-[500]">
          Attachments (
          {attachments?.length}
          )
        </p>
        <Button
          onClick={() => router.push(`${member.id}/add`)}
          variant="contained"
          color="success"
          component="label"
          className="rounded-md m-0 p-0 h-[40px] float-right"
        >
          + Add Attachment
        </Button>
      </div>
      <div className="grid grid-cols-3 gap-3 innerSpace pl-20 pr-20 placeholder-teal-100 ">
        {attachments.map((attachment) => (
          <div key={attachment.id} className="grid auto-cols-auto pb-10">
            <ButtonBase onClick={() => handleClickOpen(attachment.url)}>
              {attachment.url.toLowerCase().endsWith('.pdf') ? (
                <div className="rounded-md w-full h-auto aspect-video object-contain bg-[#cdcdcd] flex items-center justify-center">
                  <Image
                    src="/icons/pdf-icon.png"
                    width={100}
                    height={125}
                    alt="PDF Icon"
                  />
                </div>
              ) : (
                <Avatar
                  alt={attachment.name}
                  src={attachment.url}
                  className="rounded-md w-full h-auto aspect-video object-cover"
                />
              )}
            </ButtonBase>
            <div className="grid grid-flow-col items-center justify-between auto-cols-auto">
              <div>
                <p className="m-0 p-0 font-[500] text-sm pt-1">
                  {attachment.name}
                </p>
                <p className="m-0 p-0 font-[300] text-sm">
                  <SafeDate date={attachment.createdAt} />
                </p>
              </div>
              <ActionContainer
                deleteLabel="Remove Attachment"
                editLabel="Edit Attachment"
                onDelete={() => handleOnDelete(attachment)}
              />
            </div>
          </div>
        ))}
      </div>
      <Dialog open={open} onClose={handleClose} maxWidth="md" fullWidth>
        <DialogContent>
          <Avatar
            className="rounded-md  w-[100%] h-auto object-contain"
            src={currentImage}
          />
        </DialogContent>
      </Dialog>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    // let orgId;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      org = cookies.orgID;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    // eslint-disable-next-line max-len
    const request: any = await API.MEMBERS.fetchMemberAttachments(
      memberId,
      'attachment',
      token as string,
    );
    return {
      props: {
        attachments: request.items || [],
        member: member || [],
      },
    };
  } catch (error) {
    return {
      props: {
        attachments: [],
        member: [],
      },
    };
  }
}

export default Attachments;
