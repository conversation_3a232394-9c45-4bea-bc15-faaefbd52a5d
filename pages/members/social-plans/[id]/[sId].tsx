/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
import { Button } from '@mui/material';
import API from 'api/src/lib/api';
import { Network } from 'api/src/types';
import UpdateNetworkStatus from 'components/members/social-plans/update-status';
import { useModal } from 'contexts/modal-context/modal-context';
import { parse } from 'cookie';
import useAuth from 'hooks/use-auth';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { GetServerSidePropsContext } from 'next';
import { useRouter } from 'next/router';
import { Member } from 'types/members-response-type';
import React, { useEffect, useState } from 'react';
import { SocialPlan } from 'types/social-plans-types';
import { capitalizeWords, dateFormat, groupBy } from 'utils/helper';
import MemberLayout from 'layouts/member-details-layout';
import CustomTable from 'components/table-components/table';
import EndSocialPlan from 'components/members/social-plans/update-status/end-social-plan';
import Search from 'components/search/search';
import TimelineItems from 'components/members/member-status-widget/items';
import { PaginatedTimeLineResponse } from 'types/timeline-types';

interface SocialPlansProps {
  member: Member;
  socialPlan: SocialPlan;
  timeline: any;
}
function DetailSocialPlans({ socialPlan, member, timeline }: SocialPlansProps) {
  const [openModal, setOpenModal] = useState(false);
  const [openUpdateStatus, setOpenUpdateStatus] = useState(false);
  const [updatedNetwork, setUpdatedNetwork] = useState<Network>({} as Network);
  const { showModal } = useModal();
  const router = useRouter();
  const { user } = useAuth();
  const { refresh } = useRefreshSSR();
  const initialValues = {
    reason: '',
    notes: '',
  };
  const [navigator, setNavigator] = useState<any>(null);
  const [openEndSocialPlan, setOpenEndSocialPlan] = useState(false);
  const tabs = [
    {
      label: 'Plan Status',
      value: 'status',
    },
    {
      label: 'Timeline',
      value: 'timeline',
    },
  ];
  const [activeTab, setActiveTab] = useState('status');
  const [timelineList, setTimelineList] = useState<PaginatedTimeLineResponse>(
    timeline || {
      items: [],
      metadata: {
        per: 0,
        total: 0,
        page: 0,
      },
    },
  );

  useEffect(() => {
    // Fetch navigator
    const fetchNavigator = async () => {
      if (socialPlan.creator) {
        const navigatorFetch = await API.USER.getUser(socialPlan.creator);
        setNavigator(navigatorFetch);
      }
    };
    fetchNavigator();
  }, []);

  function countUniqueServiceTypes(plan: SocialPlan) {
    const uniqueTypes = new Set();

    plan.items.forEach((item) => {
      const type = item.network?.types?.[0];
      if (type) {
        uniqueTypes.add(type);
      }
    });

    return uniqueTypes.size;
  }

  const handleEndSocialPlan = async () => {
    setOpenEndSocialPlan(true);
  };
  const sortedTimeline = timeline?.items.sort(
    (a: any, b: any) => new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime(),
  );
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value.toLowerCase();

    if (searchTerm === '') {
      setTimelineList(sortedTimeline || []);
      return;
    }

    const filteredTimeline = timeline?.items.filter(
      (item: any) => item.title?.toLowerCase().includes(searchTerm)
        || item.desc?.toLowerCase().includes(searchTerm)
        || item.status?.toLowerCase().includes(searchTerm),
    );

    setTimelineList(filteredTimeline || []);
  };

  return (
    <MemberLayout
      memberId={member.id}
      active="social-plans"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Social Plans"
      updateUrl={`/members/social-plans/${member.id}`}
      addLabel={
        capitalizeWords(socialPlan.title, true)
        || `${capitalizeWords(member.firstName)}'s Social Plan`
      }
      add
      rightSide={
        socialPlan.status !== 'complete' ? (
          <Button
            variant="contained"
            className="rounded-md !min-w-[100px] grey-btn !text-[#E42B57]"
            onClick={handleEndSocialPlan}
          >
            End Plan
          </Button>
        ) : (
          <div className="py-3" />
        )
      }
    >
      <div className="pl-10 ">
        <div className="borderLine">
          <p className="font-[600] text-[18px] p-0 mb-[20px] mt-4">
            Social Plan Details
          </p>
          <div className="flex gap-[20%] mb-5">
            <div className="flex gap-5">
              <div className="text-[15px] font-[400] flex flex-col no-child-p-margin">
                <p>Member:</p>
                <p>Navigator:</p>
              </div>
              <div className="text-[15px] font-[500] flex flex-col no-child-p-margin">
                <p>
                  {capitalizeWords(
                    `${member?.firstName} ${member?.lastName}`,
                    true,
                  )}
                </p>
                <p>
                  {capitalizeWords(
                    `${navigator?.firstName} ${navigator?.lastName}`,
                    true,
                  )}
                </p>
              </div>
            </div>
            <div className="flex gap-5">
              <div className="text-[15px] font-[400] flex flex-col no-child-p-margin">
                <p>Areas:</p>
                <p>Networks:</p>
              </div>
              <div className="text-[15px] font-[500] flex flex-col no-child-p-margin">
                <p>{countUniqueServiceTypes(socialPlan)}</p>
                <p>{socialPlan.items.length}</p>
              </div>
            </div>
            <div className="flex gap-5">
              <div className="text-[15px] font-[400] flex flex-col no-child-p-margin">
                <p>Created:</p>
                <p>Status:</p>
              </div>
              <div className="text-[15px] font-[500] flex flex-col no-child-p-margin">
                <p>{dateFormat(socialPlan.createdAt)}</p>
                <p
                  className={`${
                    socialPlan.status === 'active' && 'text-[#008390]'
                  }`}
                >
                  {capitalizeWords(socialPlan.status)}
                </p>
              </div>
            </div>
          </div>
        </div>
        <div className="flex justify-between mt-4 mb-7">
          <div className="flex">
            {tabs.map((tab, index) => (
              <div
                key={tab.value}
                onClick={() => setActiveTab(tab.value)}
                style={{
                  background:
                    activeTab !== tab.value
                      ? 'linear-gradient(180deg, #F7F8F8 0%, #DCDFDF 100%)'
                      : 'linear-gradient(180deg, #404848 0%, #868B8B 100%)',
                }}
                className={`cursor-pointer px-6 h-[38px] grid content-center 
              ${index === 0 ? 'rounded-l-lg' : ''} 
              ${index === tabs.length - 1 ? 'rounded-r-lg' : ''}
              ${
                index !== 0 && index !== tabs.length - 1
                  ? 'border-l-0 border-r-0'
                  : ''
              }`}
              >
                <p
                  className={`text-center font-[500] text-sm ${
                    activeTab === tab.value ? 'text-white' : 'text-[#262D2D]'
                  }`}
                >
                  {tab.label}
                </p>
              </div>
            ))}
          </div>
        </div>
        {activeTab === 'status' && (
          <div className="mt-9">
            {groupBy(socialPlan.items, (x) => x.type).length === 0 && (
              <p className="italic text-[#747A7A] mt-6">
                There are no active networks for this social plan.
              </p>
            )}
            {groupBy(socialPlan.items, (x) => x.type).map((item: any[]) => (
              <div key={item[0].id} className="mb-12">
                <div>
                  <p className="font-[500] text-lg m-0 mb-3">
                    {capitalizeWords(item[0].type)}
                  </p>
                </div>
                <CustomTable
                  source={item}
                  // eslint-disable-next-line @typescript-eslint/no-empty-function
                  variant="social-plan-network-item-table"
                  style={{
                    border: 'solid 1px lightgray',
                    borderRadius: '10px',
                    borderBottom: 'none',
                  }}
                  headCellStyle={{
                    padding: '4px 16px',
                    fontSize: '13px',
                    color: '#747A7A',
                  }}
                  tableCellStyle={{ padding: '10px 16px', fontSize: '15px' }}
                  handleDelete={handleEndSocialPlan}
                  firstColumnStaticWidth={30}
                  secondColumnStaticWidth={20}
                  handleEdit={setOpenUpdateStatus}
                />
              </div>
            ))}
          </div>
        )}
        {activeTab === 'timeline' && (
          <div>
            <div className="flex justify-between items-center mb-3">
              <p className="font-[600] text-[18px] p-0 m-0">
                Events
                {' '}
                {`(${sortedTimeline?.length})`}
              </p>
              <div>
                <Search placeholder="Search timeline" onChange={handleSearch} />
              </div>
            </div>
            <TimelineItems initialData={timelineList} memberId={member.id} />
          </div>
        )}
        <UpdateNetworkStatus
          socialPlanId={socialPlan.id}
          navigatorId={user?.id as string}
          network={updatedNetwork}
          open={openUpdateStatus}
          refresh={refresh}
          setOpen={setOpenUpdateStatus}
        />
      </div>
      <EndSocialPlan
        open={openEndSocialPlan}
        refresh={refresh}
        setOpen={setOpenEndSocialPlan}
        socialPlanId={socialPlan.id}
        memberName={`${member?.firstName} ${member?.lastName}`}
        memberId={member.id}
      />
    </MemberLayout>
  );
}

export const getServerSideProps = async (
  context: GetServerSidePropsContext,
) => {
  try {
    const { req } = context;
    let token;
    let org;
    // let orgId;
    const memberId: string = context.query.id as string;
    const socialPlanId: string = context.query.sId as string;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      org = cookies.orgID;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    // eslint-disable-next-line max-len
    const request: any = await API.SOCIALPLANS.fetchSingleSocialPlan(
      socialPlanId,
      token,
      org,
    );
    const timeline = await API.SOCIALPLANS.fetchSocialPlanTimeline(
      socialPlanId,
      token,
      org,
    );

    return {
      props: {
        socialPlan: request || [],
        member: member || [],
        timeline: timeline || [],
      },
    };
  } catch (error) {
    return {
      props: {
        socialPlan: [],
        member: [],
        timeline: [],
      },
    };
  }
};

export default DetailSocialPlans;
