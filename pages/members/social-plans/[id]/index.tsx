import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { Button } from '@mui/material';
import { useEffect, useState } from 'react';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { SocialPlan, SocialPlansProps } from 'types/social-plans-types';
import CustomTable from 'components/table-components/table';
import { useRouter } from 'next/router';
import EndSocialPlan from 'components/members/social-plans/update-status/end-social-plan';

function SocialPlans({ member, socialPlans = [], pageInfo }: SocialPlansProps & { pageInfo: any }) {
  // const { user } = useAuth();
  const [openEndSocialPlan, setOpenEndSocialPlan] = useState(false);
  const [socialPlanId, setSocialPlanId] = useState<string>('');
  const [socialPlanData, setSocialPlanData] = useState<SocialPlan[]>([]);
  const { refresh } = useRefreshSSR();
  const router = useRouter();
  const { sortBy = 'createdAt', sortDirection = 'desc' } = router.query;

  useEffect(() => {
    setSocialPlanData(socialPlans);
  }, [socialPlans]);

  const handleEndSocialPlan = async (id: string) => {
    setSocialPlanId(id);
    setOpenEndSocialPlan(true);
  };

  return (
    <MemberLayout
      memberId={member.id}
      active="social-plans"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Social Plans"
    >
      <div className="w-full pl-10">
        <div className="flex items-center justify-between mt-1 mb-4">
          <p className="font-[500] text-[18px] m-0">
            Social Plans (
            {pageInfo.total}
            )
          </p>
          <Button
            variant="contained"
            onClick={() => router.push(`/members/social-plans/${member.id}/new`)}
            color="success"
            className="rounded-md"
          >
            + New Social Plan
          </Button>
        </div>
        {socialPlanData.length > 0 ? (
          <div className="mt-3">
            <CustomTable
              source={socialPlanData}
              variant="social-plan-table"
              style={{
                border: 'solid 1px lightgray',
                borderRadius: '10px',
                borderBottom: 'none',
              }}
              headCellStyle={{
                padding: '4px 16px',
                fontSize: '13px',
                color: '#747A7A',
              }}
              tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
              handleDelete={handleEndSocialPlan}
              pageInfo={pageInfo}
              sortBy={sortBy as string}
              sortDirection={sortDirection as 'asc' | 'desc'}
              onPageChange={(newPage) => {
                router.push({
                  pathname: `/members/social-plans/${member.id}`,
                  query: {
                    ...router.query,
                    page: newPage,
                    sortBy,
                    sortDirection,
                  },
                });
              }}
              onSortChange={(newSortBy, newDirection) => {
                router.push({
                  pathname: `/members/social-plans/${member.id}`,
                  query: {
                    page: 1,
                    sortBy: newSortBy,
                    sortDirection: newDirection,
                  },
                });
              }}
            />
          </div>
        ) : (
          <p className="text-[14px] font-[400] italic text-[#262D2D] p-0 m-0">
            No social plans found for member.
          </p>
        )}

        <EndSocialPlan
          open={openEndSocialPlan}
          refresh={refresh}
          setOpen={setOpenEndSocialPlan}
          socialPlanId={socialPlanId}
          memberName={`${member?.firstName} ${member?.lastName}`}
          memberId={member.id}
        />
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req, query } = context;
    const page = parseInt((query.page as string) || '1', 10);
    const sortBy = (query.sortBy as string) || 'createdAt';
    const sortDirection = (query.sortDirection as string) || 'desc';
    const pageSize = 12;

    let token;
    let org;
    const memberId: string = context.query.id as string;

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }

    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    const request: any = await API.MEMBERS.fetchMemberSocialPlans(
      memberId,
      token,
      org,
      pageSize,
      page,
      sortBy,
      sortDirection,
    );

    return {
      props: {
        socialPlans: request.items || [],
        pageInfo: request.metadata || { per: 0, total: 0, page: 1 },
        member: member || {},
      },
    };
  } catch (error) {
    return {
      props: {
        socialPlans: [],
        pageInfo: { per: 0, total: 0, page: 1 },
        member: {},
      },
    };
  }
}

export default SocialPlans;
