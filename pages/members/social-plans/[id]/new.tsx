/* eslint-disable jsx-a11y/no-noninteractive-element-interactions */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
import React, { useContext, useEffect, useState } from 'react';
import { Button, TextField } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { Member } from 'components/members/members-table/type';
import { capitalizeWords } from 'utils/helper';
import { Network } from 'api/src/types';
import SelectNetwork from 'components/networks/select-network/select-network';
import CustomTable from 'components/table-components/table';
import MemberLayout from 'layouts/member-details-layout';
import { GetServerSidePropsContext } from 'next';
import API from 'api/src/lib/api';
import { parse } from 'cookie';
import Cookies from 'js-cookie';
import router from 'next/router';
import { useModal } from 'contexts/modal-context/modal-context';

interface CreateSocialPlanProps {
  open: boolean;
  setOpen: React.Dispatch<React.SetStateAction<boolean>>;
  member: Member;
  type: string;
  callback?: (data: any) => void;
}

interface NetworkListProps {
  type: string;
  network: Network[];
}

function NewSocialPlan({
  open,
  member,
  callback = () => '',
  setOpen,
  type,
}: CreateSocialPlanProps) {
  const [title, setTitle] = useState('');
  const [selectedNetworks, setSelectedNetworks] = useState<Network[]>([]);
  const [networkList, setNetworkList] = useState<NetworkListProps[]>([]);
  const [networkType, setNetworkType] = useState('');
  const [openSelectNetwork, setOpenSelectNetwork] = useState(false);
  const sections = useContext(ConstantsContext)?.carePackageSections;
  const [filterTrigger, setFilterTrigger] = useState(false);
  const { showModal } = useModal();
  const handleClose = () => {
    setOpen(false);
  };

  // const handleCallBack = () => {
  //   const model = {
  //     title,
  //     networkList,
  //     packageType: type,
  //   };
  //   callback(model);
  //   handleClose();
  // };

  useEffect(() => {
    if (!openSelectNetwork) {
      setNetworkList((prevList) => {
        const updatedList = prevList.map((item) => (item.type === networkType
          ? { ...item, network: selectedNetworks }
          : item));

        // If the type doesn't exist yet, add it
        if (!updatedList.find((item) => item.type === networkType)) {
          updatedList.push({ type: networkType, network: selectedNetworks });
        }

        return updatedList;
      });

      setSelectedNetworks([]); // Reset only when modal closes
      setFilterTrigger(!filterTrigger);
    }
  }, [openSelectNetwork]);

  const handleSelectNetworksClick = (itemKey: string) => {
    const existingNetworks = networkList.find((x) => x.type === itemKey)?.network || [];
    setSelectedNetworks(existingNetworks);
    setNetworkType(itemKey);

    setTimeout(() => {
      setOpenSelectNetwork(true);
    }, 0);
  };

  const removeNetwork = (networkToRemove: Network) => {
    setNetworkList((prevList) => prevList.map((item) => {
      if (item.type === networkType) {
        const updatedNetworks = item.network.filter(
          (network) => network.id !== networkToRemove.id,
        );
        return { ...item, network: updatedNetworks };
      }
      return item;
    }));
  };

  function haversineDistanceMiles(
    lat1: number,
    lon1: number,
    lat2: number,
    lon2: number,
  ): number {
    const toRadians = (degree: number) => (degree * Math.PI) / 180;

    const R = 3958.8; // Earth's radius in miles

    const dLat = toRadians(lat2 - lat1);
    const dLon = toRadians(lon2 - lon1);

    const a = Math.sin(dLat / 2) ** 2
      + Math.cos(toRadians(lat1))
        * Math.cos(toRadians(lat2))
        * Math.sin(dLon / 2) ** 2;

    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));

    return R * c;
  }

  useEffect(() => {
    setNetworkList((prevList) => prevList
      .filter((item) => item.network.length > 0)
      .map((item) => ({
        ...item,
        network: item.network.map((network) => {
          const memberLat = member.address[0]?.lat;
          const memberLon = member.address[0]?.lon;
          const networkLat = network.address[0]?.lat;
          const networkLon = network.address[0]?.lon;

          let distanceToMember = null;

          if (
            memberLat != null
              && memberLon != null
              && networkLat != null
              && networkLon != null
          ) {
            distanceToMember = Math.round(
              haversineDistanceMiles(
                memberLat,
                memberLon,
                networkLat,
                networkLon,
              ) * 10,
            ) / 10;
          }

          return {
            ...network,
            distanceToMember,
          };
        }),
      })));
  }, [filterTrigger]);

  const handleSend = async () => {
    const model = {
      orgID: Cookies.get('orgID'),
      title,
      status: 'active', // todo: active/draft
      type: 'individual',
      creator: Cookies.get('userID'),
      reciever: member.id,
    };
    if (title === '') {
      showModal(
        'Incomplete Form',
        'Please enter a title for the social plan.',
      );
      return;
    }
    const response: any = await API.SOCIALPLANS.createSocialPlan(model);
    if (response.id && networkList.length > 0) {
      networkList.forEach(async (item) => {
        item.network.forEach(async (network) => {
          const itemModel = {
            item: {
              networkID: network.id,
              creatorID: Cookies.get('userID'),
              type: item.type,
            },
          };
          await API.SOCIALPLANS.createItemSocialPlan(itemModel, response.id);
        });
      });
    }
    router.push(`/members/social-plans/${member.id}`);
  };

  return (
    <MemberLayout
      memberId={member.id}
      active="social-plans"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Social Plans"
      updateUrl={`/members/social-plans/${member.id}`}
      addLabel="New Plan"
      add
      rightSide={(
        <Button
          variant="contained"
          className="rounded-md !min-w-[50px]"
          onClick={handleSend}
        >
          <CheckIcon className="w-5 h-5 mr-2" />
          Send
        </Button>
      )}
    >
      <div className="pl-10 grid grid-cols-3 gap-7 mt-3">
        <TextField
          onChange={(e) => setTitle(e.target.value)}
          className="col-span-3 w-1/3"
          label="Enter plan title"
          variant="filled"
        />
        <div className="col-span-3 pb-5">
          {sections?.map((item) => (
            <div key={item.key} className="mb-10">
              <div className="flex items-center justify-between mb-3">
                <p className="font-[600] text-[18px] m-0 p-0">
                  {capitalizeWords(item.title)}
                </p>
                <p
                  onClick={() => handleSelectNetworksClick(item.key)}
                  className="text-primary font-[500] text-[15px] cursor-pointer hover:text-primary/80 m-0 p-0"
                >
                  Select Networks
                </p>
              </div>
              <div className="grid items-center auto-cols-auto w-full">
                {networkList.filter((x) => x.type === item.key)[0]?.network
                  ?.length > 0 ? (
                    <CustomTable
                      source={
                      networkList.filter((x) => x.type === item.key)[0].network
                    }
                      variant="social-plan-network-table"
                      metadata={{
                        list: networkList,
                        sKey: item.key,
                        setSelected: setNetworkList, // Pass removeNetwork function to CustomTable
                      }}
                      handleDelete={removeNetwork}
                      headCellStyle={{
                        borderBottom: 'solid 1px #DCDFDF',
                      }}
                      style={{
                        borderBottom: 'none',
                      }}
                      firstColumnStaticWidth={25}
                    />
                  ) : (
                    <p className="font-[400] text-[15px] m-0 p-0 text-[#747A7A]">
                      No networks selected.
                    </p>
                  )}
              </div>
            </div>
          ))}
          <SelectNetwork
            open={openSelectNetwork}
            setOpen={setOpenSelectNetwork}
            selectedNetworks={selectedNetworks}
            networkType={networkType}
            setSelectNetworks={setSelectedNetworks}
            member={member}
          />
        </div>
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, org);

    return {
      props: {
        member: member || [],
      },
    };
  } catch (error) {
    return {
      props: {
        member: [],
      },
    };
  }
}

export default NewSocialPlan;
