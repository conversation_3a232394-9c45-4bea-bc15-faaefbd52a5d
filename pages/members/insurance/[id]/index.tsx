/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import { Button } from '@mui/material';
// import useRefreshSSR from 'hooks/use-refresh-ssr';
import React, { useContext, useEffect, useState } from 'react';
import { useRouter } from 'next/router';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import CustomTable from 'components/table-components/table';
// import { useModal } from 'contexts/modal-context/modal-context';

interface InsuranceProps {
  member: any;
  insurances: any[];
}
function Insurance({ member, insurances }: InsuranceProps) {
  // const { refresh } = useRefreshSSR();
  const router = useRouter();
  const constants = useContext(ConstantsContext);
  // const { showModal } = useModal();
  const [updatedInsurances, setUpdatedInsurances] = useState<any[]>([]);

  useEffect(() => {
    if (insurances?.length) {
      const tempInsurances = insurances.map((insurance) => ({
        ...insurance,
        carrier: insurance.plans?.[0]?.issuer || 'Unknown', // Set to issuer or a fallback value
      }));

      setUpdatedInsurances(tempInsurances);
    }
  }, [insurances]);

  return (
    <MemberLayout
      memberId={member.id}
      active="insurance"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Insurance"
    >
      <div className="w-full pt-1 pb-1 justify-between pl-10 grid grid-flow-col auto-cols-max items-center">
        <p className="m-0 p-0 font-[600] text-lg">
          Insurance (
          {insurances?.length}
          )
        </p>
        <Button
          onClick={() => router.push(`/members/insurance/${member.id}/add-edit/add`)}
          variant="contained"
          color="success"
          component="label"
          className="rounded-md m-0 p-0 h-[40px] float-right"
        >
          + Add Insurance
        </Button>
      </div>

      <div className="w-full pl-10">
        {insurances?.length > 0 ? (
          <CustomTable
            variant="member-insurances-table"
            source={updatedInsurances}
            style={{
              border: 'solid 1px lightgray',
              borderRadius: '10px',
              borderBottom: 'none',
              marginTop: '16px',
            }}
            headCellStyle={{
              padding: '4px 16px',
              fontSize: '13px',
              color: '#747A7A',
            }}
            tableCellStyle={{ padding: '0px 16px', fontSize: '15px' }}
          />
        ) : (
          <p className="text-[15px] font-[400] m-0 mt-1">
            No insurance has been listed. Add the first one.
          </p>
        )}
      </div>
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req } = context;
    let token;
    let org;
    // let orgId;
    const memberId: string = context.query.id as string;
    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      //   orgId = cookies.orgID;
      token = cookies.token; // get cookie from context.req.headers.cookie named token
      org = cookies.orgID;
    }
    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    // eslint-disable-next-line max-len
    const request: any = await API.MEMBERS.getMemberInsurance(memberId, token);
    return {
      props: {
        insurances: request.items || [],
        member: member || [],
      },
    };
  } catch (error) {
    return {
      props: {
        insurances: [],
        member: [],
      },
    };
  }
}

export default Insurance;
