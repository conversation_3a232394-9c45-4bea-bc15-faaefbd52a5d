/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import InsuranceAddUpdate from 'components/members/insurance/add-edit';

interface AddInsuranceProps {
  member: any;
  insurance: any;
  add: boolean;
}
function AddInsurance({ member, insurance, add }: AddInsuranceProps) {
  return (
    <MemberLayout
      memberId={member.id}
      active="insurance"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Insurance"
      updateUrl={`/members/insurance/${member.id}`}
      addLabel={add ? 'Add Insurance' : 'Edit Insurance'}
      add
      rightSide={<div />}
    >
      <InsuranceAddUpdate member={member} add={add} insurance={insurance} />
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req, query, resolvedUrl } = context;
    let token;
    let org;

    const memberId: string = query.id as string;
    const pathSegments = resolvedUrl.split('/'); // Split URL by "/"
    const lastSegment = pathSegments[pathSegments.length - 1]; // Get the last part of the URL
    const add = lastSegment === 'add'; // Check if it's "add"
    const insuranceId = add ? null : lastSegment; // If it's not "add", treat it as an insurance ID

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }

    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    const insurance = add ? null : await API.MEMBERS.getMemberInsuranceById(insuranceId as string, token);

    return {
      props: {
        member: member || [],
        insurance: insurance || [],
        add,
      },
    };
  } catch (error) {
    return {
      props: {
        member: [],
        insurance: [],
        add: true,
      },
    };
  }
}

export default AddInsurance;
