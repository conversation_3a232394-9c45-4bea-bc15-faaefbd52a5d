/* eslint-disable no-nested-ternary */
/* eslint-disable jsx-a11y/no-static-element-interactions */
/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable max-len */
// import { useRouter } from 'next/router';
import { GetServerSidePropsContext } from 'next';
import { parse } from 'cookie';
import API from 'api/src/lib/api';
import MemberLayout from 'layouts/member-details-layout';
import InsuranceDetail from 'components/members/insurance/detail';
import { capitalizeWords } from 'utils/helper';
import { Button } from '@mui/material';
import { useRouter } from 'next/router';
import { useModal } from 'contexts/modal-context/modal-context';

interface InsuranceDetailProps {
  member: any;
  insurance: any;
}
function InsuranceDetailView({ member, insurance }: InsuranceDetailProps) {
  const router = useRouter();
  const { showModal } = useModal();
  return (
    <MemberLayout
      memberId={member.id}
      active="insurance"
      fullName={`${member?.firstName} ${member?.lastName}`}
      updateLabel="Insurance"
      updateUrl={`/members/insurance/${member.id}`}
      addLabel={capitalizeWords(insurance?.plans?.[0]?.issuer, true) || 'Details'}
      add
      rightSide={(
        <Button
          variant="contained"
          className="grey-btn rounded-md !text-[#E42B57]"
          onClick={async () => {
            showModal('Warning', 'Are you sure you want to remove this insurance?', async () => {
              const response = await API.MEMBERS.deleteMemberInsurance(insurance.id);
              if (response) {
                router.push(`/members/insurance/${member.id}`);
              }
            });
          }}
        >
          Delete Insurance
        </Button>
)}
    >
      <InsuranceDetail member={member} insurance={insurance} />
    </MemberLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  try {
    const { req, query, resolvedUrl } = context;
    let token;
    let org;

    const memberId: string = query.id as string;
    const pathSegments = resolvedUrl.split('/'); // Split URL by "/"
    const lastSegment = pathSegments[pathSegments.length - 1]; // Get the last part of the URL
    const insuranceId = lastSegment; // If it's not "add", treat it as an insurance ID

    if (req.headers.cookie) {
      const cookies = parse(req.headers.cookie);
      token = cookies.token;
      org = cookies.orgID;
    }

    const member = await API.MEMBERS.fetchMember(memberId, token, org);
    const insurance = await API.MEMBERS.getMemberInsuranceById(insuranceId as string, token);

    return {
      props: {
        member: member || [],
        insurance: insurance || [],
      },
    };
  } catch (error) {
    return {
      props: {
        member: [],
        insurance: [],
      },
    };
  }
}

export default InsuranceDetailView;
