import React, { useEffect, useState } from 'react';
import API from 'api/src/lib/api';
import { Member } from 'components/members/members-table/type';
import Search from 'components/search/search';
import MainLayout from 'layouts/main-layout';
import { parse } from 'cookie';
import { GetServerSidePropsContext } from 'next';
import { Add } from '@mui/icons-material';
import { Button } from '@mui/material';
import CustomTable from 'components/table-components/table';
import { useRouter } from 'next/router';
import ConfigureMemberColumns from 'components/members/configure-columns';

interface PageInfo {
  per: number;
  total: number;
  page: number;
}

interface MembersPageProps {
  members: Member[];
  pageInfo: PageInfo;
}

function MembersPage({ members, pageInfo }: MembersPageProps) {
  const [memberData, setMemberData] = useState<Member[]>([]);
  const router = useRouter();
  const { sortBy = 'createdAt', sortDirection = 'desc' } = router.query;

  // Handle search input
  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = e.target.value;

    // Update query parameters with search term
    router.push({
      pathname: '/members',
      query: {
        ...router.query,
        search: searchTerm || undefined, // Update search term in query
        page: 1, // reset to the first page on search
      },
    });
  };

  useEffect(() => {
    setMemberData(members); // Set member data when the page loads or the data is updated
  }, [members]);

  return (
    <MainLayout
      buttonProps={{
        addButton: false,
        addButtonOnClick: () => {
          router.push('/members/add-edit/add');
        },
        addButtonLabel: 'Add Member',
      }}
    >
      <div style={{ gridTemplateColumns: 'min-content 1fr' }}>
        <div className="pl-[15px] pb-0 pt-[15px] max-h-screen grid grid-flow-col auto-cols-auto items-center overflow-auto pr-5">
          <p className="font-[600] text-[21px] p-0 m-0">
            Members
            <span className="font-[300]">
              {' '}
              (
              {pageInfo.total}
              )
              {' '}
            </span>
          </p>
          <div className="justify-self-end grid grid-flow-col auto-cols-max gap-2">
            <ConfigureMemberColumns />
            <Search placeholder="Search Member" onChange={handleSearch} />
            <Button
              style={{ fontWeight: 500 }}
              variant="contained"
              onClick={() => router.push('/members/add-edit/add')}
              className="rounded-md font-[500] text-sm"
            >
              <Add style={{ color: 'white', fontSize: 21 }} />
              <span className="font-[500]">Add Member</span>
            </Button>
          </div>
        </div>

        <CustomTable
          source={memberData}
          variant="main-member-table"
          pageInfo={pageInfo}
          sortBy={sortBy as string}
          sortDirection={sortDirection as 'asc' | 'desc'}
          onPageChange={(newPage) => {
            router.push({
              pathname: '/members',
              query: {
                ...router.query,
                page: newPage,
                sortBy,
                sortDirection,
              },
            });
          }}
          onSortChange={(newSortBy, newDirection) => {
            router.push({
              pathname: '/members',
              query: {
                ...router.query,
                page: 1, // reset to first page on sort
                sortBy: newSortBy === 'household' ? '' : newSortBy,
                sortDirection: newDirection,
              },
            });
          }}
          headCellStyle={{
            paddingLeft: '16px',
            paddingBottom: '4px',
          }}
          className="pr-1"
        />
      </div>
    </MainLayout>
  );
}

export async function getServerSideProps(context: GetServerSidePropsContext) {
  const { req, query } = context;
  let token;
  let orgId;
  let teamId;
  const page = parseInt((query.page as string) || '1', 10);
  const sortBy = (query.sortBy as string) || 'createdAt';
  const sortDirection = (query.sortDirection as string) || 'desc';
  const search = (query.search as string) || '';
  const pageSize = 14;

  if (req.headers.cookie) {
    const cookies = parse(req.headers.cookie);
    orgId = cookies.orgID;
    token = cookies.token; // get cookie from context.req.headers.cookie named token
    teamId = cookies.teamID === 'all' ? null : cookies.teamID;
  }

  const session: any = await API.AUTH.session(token);

  if (token && orgId) {
    if (session?.roles?.includes('admin')) {
      const members: any = await API.MEMBERS.fetchMembers(
        token,
        orgId,
        pageSize,
        page,
        sortBy,
        sortDirection,
        search,
      );

      return {
        props: {
          members: members?.items || [],
          pageInfo: members?.metadata || { per: 0, total: 0, page: 0 },
        },
      };
    }

    const members: any = teamId
      ? await API.MEMBERS.fetchMembersByTeam(
        teamId,
        token,
        orgId,
        pageSize,
        page,
        sortBy,
        sortDirection,
        search, // pass the search query to the API
      )
      : await API.MEMBERS.fetchMembers(
        token,
        orgId,
        pageSize,
        page,
        sortBy,
        sortDirection,
        search, // pass the search query to the API
      );

    return {
      props: {
        members: teamId ? members?.items || [] : members?.items || [],
        pageInfo: members?.metadata || { per: 0, total: 0, page: 0 },
      },
    };
  }

  return {
    props: {
      members: [],
      pageInfo: { per: 0, total: 0, page: 0 },
    },
  };
}

export default MembersPage;
