import API from 'api/src/lib/api';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useFormik } from 'formik';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useRouter } from 'next/router';
import { useState, useContext } from 'react';
import { createValidationSchema } from 'utils/helper';

const useAddAttachmentLogic = (add:boolean, id?:string) => {
  const initialValues = {
    title: '',
  };
  const schema = createValidationSchema(initialValues);
  const [picture, setPicture] = useState('');
  const constants = useContext(ConstantsContext);
  const { refresh } = useRefreshSSR();
  const router = useRouter();

  const onCreate = async (model:any) => {
    await API.MEMBERS.createMemberAttachment(id?.toLowerCase() || '', model);
  };

  const formik = useFormik({
    initialValues: {
      ...initialValues,
    },
    validateOnChange: false,
    onSubmit: async (values) => {
      if (!picture) {
        formik.errors.title = 'Please upload a picture';
        return;
      }
      const model = {
        base64: picture,
        name: values.title,
        type: 'attachment',
      };
      if (add) {
        await onCreate(model);
      }
      await refresh();
      router.back();
    },
    validationSchema: schema,
  });

  return {
    formik,
    picture,
    setPicture,
    router,
    constants,
  };
};
export default useAddAttachmentLogic;
