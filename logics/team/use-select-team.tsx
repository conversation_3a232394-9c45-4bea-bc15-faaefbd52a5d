/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */

import {
  useState,
} from 'react';
import { Teams } from 'types/teams-types';

const useSelectTeam = (
  selectedTeams: Teams[],
) => {
  const [open, setOpen] = useState<boolean>(false);

  function TeamSelectorWidget() {
    return (
      selectedTeams.length > 0 ? (
        <div className="grid grid-flow-col auto-cols-auto  items-center  place-content-between">
          <p className="text-lg font-[600] m-0 p-0">Teams</p>
          <p className="m-0 p-0 text-[14px] mt-2">
            <span
              onClick={() => { setOpen(true); }}
              className="text-primary  font-[500] cursor-pointer"
            >
              + Assign to Team
            </span>

          </p>
        </div>
      ) : (
        <div className="grid">
          <p className="text-lg font-[600] m-0 p-0">Teams</p>

          <p className="text-[#646F79] text-sm mt-2">
            This household does not have any team added.
            <span
              onClick={() => { setOpen(true); }}
              className="text-primary  font-[500] cursor-pointer"
            >
              {' + Assign to Team'}
            </span>
          </p>
        </div>
      )
    );
  }

  return { TeamSelectorWidget, open, setOpen };
};

export default useSelectTeam;
