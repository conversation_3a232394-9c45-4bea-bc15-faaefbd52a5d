import API from 'api/src/lib/api';
import { AuthContext } from 'contexts/auth-context/auth-context';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useFormik } from 'formik';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import Cookies from 'js-cookie';
import { useRouter } from 'next/router';
import { useState, useContext } from 'react';
import { Navigator } from 'types/navigators-types';
import { createValidationSchema } from 'utils/helper';

const useAddTeamLogic = (add: boolean, id?: string) => {
  const initialValues = {
    title: '',
  };
  const schema = createValidationSchema(initialValues);
  const [picture, setPicture] = useState('');
  const constants = useContext(ConstantsContext);
  const auth = useContext(AuthContext);
  const { refresh } = useRefreshSSR();
  const router = useRouter();
  const [selectedNavigators, setSelectedNavigators] = useState<Navigator[]>([]);

  const onUpdate = async (model: any) => {
    await API.TEAMS.updateTeam(id as string, {
      body: model,
    });
  };

  const onCreate = async (createModel: any, updateModel: any) => {
    const response: any = await API.TEAMS.createTeam({
      body: createModel,
    });
    const teamId = response.id;

    if (selectedNavigators.length > 0) {
      await API.TEAMS.updateTeam(teamId, {
        body: updateModel,
      });
    }
  };

  const formik = useFormik({
    initialValues: {
      ...initialValues,
      navigators: [],
    },
    validateOnChange: false,
    onSubmit: async (values) => {
      const createModel: any = {
        orgID: auth?.user?.org.id as string,
        name: values.title as string,
      };

      const updateModel: any = {
        ...createModel,
        navigators: selectedNavigators.map((navigator) => navigator.id),
      };

      const address = {
        city: '',
        state: '',
        street: '',
        zip: '',
        kind: 'main',
      };

      // Check if any address field is empty
      const hasEmptyAddressField = Object.values(address).some(
        (value) => value === '',
      );

      // Add address only if all fields are filled
      if (!hasEmptyAddressField) {
        createModel.address = address;
        updateModel.address = address;
      }

      if (add) {
        await onCreate(createModel, updateModel);
      } else {
        await onUpdate(updateModel);
      }

      Cookies.remove('teamID');
      Cookies.remove('teams');
      refresh();
      router.back();
      setTimeout(() => {
        router.reload();
      }, 300);
    },
    validationSchema: schema,
  });

  return {
    formik,
    picture,
    setPicture,
    selectedNavigators,
    setSelectedNavigators,
    router,
    constants,
  };
};
export default useAddTeamLogic;
