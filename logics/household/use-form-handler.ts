/* eslint-disable no-use-before-define */
import API from 'api/src/lib/api';
import { useFormik } from 'formik';
import useAuth from 'hooks/use-auth';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useRouter } from 'next/router';
import { useState } from 'react';
import { HouseHold, Address } from 'types/household-types';
import { Member } from 'types/members-response-type';
import { Teams } from 'types/teams-types';

import { createValidationSchema, getIdsFromArray } from 'utils/helper';

const useHouseholdFormHandler = (add:boolean, id?:string) => {
  const [picture, setPicture] = useState<string>('');
  const [selectedMembers, setSelectedMembers] = useState<Member[]>([]);
  const [member, setMember] = useState<Member>({} as Member);
  const [selectedTeams, setSelectedTeams] = useState<Teams[]>([]);
  const [address, setAddress] = useState<Address | null>(null);
  const router = useRouter();
  const { refresh } = useRefreshSSR();
  const { user } = useAuth();
  const initialValues = {
    title: '', street1: '', city: '', state: '', zip: '',
  };
  const schema = createValidationSchema(initialValues);
  const formik = useFormik({
    initialValues: {
      ...initialValues,
      street2: '',
    },
    onSubmit: async (values) => {
      if (add) {
        await onNewSubmit(values);
      } else {
        await onUpdate(values, id as string);
      }
      await refresh();
      router.back();
    },
    validateOnChange: false,
    validationSchema: schema,
  });
  const newHousehold = async (title:string) => {
    try {
      const request = await API.HOUSEHOLDS.createHousehold({
        body: {
          orgID: user?.org.id as string,
          title,
          kind: 'household',
          type: 'household',
          headOfHouse: {},
        },
      });
      return request;
    } catch (error:any) {
      throw new Error(error);
    }
  };
  const onUpdate = async (values:typeof formik.initialValues, householdId:string) => {
    await API.HOUSEHOLDS.updateHouseHold({
      householdId,
      body: {
        members: getIdsFromArray(selectedMembers),
        teams: getIdsFromArray(selectedTeams),
        title: values.title,
        address: {
          street: values.street1,
          street2: values.street2,
          city: values.city,
          state: values.state,
          zip: values.zip,
          kind: 'main',
        },
      },
    });
    if (picture) {
      await API.HOUSEHOLDS.createHouseholdAttachment({
        householdId,
        body: {
          type: 'householdProfile',
          name: `${householdId}-profile`,
          base64: picture,
        },
      });
    }
  };

  const onNewSubmit = async (values:typeof formik.initialValues) => {
    const household = await newHousehold(values.title) as unknown as HouseHold;

    const updateAddress = API.HOUSEHOLDS.updateHouseHold({
      householdId: household.id,
      body: {
        address: {
          street: values.street1,
          street2: values.street2,
          city: values.city,
          state: values.state,
          zip: values.zip,
          kind: 'main',
        },
      },
    });
    const createProfile = picture ? API.HOUSEHOLDS.createHouseholdAttachment({
      householdId: household.id,
      body: {
        type: 'householdProfile',
        name: `${household.id}-profile`,
        base64: picture,
      },
    }) : null;

    const attachMembers = selectedMembers.length > 0 ? API.HOUSEHOLDS.addMemberToHousehold({
      householdId: household.id,
      body: {
        members: getIdsFromArray(selectedMembers),
      },
    }) : null;

    const attachTeams = selectedTeams.length > 0 ? API.HOUSEHOLDS.addTeamToHousehold({
      householdId: household.id,
      body: {
        teams: getIdsFromArray(selectedTeams),
      },
    }) : null;
    await Promise.all([updateAddress, createProfile, attachMembers, attachTeams]);
  };

  return {
    formik,
    initialValues,
    schema,
    picture,
    selectedMembers,
    selectedTeams,
    onNewSubmit,
    onUpdate,
    setPicture,
    setSelectedMembers,
    setSelectedTeams,
    address,
    setAddress,
    member,
    setMember,
  };
};

export default useHouseholdFormHandler;
