import API from 'api/src/lib/api';
import { AuthContext } from 'contexts/auth-context/auth-context';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useFormik } from 'formik';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useRouter } from 'next/router';
import { useState, useContext } from 'react';
import { createValidationSchema } from 'utils/helper';

const useAddNavigatorLogic = (add: boolean, id?: string) => {
  const initialValues = {
    firstName: '',
    lastName: '',
    email: '',
    phone: '',
    role: '',
    navigatorTeams: [] as any[],
  };

  // const schema = createValidationSchema(initialValues);
  const [picture, setPicture] = useState('');
  const constants = useContext(ConstantsContext);
  const auth = useContext(AuthContext);
  const { refresh } = useRefreshSSR();
  const router = useRouter();

  const onUpdate = async (model: any) => {
    await API.USER.updateUser(id as string, model);
    if (picture.includes('base64')) {
      await API.USER.createUserAttachment(id as string, {
        type: 'userProfile',
        kind: 'main',
        name: `${id}-profile`,
        base64: picture,
      });
    }
  };

  const onCreate = async (model: any) => {
    const newNavigator: any = await API.AUTH.signUp(model);

    if (picture.includes('base64')) {
      await API.USER.createUserAttachment(newNavigator.user.id, {
        type: 'userProfile',
        kind: 'main',
        name: `${newNavigator.user.id}-profile`,
        base64: picture,
      });
    }

    return newNavigator.user.id;
  };

  const formik = useFormik({
    initialValues,
    validateOnChange: false,
    onSubmit: async (values) => {
      const createModel = {
        orgID: auth?.user?.org.id as string,
        firstName: values.firstName,
        lastName: values.lastName,
        username: values.email,
        password: 'Welcome2023!',
        phone: values.phone,
        roles: [values.role],
      };

      const updateModel = {
        orgID: auth?.user?.org.id as string,
        firstName: values.firstName,
        lastName: values.lastName,
        username: values.email,
        password: 'Welcome2023!',
        phone: values.phone,
        roles: [values.role],
        teams: values.navigatorTeams.map((team: any) => team.id),
      };

      let navigatorId;
      if (add) {
        navigatorId = await onCreate(createModel);
        await API.USER.updateUser(navigatorId as string, updateModel);
      } else {
        await onUpdate(updateModel);
      }

      await refresh();
      router.back();
    },
    // validationSchema: schema,
  });

  return {
    formik,
    picture,
    setPicture,
    router,
    constants,
  };
};
export default useAddNavigatorLogic;
