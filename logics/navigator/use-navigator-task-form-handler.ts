import API from 'api/src/lib/api';
import { useFormik } from 'formik';
import useAuth from 'hooks/use-auth';
import { useRouter } from 'next/router';
import { useEffect, useState } from 'react';
import { Member } from 'types/members-response-type';
import { Navigator } from 'types/navigators-types';
import { TaskObject } from 'types/tasks-response-type';
import {
  // eslint-disable-next-line max-len
  ValidationConfig, capitalizeWords, createValidationSchema, dateToEpoch,
  validateFormWithConditions,
} from 'utils/helper';

interface AddEditTaskProps {
  add?: boolean;
  task?: TaskObject;
}
const useNavigatorTaskFormHandler = ({ add = true, task }:AddEditTaskProps) => {
  const { user } = useAuth();
  const router = useRouter();
  const [selectedNavigators, setSelectedNavigators] = useState<Navigator[]>([]);
  const [selectedMembers, setSelectedMembers] = useState<Member[]>([]);
  const navigatorId = sessionStorage.getItem('navigatorId');
  const validationConfig:ValidationConfig = {
    conditions: {
      remote: [
        {
          fields: ['tmp.remoteLink'], // Still usable for single items as an array
          isRequiredIfTrue: true,
          isRequiredIfFalse: false,
          prerequisite: (values) => ['assessment', 'visit'].includes(values.tmp.taskType),
        },
        {
          fields: ['location.street', 'location.state', 'location.zip'], // Array of fields
          isRequiredIfTrue: false,
          isRequiredIfFalse: true,
          prerequisite: (values) => ['assessment', 'visit'].includes(values.tmp.taskType),
        },
      ],
      general: [{
        fields: ['tmp.phone'],
        isRequiredIfTrue: true,
        isRequiredIfFalse: true,
        prerequisite: (values) => values.tmp.taskType === 'contact' && ['phone', 'any'].includes(values.tmp.type),
      },
      {
        fields: ['title'],
        isRequiredIfTrue: true,
        isRequiredIfFalse: true,
        prerequisite: (values) => values.tmp.taskType === 'custom',
      },
      {
        fields: ['tmp.date'],
        isRequiredIfTrue: true,
        isRequiredIfFalse: true,
        prerequisite: (values) => !['no_end_date'].includes(values.tmp.dateType),
      },
      {
        fields: ['tmp.selectMember'],
        isRequiredIfTrue: true,
        isRequiredIfFalse: true,
        errorMessageIfFalse: 'Please select a member',
        errorMessageIfTrue: 'Please select a member',
        prerequisite: () => selectedMembers.length === 0,
      },
      {
        fields: ['tmp.selectNavigator'],
        isRequiredIfTrue: true,
        isRequiredIfFalse: true,
        errorMessageIfFalse: 'Select at least one navigator',
        errorMessageIfTrue: 'Select at least one navigator',
        prerequisite: () => selectedNavigators.length === 0,
      },
      ],
    }, // conditions
  };
  const requiredFields = {
    desc: '',
    remote: false,
  };

  const formik = useFormik({
    initialValues: {
      ...requiredFields,
      title: '',
      urgent: false,
      tmp: {
        taskType: '',
        type: '',
        isLocation: true,
        remoteLink: '',
        selectNavigator: '',
        selectMember: '',
        phone: '',
        useMemberLocation: false,
        date: '',
        dateType: 'date',
      },
      location: {
        street: '',
        street2: '',
        city: '',
        state: '',
        zip: '',
        kind: 'main',
      },
      taskDetails: {
        title: '',
        type: '',
        kind: '',
      },
    },
    validateOnChange: false,
    validationSchema: createValidationSchema(requiredFields),
    // eslint-disable-next-line max-len
    validate: (values) => validateFormWithConditions(values, validationConfig),
    onSubmit: async (values) => {
      const kinds = values.tmp.taskType === 'custom' ? { kind: 'custom', type: 'custom' } : {};
      const model = {
        orgID: user?.org?.id,
        title: values.tmp.taskType === 'custom' ? values.title : capitalizeWords(values.tmp.taskType, true),
        status: selectedNavigators.length === 1 ? 'picked up' : 'pending', // default value
        remote: values.remote, // if location selected,
        creatorID: user?.id,
        type: values.tmp.taskType,
        desc: values.desc,
        dueAtEpoc: values.tmp.dateType === 'no_end_date' ? dateToEpoch('2028-01-01') : dateToEpoch(values.tmp.date),
        assigneeID: selectedNavigators.length === 1
          ? selectedNavigators[0].id : undefined,
        completedBy: selectedNavigators.map((nav) => nav.id),
        receivers: selectedMembers.map((mem) => mem.id),
        location: values.tmp.isLocation ? values.location : undefined,
        taskDetail: {
          ...values.taskDetails,
          meta: {
            data: {
              remote: values.tmp.remoteLink,
              phone: values.tmp.phone,
              dateType: values.tmp.dateType,
            },
          },
          title: values.tmp.type === 'custom' ? values.title : capitalizeWords(values.tmp.taskType, true),
          ...kinds,
        },
      };
      if (!add) {
        const update = {
          id: task?.id,
          ...model,
        };
        await API.TASKS.updateTask(task?.id as string, update);
      } else {
        await API.TASKS.createTask(model);
      }
      await router.push(`/navigators/${navigatorId}`);
    },
  });
  useEffect(() => {
    if (!add && task) {
      setSelectedNavigators(task.completedBy as never);
      setSelectedMembers(task.receivers as never);
      formik.setValues({
        ...requiredFields,
        title: task.title,
        desc: task.desc,
        urgent: task.urgent ? task.urgent : false,
        tmp: {
          taskType: task.type,
          type: task.taskDetail.type,
          isLocation: !!task.location,
          remoteLink: task.taskDetail.meta?.data?.remote,
          selectNavigator: '',
          selectMember: '',
          phone: task.taskDetail.meta?.data?.phone,
          useMemberLocation: false,
          date: new Date(task.dueAtEpoc * 1000).toISOString().split('T')[0],
          dateType: task.taskDetail.meta?.data?.dateType,
        },
        location: {
          street: task.location?.street || '',
          street2: task.location?.street2 || '',
          city: task.location?.city || '',
          state: task.location?.state || '',
          zip: task.location?.zip || '',
          kind: task.location?.kind || 'main',
        },
        taskDetails: {
          title: task.taskDetail.title,
          type: task.taskDetail.type,
          kind: task.taskDetail.kind,
        },
      });
    }
  }, [task]);

  return {
    formik,
    formikType: typeof formik,
    selectedMembers,
    setSelectedMembers,
    selectedNavigators,
    setSelectedNavigators,
  };
};

export default useNavigatorTaskFormHandler;
