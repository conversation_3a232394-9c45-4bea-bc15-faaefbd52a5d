import API from 'api/src/lib/api';
import { AuthContext } from 'contexts/auth-context/auth-context';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useFormik } from 'formik';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useRouter } from 'next/router';
import { useState, useContext } from 'react';
import { Carrier, Service } from 'types/service-types';
import { createValidationSchema } from 'utils/helper';

const useAddNetworkLogic = (add:boolean, id?:string) => {
  const initialValues = {
    title: '',
    types: '',
    status: '',
  };
  const schema = createValidationSchema(initialValues);
  const [picture, setPicture] = useState('');
  const constants = useContext(ConstantsContext);
  const auth = useContext(AuthContext);
  const { refresh } = useRefreshSSR();
  const router = useRouter();
  const [selectedService, setSelectedService] = useState<Service[]>([]);

  const onUpdate = async (values:any) => {
    const serviceRequests:any = [];
    const newServices = selectedService.filter((item:Service) => !('id' in item));
    if (newServices.length > 0) {
      newServices.forEach((service: any) => {
        serviceRequests.push(API.NETWORKS.createService({
          orgID: auth?.user?.org.id as string,
          name: service.name,
          desc: service.desc,
          service: service.service,
        }));
      });
    }
    if (newServices.length > 0) {
      const services = await Promise.all(serviceRequests);
      const existingServices = selectedService.filter((item:Service) => 'id' in item);
      await API.NETWORKS.updateNetwork(id as string, {
        name: values.title as string,
        types: [values.types],
        address: {
          street: values.street as string,
          street2: values.street2 as string || '',
          state: values.state as string,
          city: values.city as string,
          zip: values.zip as string,
        },
        phone: {
          number: values.phone,
          label: 'main',
        },
        email: values?.email,
        website: values?.website,
        status: values?.status as string,
        contact: values.contact as string,
        services: services.concat(existingServices).map((service: any) => service.id),
        carriers: values.carriers.map((carrier: any) => carrier.key) as string[],
      });
    } else {
      const existingServices = selectedService.filter((item:Service) => 'id' in item);
      await API.NETWORKS.updateNetwork(id as string, {
        name: values.title as string,
        types: [values.types],
        address: {
          street: values.street as string,
          street2: values.street2 as string || '',
          state: values.state as string,
          city: values.city as string,
          zip: values.zip as string,
        },
        phone: {
          number: values.phone,
          label: 'main',
        },
        email: values?.email,
        website: values?.website,
        status: values?.status as string,
        contact: values.contact as string,
        services: existingServices.map((service: any) => service.id),
        carriers: values.carriers.map((carrier: any) => carrier.id) as string[],
      });
    }
  };

  const onCreate = async (values:any) => {
    const serviceRequests:any = [];
    const newNetwork:any = await API.NETWORKS.createNetwork({
      orgID: auth?.user?.org.id as string,
      name: values.title as string,
      types: [values.types],
      status: values?.status as string,
      contact: values.contact as string,
      carriers: values.carriers.map((carrier: Carrier) => carrier.id) as string[],
    });

    selectedService.forEach((service: any) => {
      serviceRequests.push(API.NETWORKS.createService({
        orgID: auth?.user?.org.id as string,
        name: service.name,
        desc: service.desc,
        service: service.service,
      }));
    });

    const services = await Promise.all(serviceRequests);
    const addressPhone = API.NETWORKS.createAddress(newNetwork.id, {
      address: {
        kind: 'main',
        street: values.street as string,
        street2: values.street2 as string || '',
        state: values.state as string,
        city: values.city as string,
        zip: values.zip as string,
      },
      phone: {
        label: 'main',
        number: values.phone as string,
      },
    });
    const addToService = API.NETWORKS.addServices(
      newNetwork.id,
      services.map((service: any) => service.id),
    );
    await Promise.all([addressPhone, addToService]);
  };

  const formik = useFormik({
    initialValues: {
      ...initialValues,
      street2: '',
      street: '',
      city: '',
      state: '',
      zip: '',
      contact: '',
      phone: '',
      email: '',
      website: '',
      carriers: [] as any[],
    },
    validateOnChange: false,
    onSubmit: async (values) => {
      if (add) {
        onCreate(values);
      } else {
        onUpdate(values);
      }
      await refresh();
      router.back();
    },
    validationSchema: schema,
  });

  return {
    formik,
    picture,
    setPicture,
    selectedService,
    setSelectedService,
    router,
    constants,
  };
};
export default useAddNetworkLogic;
