/* eslint-disable no-unused-expressions */
import API from 'api/src/lib/api';
import { AuthContext } from 'contexts/auth-context/auth-context';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useModal } from 'contexts/modal-context/modal-context';
import { useFormik } from 'formik';
import useRefreshSSR from 'hooks/use-refresh-ssr';
import { useRouter } from 'next/router';
import { useState, useContext } from 'react';
import { MemberTags } from 'types/members-response-type';
import { createValidationSchema } from 'utils/helper';

const useAddMemberLogic = (add:boolean, id?:string, noRefreshOnSubmit?:boolean) => {
  const initialValues = {
    firstName: '',
    lastName: '',
    type: '',
  };
  const schema = createValidationSchema(initialValues);
  const [picture, setPicture] = useState('');
  const [deleteStatus, setDeleteStatus] = useState<string[]>([]);
  const constants = useContext(ConstantsContext);
  const { showModal } = useModal();
  const auth = useContext(AuthContext);
  const { refresh } = useRefreshSSR();
  const router = useRouter();
  const statusGenerate = (values:any) => {
    const status = [];
    values.homeless && status.push(values.homelessDetails);
    values.tmp.isHospital && status.push(values.hospitalStatus);
    return status;
  };

  const removeFromProgram = async (status:string) => {
    showModal('Warning', 'Are you sure you want to remove this member from the program?', async () => {
      await API.MEMBERS.deleteMemberStatus(id as string, status);
      router.reload();
    });
  };

  const onUpdate = async (values: any, model:any) => {
    const statusCheck = statusGenerate(values);
    await API.MEMBERS.updateMember(id || '', model);

    if (picture.includes('base64')) {
      await API.MEMBERS.createMemberAttachment(id || '', {
        type: 'profile',
        name: `${id}-profile`,
        base64: picture,
      });
    }
    if (deleteStatus.length > 0) {
      // eslint-disable-next-line no-restricted-syntax
      for await (const status of deleteStatus) {
        await API.MEMBERS.deleteMemberStatus(id || '', status);
      }
    }
    if (statusCheck.length > 0) {
      // eslint-disable-next-line no-restricted-syntax
      for await (const status of statusCheck) {
        await API.MEMBERS.updateMember(id || '', { memberStatus: status });
      }
    }
  };

  const onCreate = async (values: any, model:any) => {
    const statusCheck = statusGenerate(values);
    const newUser: any = await API.MEMBERS.createMember(model as any);
    if (picture.includes('base64')) {
      await API.MEMBERS.createMemberAttachment(newUser.id || '', {
        type: 'profile',
        name: `${newUser.id}-profile`,
        base64: picture,
      });
    }
    if (statusCheck.length > 0) {
      // eslint-disable-next-line no-restricted-syntax
      for await (const status of statusCheck) {
        await API.MEMBERS.updateMember(newUser.id || '', { memberStatus: status });
      }
    }
    return newUser;
  };

  const formik = useFormik({
    initialValues: {
      ...initialValues,
      referredBy: '',
      school: '',
      schoolName: '',
      grade: '',
      gender: '',
      genderIdentity: '',
      pronouns: '',
      sexualIdentity: '',
      phoneNumber: '',
      email: '',
      lastContact: '',
      lang: '',
      status: 'active',
      ethnicity: '',
      dob: '',
      homeless: false,
      enrolledOn: '',
      unenrolledDate: '',
      pregnancyStatus: null,
      deliveryDate: '',
      military: '',
      tags: [] as MemberTags[],
      emergencyContact: {
        name: '',
        phone: '',
        type: '',
      },
      homelessDetails: {
        name: '',
        state: '',
        kind: 'homeless',
        address: {
          kind: 'main',
          street: '',
          city: '',
          state: '',
          zip: '',
        },
      },
      hospitalStatus: {
        name: '',
        state: '',
        kind: 'admission',
        location: '',
        startDate: '',
        endDate: '',
        address: {
          name: '',
          street: '',
          city: '',
          state: '',
          zip: '',
        },
      },
      tmp: {
        isPregnant: false,
        isHospital: false,
        currentStatus: 'active',
      },
    },
    validateOnChange: false,
    onSubmit: async (values) => {
      let model = {
        orgID: auth?.user.org.id as string,
        firstName: values.firstName,
        lastName: values.lastName,
        email: values.email,
        lang: values.lang,
        referredBy: values.referredBy,
        phone: values.phoneNumber,
        dob: new Date(values.dob).toISOString().replace(/T.*/, ''),
        type: values.type,
        roles: ['clients'],
        gender: values.gender,
        meta: {
          data: {},
          contacts: [values.emergencyContact],
        },
        homeless: values.homeless,
        lastContact: values?.lastContact ? values.lastContact : null,
        genderIdentity: values.genderIdentity,
        pronouns: values.pronouns,
        sexualIdentity: values.sexualIdentity,
        ethnicity: values.ethnicity,
        status: values.status,
        enrolledOn: values.status.includes('withdrawn') ? '' : values.enrolledOn?.replace(/T.*/, ''),
        unenrolledDate: !values.status.includes('withdrawn') ? '' : values.unenrolledDate?.replace(/T.*/, '') || '',
        pregnancyStatus: !values.tmp.isPregnant ? null : values.pregnancyStatus,
        deliveryDate: values.deliveryDate,
        military: values.military,
        tags: values.tags,
      };
      if (values.school.toLowerCase() === 'yes') {
        model = {
          ...model,
          school: {
            name: values.schoolName || null,
            grade: values.grade || null,
          },
        } as any;
      }
      if (add && noRefreshOnSubmit) {
        const response = await onCreate(formik.values, model); // <-- return this
        return response;
      } if (add) {
        await onCreate(formik.values, model);
        await refresh();
        router.back();
      } else {
        await onUpdate(formik.values, model);
        await refresh();
        router.reload();
      }
    },
    validationSchema: schema,
  });

  return {
    formik,
    picture,
    setPicture,
    deleteStatus,
    setDeleteStatus,
    removeFromProgram,
    router,
    constants,
  };
};
export default useAddMemberLogic;
