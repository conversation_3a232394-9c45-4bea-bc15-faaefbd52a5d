/* eslint-disable jsx-a11y/click-events-have-key-events */
/* eslint-disable jsx-a11y/no-static-element-interactions */

import {
  useState,
} from 'react';
import { Member } from 'types/members-response-type';

const useSelectMember = (
  selectedMembers: Member[],
) => {
  const [open, setOpen] = useState<boolean>(false);
  const [openAddMember, setOpenAddMember] = useState(false);
  function MemberSelectorWidget() {
    return (
      selectedMembers.length > 0 ? (
        <div className="grid grid-flow-col auto-cols-auto  items-center  place-content-between">
          <p className="text-lg font-[600] m-0 p-0">Members</p>
          <p className="m-0 p-0 text-[14px] mt-2">
            <span
              onClick={() => { setOpen(true); }}
              className="text-primary  font-[500] cursor-pointer"
            >
              {'Select Member '}
            </span>
            <span className="cursor-none">OR</span>
            <span
              onClick={() => {
                setOpenAddMember(true);
              }}
              className="text-primary  font-[500] cursor-pointer"
            >
              {' + Add Member'}
            </span>
          </p>
        </div>
      ) : (
        <div className="grid">
          <p className="text-lg font-[600] m-0 p-0">Members</p>

          <p className="text-[#646F79] text-sm mt-2">
            This household does not have any members added.
            <span
              onClick={() => { setOpen(true); }}
              className="text-primary  font-[500] cursor-pointer"
            >
              {' Select Member '}
            </span>
            <span className="cursor-none">OR</span>
            <span
              onClick={() => {
                setOpenAddMember(true);
              }}
              className="text-primary  font-[500] cursor-pointer"
            >
              {' + Add Member'}
            </span>
          </p>
        </div>
      )
    );
  }

  return {
    MemberSelectorWidget, open, setOpen, openAddMember, setOpenAddMember,
  };
};

export default useSelectMember;
