interface Answer {
  answerID: string;
    parentID: string;
    questionID: string;
    surveyID: string;
    takerID: string;
    giverID: string;
    answer: string;
    orgID: string;
    key: string;
    score: string;
    value: string;
  }

interface QuestionProps {
  parentID: string;
  answers: Answer[];
}
interface LogicProps {
  question:QuestionProps[],
  parentID:string,
  answerText:string,
  value:string,
  questionId:string,
  answerId:string,
  surveyID:string,
  takerID:string,
  giverID:string,
  orgID:string,
  key:string,
  options: boolean,
  multi?: boolean,
  id?: string,
}
const useAnswerLogic = () => {
  const generateModel = ({
    parentID,
    answerText,
    answerId,
    value,
    questionId,
    surveyID,
    takerID,
    giverID,
    orgID,
    key,
    // eslint-disable-next-line no-unused-vars
    multi = false,
    id = undefined,
  }:LogicProps) => ({
    parentID,
    answers: [{
      parentID,
      questionID: questionId,
      surveyID,
      takerID,
      giverID,
      answerID: answerId || '',
      answer: answerText,
      orgID,
      key,
      id: undefined,
      score: '0',
      value: value || '0',
    }],
  });

  const answerLogic = (props: LogicProps) => {
    const questionIndex = props.question.findIndex(
      (item) => item.parentID.toLowerCase() === props.questionId.toLowerCase(),
    );

    if (questionIndex === -1) {
      props.question.push(generateModel(props));
      return props.question;
    }

    const updatedQuestions = [...props.question];
    const currentAnswers = updatedQuestions[questionIndex].answers;

    if (props.multi) {
      const existingAnswerIndex = currentAnswers.findIndex(
        (answer) => answer.answerID.toLowerCase() === props.answerId.toLowerCase(),
      );

      if (existingAnswerIndex !== -1) {
        currentAnswers.splice(existingAnswerIndex, 1);
      } else {
        currentAnswers.push(generateModel(props).answers[0]);
      }
    } else {
      // For non-multi questions, replace the existing answer
      updatedQuestions[questionIndex].answers = [generateModel(props).answers[0]];
    }

    return updatedQuestions;
  };

  // eslint-disable-next-line max-len
  const isAnswer = (answers: QuestionProps[], answerId: string, questionId: string) => answers.some((item) => item?.answers?.some((answer) => answer?.answerID.toLowerCase() === questionId.toLowerCase()));

  const isComplete = (answers: QuestionProps[], list:any[], sKey:string, questionId:string) => {
    if (answers.length === 0) return false;
    // eslint-disable-next-line no-underscore-dangle
    if (sKey === 'full_hrp') {
      return (answers?.findIndex((x) => x?.parentID === questionId.toLowerCase()) !== -1);
    }
    return list.every((x) => answers.some((y) => y.parentID.toLowerCase() === x.id.toLowerCase()));
  };
  return { answerLogic, isAnswer, isComplete };
};

export default useAnswerLogic;
