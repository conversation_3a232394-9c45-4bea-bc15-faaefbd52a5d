import { ConstantsContext } from 'contexts/constant-context/constant-context';
import { useContext } from 'react';
import { findRangeTitle } from 'utils/helper';

export interface ScoreResponse {
    color: string;
    title: string;
}
const useScore = () => {
  const constant = useContext(ConstantsContext);
  const getScoreTitle = (score: number) => {
    if (!score) {
      return { title: 'N/A', color: 'gray' };
    }
    const ranges = constant?.scores.full_hrp;
    if (ranges) {
      return findRangeTitle(ranges, score) as ScoreResponse;
    }
    return null;
  };

  const getSurveyScore = (score: number) => {
    switch (score) {
      case 0: return { title: 'Urgent Need', color: '#DE6F09' };
      case 1: return { title: 'Maintenance', color: '#1470C4' };
      case 2: return { title: 'Resilient', color: '#0ABF89' };
      case 3: return { title: 'Thriving', color: '#0ABF89' };
      default: return { title: 'Assessment Needed', color: '#D5DCE2' };
    }
  };
  return { getScoreTitle, getSurveyScore };
};

export default useScore;
