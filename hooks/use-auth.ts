import API from 'api/src/lib/api';
import { UserLogin } from 'api/src/types';
import { AuthContext } from 'contexts/auth-context/auth-context';
import { useContext, useState } from 'react';
import Cookie from 'js-cookie';
import { useRouter } from 'next/router';
import { useLoading } from 'contexts/loading-context/loading-context';
import { useModal } from 'contexts/modal-context/modal-context';
import { convertKeyValue } from 'utils/helper';

const useAuth = () => {
  const auth = useContext(AuthContext);
  const [error, setError] = useState(null);
  const router = useRouter();
  const { showLoading, hideLoading } = useLoading();
  const { showModal } = useModal();

  const login = async ({ username, password }: UserLogin) => {
    showLoading();
    try {
      const tryLogin = await API.AUTH.login({ username, password }) as any;
      if (tryLogin) {
        const request = await API.USER.getUser(
          tryLogin.user.id,
          tryLogin.token,
          tryLogin.user.org.id,
        ) as any;
        const teams = request?.teams || [];
        const convert:any = await convertKeyValue(teams, 'id', 'name', ['key', 'title']);
        if(tryLogin.user.roles.includes('drop_all')) {
        convert.push({ key: 'all', title: 'All' });
         }
        convert.sort((a:any, b:any) => a.title.localeCompare(b.title));
        const stringfyTeams = JSON.stringify(convert);
        auth?.setAuth({ user: tryLogin.user, teams: convert });
        Cookie.set('token', tryLogin.token);
        Cookie.set('orgID', tryLogin.user.org.id);
        Cookie.set('userID', tryLogin.user.id);
        Cookie.set('roles', tryLogin.user.roles.join(','));
        Cookie.set('teams', stringfyTeams);
        Cookie.set('teamID', convert[0]?.key || '');
        if(tryLogin.user.roles.includes('admin')) {
          await router.replace('/households');
        } else {
          await router.replace(`/${tryLogin.user.meta.access[0]}`);
        }
        hideLoading();
      }
    } catch (err:any) {
      hideLoading();
      showModal('Auth Error', err?.data?.reason || JSON.stringify(err?.data));
      setError(err.data);
    }
  };

  const logout = () => {
    Cookie.remove('token');
    router.push('/login');
  };
  return {
    login, logout, ...auth, error,
  };
};
export default useAuth;
