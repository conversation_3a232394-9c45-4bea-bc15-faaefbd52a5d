import {
  Restaurant, Home, AccountBalance, Check,
  MonetizationOnOutlined, Monitor, DirectionsCar, Work, MenuBook, SvgIconComponent, Close,
} from '@mui/icons-material';
import Icon from '@mui/material/Icon';

interface IIconSet {
    [key: string]: SvgIconComponent;
}
const useIcon = () => {
  const IconSet:IIconSet = {
    housing: Home,
    nutrition: Restaurant,
    policy: AccountBalance,
    income: MonetizationOnOutlined,
    technology: Monitor,
    transportation: DirectionsCar,
    employment: Work,
    education: MenuBook,
    check: Check,
    close: Close,
  };
  return { IconSet, Icon };
};

export default useIcon;
