import React, {
  createContext, useState, useContext, useRef, useMemo,
} from 'react';
import {
  Modal, Box, Button,
} from '@mui/material';
import CloseOutlinedIcon from '@mui/icons-material/CloseOutlined';
import { Check } from '@mui/icons-material';

interface ModalContextProps {
  // eslint-disable-next-line no-unused-vars
  showModal: (title: string, message: string, onConfirm?: () => void) => void;
  hideModal: () => void;
}

const ModalContext = createContext<ModalContextProps | undefined>(undefined);

export const useModal = () => {
  const context = useContext(ModalContext);
  if (!context) {
    throw new Error('useModal must be used within a ModalProvider');
  }
  return context;
};

export function ModalProvider({ children }: any) {
  const [isOpen, setIsOpen] = useState(false);
  const [title, setTitle] = useState('');
  const [message, setMessage] = useState('');
  const confirmAction = useRef<() => void | null>();

  const showModal = (textTitle: string, textMessage: string, onConfirm?: () => void) => {
    setTitle(textTitle);
    setMessage(textMessage);
    setIsOpen(true);
    confirmAction.current = onConfirm;
  };

  const hideModal = () => setIsOpen(false);

  const handleConfirm = () => {
    if (confirmAction.current) {
      confirmAction.current();
    }
    hideModal();
  };

  const value = useMemo(() => ({
    showModal,
    hideModal,
  }), []);

  return (
    <ModalContext.Provider value={value}>
      <Modal open={isOpen} onClose={hideModal}>
        <Box sx={{
          position: 'absolute',
          maxHeight: 'auto',
          minWidth: '40%',
          outline: 'none',
          borderRadius: '8px',
          top: '50%',
          left: '50%',
          transform: 'translate(-50%, -50%)',
          width: 450,
          padding: 5,
          bgcolor: 'background.paper',
          boxShadow: 24,
        }}
        >
          <div
            style={{
              height: 300, alignItems: 'center', justifyContent: 'center', justifyItems: 'center',
            }}
            className="grid grid-flow-row h-full"
          >
            <div>
              <p className="text-center font-[500] text-2xl">{title}</p>
              <p className="text-center text-lg">{message}</p>
            </div>
            <div className="grid grid-flow-col  auto-cols-max gap-5">

              <Button
                sx={{
                  height: 42,
                  borderRadius: '8px',
                  width: '180px',
                  justifyItems: 'center',
                  justifyContent: 'center',
                  margin: '5px',
                  color: 'red',
                  fontWeight: 'bold',
                  display: 'flex',
                  alignItems: 'center',
                }}
                className="grey-btn"
                onClick={hideModal}
              >
                <CloseOutlinedIcon className="h-5 w-5 mr-2" />
                <span className="text-sm">Close</span>
              </Button>
              {confirmAction.current && (
              <Button
                variant="contained"
                style={{ backgroundColor: '#0ABF89', color: 'white' }}
                sx={{
                  height: 42,
                  borderRadius: '8px',
                  width: '180px',
                  justifyItems: 'center',
                  justifyContent: 'center',
                  margin: '5px',
                }}
                onClick={handleConfirm}
              >
                <Check className="h-5 w-5 mr-2" />
                <span className="text-sm">Confirm</span>
              </Button>
              )}
            </div>
          </div>
        </Box>
      </Modal>
      {children}
    </ModalContext.Provider>
  );
}
