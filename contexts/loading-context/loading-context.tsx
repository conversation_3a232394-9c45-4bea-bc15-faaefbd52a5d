import React, {
  createContext, useState, useContext,
} from 'react';
import { CircularProgress, Backdrop } from '@mui/material';

interface LoadingContextProps {
  showLoading: () => void;
  hideLoading: () => void;
}

const LoadingContext = createContext<LoadingContextProps | undefined>(undefined);

export const useLoading = () => {
  const context = useContext(LoadingContext);
  if (!context) {
    throw new Error('useLoading must be used within a LoadingProvider');
  }
  return context;
};

// eslint-disable-next-line react/function-component-definition
export const LoadingProvider = ({ children }:any) => {
  const [isLoading, setIsLoading] = useState(false);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars, no-unused-vars
  const [loadingList, setLoadingList] = useState<boolean[]>([]);

  const showLoading = () => {
    setLoadingList((prev) => [...prev, true]);
    setIsLoading(true);
  };

  const hideLoading = () => {
    setLoadingList((prev) => {
      const newList = [...prev];
      newList.pop();
      if (newList.length === 0) {
        setIsLoading(false);
      }
      return newList;
    });
  };
  return (
    // eslint-disable-next-line react/jsx-no-constructed-context-values
    <LoadingContext.Provider value={{ showLoading, hideLoading }}>
      <Backdrop open={isLoading} sx={{ color: '#fff', zIndex: 99999 }}>
        <CircularProgress color="inherit" />
      </Backdrop>
      {children}
    </LoadingContext.Provider>
  );
};
