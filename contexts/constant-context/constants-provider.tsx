import {
  ReactNode, useCallback, useEffect, useState,
} from 'react'; // type for children prop
import type { ConstantContext } from 'contexts/constant-context/type';
import { ConstantsContext } from 'contexts/constant-context/constant-context';
import API from 'api/src/lib/api';
import useAuth from 'hooks/use-auth';

interface ConstantsProviderProps {
  children: ReactNode;
}

function ConstantsProvider({ children }: ConstantsProviderProps) {
  const [constants, setConstants] = useState<ConstantContext>();
  const auth = useAuth();

  const fetchConstants = useCallback(async () => {
    const response = await API.CONSTANTS.fetchConstants();
    // setConstants(response);
    setConstants({ ...response, ...auth.user?.org?.meta?.constants });
  }, [auth]);

  useEffect(() => {
    if (auth.user) {
      fetchConstants();
    }
  }, [auth.user]);

  return (
    <ConstantsContext.Provider value={constants}>
      {children}
    </ConstantsContext.Provider>
  );
}

export default ConstantsProvider;
