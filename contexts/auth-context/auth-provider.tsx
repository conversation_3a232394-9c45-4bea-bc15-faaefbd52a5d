import {
  ReactNode, useEffect, useMemo, useState,
} from 'react'; // type for children prop
import type { AuthContextType } from 'contexts/auth-context/type';
import { AuthContext } from 'contexts/auth-context/auth-context';
import API from 'api/src/lib/api';
import { useRouter } from 'next/router';
import <PERSON>ie from 'js-cookie';
import { convertKeyValue } from 'utils/helper';

interface AuthProviderProviderProps {
  children: ReactNode;
}

function AuthProvider({ children }: AuthProviderProviderProps) {
  const [auth, setAuth] = useState<AuthContextType>();
  const router = useRouter();
  const value = useMemo(() => ({
    ...auth, setAuth,
  } as AuthContextType), [auth, setAuth]);
 
  useEffect(() => {
    if (router.asPath === '/404') return;
    if (router.asPath === '/401') return;
    if (router.asPath === '/login') return;
    if (!auth?.user) {
      const verifySession = async () => {
        const response:any = await API.AUTH.session();
        localStorage.setItem('member-config', JSON.stringify(response?.meta?.data || []));
        const convertTeam:any = await convertKeyValue(response?.teams, "id", "name", ["key", "title"]);
         if(response?.roles?.includes('drop_all')) {
        convertTeam.push({ key: 'all', title: 'All' });
         }
        // order by alphabet
        convertTeam.sort((a:any, b:any) => a.title.localeCompare(b.title));
        Cookie.set('teams', JSON.stringify(convertTeam));
        const teamId = Cookie.get('teamID');
        const team = response?.teams.find((t:any) => t.id === teamId);
        if(!team) {
          if(response?.teams.length > 0) {
            Cookie.set('teamID', response?.teams[0].id.toLowerCase());
          }
        }
        // eslint-disable-next-line no-unused-expressions
        response && setAuth({ user: response, teams: convertTeam } as any);
      };
      verifySession();
    }

  }, [JSON.stringify(auth?.user), JSON.stringify(auth?.teams)]);

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export default AuthProvider;
