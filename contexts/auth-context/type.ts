/* eslint-disable no-use-before-define */
import type React from 'react';
import { Teams } from 'types/teams-types';

export interface Org {
    id: string;
    url: string;
    title: string;
    type: string;
    createdAt: string;
    desc: string;
    meta: Meta;
    updatedAt: string;

}
interface Meta {
  constants: Constants;
  features: string[];
}

interface Constants {
  noteTags: NoteTag[];
  roles: SocialPlanSection[];
  socialPlanSections: SocialPlanSection[];
}

interface NoteTag {
  key: string;
  color: string;
  title: string;
}

interface SocialPlanSection {
  key: string;
  title: string;
}
export interface User {
    updatedAt: string;
    firstName: string;
    org: Org;
    email: string;
    color: string;
    id: string;
    meta: any;
    attachments: any[];
    auth: string;
    middleName: string;
    profile: string;
    lastName: string;
    roles: string[];
    createdAt: string;
}

export interface AuthContextType {
    user: User;
    teams: Teams[];
    setAuth: React.Dispatch<React.SetStateAction<{user: User; teams:Teams[]}>>;
}
