{"name": "e2e", "$schema": "../node_modules/nx/schemas/project-schema.json", "sourceRoot": "e2e/src", "projectType": "application", "targets": {"e2e": {"executor": "@nx/cypress:cypress", "options": {"cypressConfig": "e2e/cypress.config.ts", "devServerTarget": "dona-workspace:serve:development", "testingType": "e2e"}, "configurations": {"production": {"devServerTarget": "dona-workspace:serve:production"}}}, "lint": {"executor": "@nx/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["e2e/**/*.{js,ts}"]}}}, "tags": [], "implicitDependencies": ["dona-workspace"]}