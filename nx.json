{"$schema": "./node_modules/nx/schemas/nx-schema.json", "npmScope": "dona-workspace", "tasksRunnerOptions": {"default": {"runner": "nx-cloud", "options": {"cacheableOperations": ["build", "lint", "test", "e2e"], "accessToken": "YzA3M2YzMDItNDllMi00MjQ5LWFkNzgtMjYwY2ViZjlhZmFjfHJlYWQtd3JpdGU="}}}, "targetDefaults": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"]}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"]}, "e2e": {"inputs": ["default", "^production"]}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json", "{workspaceRoot}/.eslintignore"]}}, "namedInputs": {"default": ["{projectRoot}/**/*", "sharedGlobals"], "production": ["default", "!{projectRoot}/**/?(*.)+(spec|test).[jt]s?(x)?(.snap)", "!{projectRoot}/tsconfig.spec.json", "!{projectRoot}/jest.config.[jt]s", "!{projectRoot}/.eslintrc.json"], "sharedGlobals": []}, "generators": {"@nx/react": {"application": {"babel": true}, "library": {"unitTestRunner": "jest"}}, "@nx/next": {"application": {"style": "css", "linter": "eslint"}}}}