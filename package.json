{"name": "dona-workspace", "version": "0.0.0", "engines": {"node": ">=16"}, "license": "MIT", "scripts": {"start": "nx serve", "run-heroku": "npx next start ./dist/dona-workspace", "build": "npx nx build", "test": "nx test"}, "private": true, "dependencies": {"@apollo/client": "^3.8.3", "@emotion/react": "^11.11.0", "@emotion/styled": "^11.11.0", "@juggle/resize-observer": "^3.4.0", "@mapbox/mapbox-sdk": "^0.16.1", "@mui/icons-material": "^5.11.16", "@mui/lab": "^5.0.0-alpha.129", "@mui/material": "^5.12.3", "@mui/styled-engine-sc": "^5.12.0", "@mui/system": "^5.12.3", "@mui/x-date-pickers": "^6.3.1", "@nx/next": "^17.1.3", "@swc/helpers": "~0.5.0", "axios": "^1.4.0", "chart.js": "^4.4.0", "cookie": "^0.5.0", "dayjs": "^1.11.7", "formik": "^2.2.9", "gridjs": "^6.2.0", "gridjs-react": "^6.1.1", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "mapbox-gl": "^3.3.0", "next": "13.3.0", "nookies": "^2.5.2", "react": "^18.2.0", "react-beautiful-dnd": "^13.1.1", "react-chartjs-2": "^5.2.0", "react-circular-progressbar": "^2.1.0", "react-dom": "^18.2.0", "react-infinite-scroll-component": "^6.1.0", "react-markdown": "^9.0.3", "react-signature-canvas": "^1.1.0-alpha.2", "styled-components": "^5.3.10", "tslib": "^2.3.0", "yup": "^1.1.1"}, "devDependencies": {"@nx/cypress": "16.1.3", "@nx/eslint-plugin": "16.1.3", "@nx/jest": "16.1.3", "@nx/js": "16.1.3", "@nx/linter": "16.1.3", "@nx/react": "16.1.3", "@nx/storybook": "^16.1.3", "@nx/workspace": "16.1.3", "@swc/cli": "~0.1.62", "@swc/core": "~1.3.51", "@swc/jest": "0.2.20", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "14.0.0", "@types/cookie": "^0.5.1", "@types/jest": "^29.4.0", "@types/js-cookie": "^3.0.3", "@types/mapbox-gl": "^3.1.0", "@types/node": "18.14.2", "@types/react": "18.0.28", "@types/react-beautiful-dnd": "^13.1.8", "@types/react-dom": "18.0.11", "@typescript-eslint/eslint-plugin": "^5.58.0", "@typescript-eslint/parser": "^5.58.0", "autoprefixer": "10.4.13", "babel-jest": "^29.4.1", "cypress": "^12.11.0", "eslint": "~8.15.0", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "13.3.0", "eslint-config-prettier": "8.1.0", "eslint-plugin-cypress": "^2.10.3", "eslint-plugin-import": "2.27.5", "eslint-plugin-jsx-a11y": "6.7.1", "eslint-plugin-react": "7.32.2", "eslint-plugin-react-hooks": "4.6.0", "jest": "^29.4.1", "jest-environment-jsdom": "^29.4.1", "nx": "^17.1.3", "nx-cloud": "latest", "postcss": "8.4.21", "prettier": "^2.6.2", "react-test-renderer": "18.2.0", "tailwindcss": "3.2.7", "ts-jest": "^29.1.0", "ts-node": "10.9.1", "typescript": "^5.3.2"}}