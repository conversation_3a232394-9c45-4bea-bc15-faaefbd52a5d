// This file should be placed in `pages/_middleware.ts`
import API from 'api/src/lib/api';
import { parse } from 'cookie';
import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

const included = ['/chats', '/households', '/members', '/navigators', '/reporting', '/tasks', '/teams'];
const allowed = ['/404', '/401', '/login', '/admin/login'];
export default async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  if (allowed.includes(pathname)) {
    return NextResponse.next();
  }
  if (!included.includes(pathname)) {
    return NextResponse.next();
  }

  let token;
  let orgId;
  if (request.headers.get('cookie')) {
    const rawCookies = request.headers.get('cookie') as any;
    const cookies = parse(rawCookies);
    orgId = cookies.orgID;
    token = cookies.token; // get cookie from context.req.headers.cookie named token
  }
  const url = request.nextUrl.clone();
  const session:any = await API.AUTH.session(token);

  if (!session?.roles?.includes('admin')) {
    if (!session?.meta?.access?.includes(pathname.replace('/', ''))) {
      url.pathname = `/${session?.meta?.access[0]?.toLowerCase() || 'login'}`;
      return NextResponse.redirect(url);
    }
  }
  // Continue with the request processing if no conditions are met
  return NextResponse.next();
}
