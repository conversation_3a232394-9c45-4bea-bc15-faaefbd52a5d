/* eslint-disable no-restricted-syntax */
const data = {
  scored: true,
  survey: {
    startedAt: '2024-04-23T01:58:35Z',
    lastSurvey: 0,
    createdAt: '2024-04-23T01:58:35Z',
    score: '16.666666',
    orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
    firstSurvey: 1,
    id: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
    endedAt: '2024-04-23T01:59:55Z',
    updatedAt: '2024-04-23T01:59:55Z',
    task: {
      id: null,
    },
    sections: [
      {
        score: '12',
        questions: [
          {
            level: 0,
            type: 'nutrition',
            value: 0,
            score: 1,
            createdAt: '2024-04-23T01:58:35Z',
            message: 'Select best option',
            question: {
              id: null,
            },
            id: '7502C6C2-5A6E-405C-A3AD-0B3678F19C92',
            questions: [
              {
                updatedAt: '2024-04-23T01:58:35Z',
                id: '88BD982E-C9D2-4062-90DE-051F07E372B5',
                title: 'At least one person in my home is hungry most days.',
                section: {
                  id: null,
                },
                value: 0,
                createdAt: '2024-04-23T01:58:35Z',
                type: 'nutrition',
                level: 0,
                message: 'At least one person in my home is hungry most days',
                score: 0,
                questions: [
                  {
                    updatedAt: '2024-04-23T01:58:35Z',
                    score: 0,
                    question: {
                      id: '88BD982E-C9D2-4062-90DE-051F07E372B5',
                    },
                    type: 'nutrition',
                    id: 'B4D5FF87-A5D7-480B-BA5F-EA8E87B5745A',
                    section: {
                      id: null,
                    },
                    title: 'Denied SNAP benefits',
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                    message: 'Denied SNAP benefits',
                    level: 3,
                  },
                  {
                    updatedAt: '2024-04-23T01:58:35Z',
                    score: 0,
                    question: {
                      id: '88BD982E-C9D2-4062-90DE-051F07E372B5',
                    },
                    type: 'nutrition',
                    id: '0ECDB0DC-CF9A-487B-B813-4034FE87A582',
                    section: {
                      id: null,
                    },
                    title: 'Difficulty feeding child',
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                    message: 'Difficulty feeding child',
                    level: 3,
                  },
                  {
                    updatedAt: '2024-04-23T01:58:35Z',
                    score: 0,
                    question: {
                      id: '88BD982E-C9D2-4062-90DE-051F07E372B5',
                    },
                    type: 'nutrition',
                    id: '2555276F-F1BB-412A-BD78-158B5E2792A2',
                    section: {
                      id: null,
                    },
                    title: 'New parent needing food for child',
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                    message: 'New parent needing food for child',
                    level: 3,
                  },
                ],
                question: {
                  id: '7502C6C2-5A6E-405C-A3AD-0B3678F19C92',
                },
              },
              {
                updatedAt: '2024-04-23T01:58:35Z',
                id: '74744F4D-09C8-4DE7-B0CD-3EF8A32F8B66',
                title: 'Without help (e.g., SNAP, WIC, Church) we would not have enough food to eat',
                section: {
                  id: null,
                },
                value: 1,
                createdAt: '2024-04-23T01:58:35Z',
                type: 'nutrition',
                level: 0,
                message: 'Without help (e.g., SNAP, WIC, Church) we would not have enough food to eat',
                score: 0,
                questions: [
                  {
                    section: {
                      id: null,
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    message: 'Improve eating habits',
                    level: 3,
                    type: 'nutrition',
                    question: {
                      id: '74744F4D-09C8-4DE7-B0CD-3EF8A32F8B66',
                    },
                    title: 'Improve eating habits',
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    id: 'EAC3D3FF-F72A-4739-BC30-814B4E210184',
                    score: 0,
                  },
                  {
                    section: {
                      id: null,
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    message: 'Diabetes',
                    level: 3,
                    type: 'nutrition',
                    question: {
                      id: '74744F4D-09C8-4DE7-B0CD-3EF8A32F8B66',
                    },
                    title: 'Diabetes',
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    id: 'E2626D9B-F5FB-499B-9170-99A3CE7A2E21',
                    score: 0,
                  },
                  {
                    section: {
                      id: null,
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    message: 'Heart condition',
                    level: 3,
                    type: 'nutrition',
                    question: {
                      id: '74744F4D-09C8-4DE7-B0CD-3EF8A32F8B66',
                    },
                    title: 'Heart condition',
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    id: 'D1602F53-8BCF-4829-B061-A14FEC3327CD',
                    score: 0,
                  },
                ],
                question: {
                  id: '7502C6C2-5A6E-405C-A3AD-0B3678F19C92',
                },
              },
              {
                section: {
                  id: null,
                },
                message: 'Everyone in my home has enough food without help from others.',
                updatedAt: '2024-04-23T01:58:35Z',
                level: 0,
                type: 'nutrition',
                question: {
                  id: '7502C6C2-5A6E-405C-A3AD-0B3678F19C92',
                },
                title: 'Everyone in my home has enough food without help from others.',
                questions: [],
                id: '19BC00EA-2324-4F49-BDB8-AB41D7F1E59F',
                createdAt: '2024-04-23T01:58:35Z',
                value: 2,
                score: 0,
              },
            ],
            title: 'Nutrition',
            updatedAt: '2024-04-23T01:59:53Z',
            section: {
              id: '20229E67-6641-417D-A5EA-30815D9E8051',
            },
          },
          {
            level: 0,
            type: 'technology',
            value: 0,
            score: 0,
            createdAt: '2024-04-23T01:58:35Z',
            message: 'Select best option',
            question: {
              id: null,
            },
            id: '9F513F32-6353-4E98-BF3E-436AD75B0E2A',
            questions: [
              {
                question: {
                  id: '9F513F32-6353-4E98-BF3E-436AD75B0E2A',
                },
                level: 0,
                createdAt: '2024-04-23T01:58:35Z',
                title: 'Our household does not have access to internet service.',
                value: 0,
                score: 0,
                message: 'Our household does not have access to internet service.',
                id: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
                questions: [
                  {
                    level: 3,
                    type: 'technology',
                    id: '650B4426-222C-443F-BF5E-63FA11F39610',
                    message: 'Phone Service Diconnected',
                    title: 'Phone Service Diconnected',
                    question: {
                      id: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    level: 3,
                    type: 'technology',
                    id: 'BFAFC585-1892-49F5-B243-B1CE6D12AD7B',
                    message: 'Internet disconnected',
                    title: 'Internet disconnected',
                    question: {
                      id: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    level: 3,
                    type: 'technology',
                    id: 'A80DAE8E-1E4A-48CB-A5D8-11549CF2A7FD',
                    message: 'Computer access',
                    title: 'Computer access',
                    question: {
                      id: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    level: 3,
                    type: 'technology',
                    id: '213E52DF-EF31-439C-8FDA-607967F52BCC',
                    message: 'Computer Password',
                    title: 'Computer Password',
                    question: {
                      id: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    createdAt: '2024-04-23T01:58:35Z',
                    message: 'Computer Virus',
                    section: {
                      id: null,
                    },
                    score: 0,
                    level: 3,
                    value: 0,
                    id: '89065821-D8B1-458E-892B-885BC9379A7D',
                    title: 'Computer Virus',
                    type: 'technology',
                    updatedAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
                    },
                  },
                  {
                    score: 0,
                    question: {
                      id: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
                    },
                    title: 'Computer Repair',
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'technology',
                    level: 3,
                    updatedAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    message: 'Computer Repair',
                    id: 'C90C9EC1-1482-43AC-B728-0F006638405A',
                    value: 0,
                  },
                  {
                    score: 0,
                    question: {
                      id: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
                    },
                    title: 'Wellup App tutorial',
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'technology',
                    level: 3,
                    updatedAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    message: 'Wellup App tutorial',
                    id: '6071FB53-1FDE-44EA-AD16-5F67D2E748C1',
                    value: 0,
                  },
                  {
                    score: 0,
                    question: {
                      id: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
                    },
                    title: 'Wellup How society impacts your health',
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'technology',
                    level: 3,
                    updatedAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    message: 'Wellup How society impacts your health',
                    id: 'D437A844-D54D-43ED-9863-56317EC1FB13',
                    value: 0,
                  },
                  {
                    score: 0,
                    question: {
                      id: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
                    },
                    title: 'Wellup Managing your household’s health',
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'technology',
                    level: 3,
                    updatedAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    message: 'Wellup Managing your household’s health',
                    id: 'E7F9DB53-0EF7-4102-8850-A1069209ED3A',
                    value: 0,
                  },
                ],
                type: 'technology',
                section: {
                  id: null,
                },
                updatedAt: '2024-04-23T01:58:35Z',
              },
              {
                question: {
                  id: '9F513F32-6353-4E98-BF3E-436AD75B0E2A',
                },
                level: 0,
                createdAt: '2024-04-23T01:58:35Z',
                title: 'Our household has unreliable access to high speed internet service or only has access to the internet using a mobile phone or tablet device.',
                value: 1,
                score: 0,
                message: 'Our household has unreliable access to high speed internet service or only has access to the internet using a mobile phone or tablet device.',
                id: 'F3EA0120-E89B-4637-9F2D-B0DD3A2E5064',
                questions: [
                  {
                    level: 3,
                    type: 'technology',
                    id: '344D73C8-C82C-4991-8179-4EDF3DD1C9ED',
                    message: 'Phone Service Diconnected',
                    title: 'Phone Service Diconnected',
                    question: {
                      id: 'F3EA0120-E89B-4637-9F2D-B0DD3A2E5064',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    level: 3,
                    type: 'technology',
                    id: 'E9CE0A88-4D05-4786-9AEB-3EE51AAFE77A',
                    message: 'Computer Password',
                    title: 'Computer Password',
                    question: {
                      id: 'F3EA0120-E89B-4637-9F2D-B0DD3A2E5064',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    level: 3,
                    type: 'technology',
                    id: '186B1120-F2D3-4899-B85A-3B7B3C530F70',
                    message: 'Computer Virus',
                    title: 'Computer Virus',
                    question: {
                      id: 'F3EA0120-E89B-4637-9F2D-B0DD3A2E5064',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    level: 3,
                    type: 'technology',
                    id: '7B11CE5C-F88E-486D-A827-314A0E2B0AD0',
                    message: 'Computer Repair',
                    title: 'Computer Repair',
                    question: {
                      id: 'F3EA0120-E89B-4637-9F2D-B0DD3A2E5064',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    level: 3,
                    type: 'technology',
                    id: 'A4089FA4-1F78-4FAE-AFDD-A5E93BB41E6A',
                    message: 'Wellup App tutorial',
                    title: 'Wellup App tutorial',
                    question: {
                      id: 'F3EA0120-E89B-4637-9F2D-B0DD3A2E5064',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    level: 3,
                    type: 'technology',
                    id: 'A19B85C2-572C-4046-9D2F-41BB0DDB5A45',
                    message: 'Wellup How society impacts your health',
                    title: 'Wellup How society impacts your health',
                    question: {
                      id: 'F3EA0120-E89B-4637-9F2D-B0DD3A2E5064',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    level: 3,
                    type: 'technology',
                    id: '704CD8EA-0978-40CC-8500-87E35DAAF9FD',
                    message: 'Wellup Managing your household’s health',
                    title: 'Wellup Managing your household’s health',
                    question: {
                      id: 'F3EA0120-E89B-4637-9F2D-B0DD3A2E5064',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                ],
                type: 'technology',
                section: {
                  id: null,
                },
                updatedAt: '2024-04-23T01:58:35Z',
              },
              {
                question: {
                  id: '9F513F32-6353-4E98-BF3E-436AD75B0E2A',
                },
                level: 0,
                createdAt: '2024-04-23T01:58:35Z',
                title: 'We have reliable access to high speed internet service',
                value: 2,
                score: 0,
                message: 'We have reliable access to high speed internet service',
                id: '320F1929-C349-46A7-883B-BE838F55CAE9',
                questions: [],
                type: 'technology',
                section: {
                  id: null,
                },
                updatedAt: '2024-04-23T01:58:35Z',
              },
            ],
            title: 'Technology',
            updatedAt: '2024-04-23T01:58:51Z',
            section: {
              id: '20229E67-6641-417D-A5EA-30815D9E8051',
            },
          },
          {
            type: 'housing',
            level: 0,
            updatedAt: '2024-04-23T01:58:59Z',
            score: 0,
            questions: [
              {
                createdAt: '2024-04-23T01:58:35Z',
                questions: [
                  {
                    updatedAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    level: 3,
                    message: 'Denied HUD housing benefits',
                    type: 'housing',
                    createdAt: '2024-04-23T01:58:35Z',
                    id: '0EF6EB9B-FC4F-441E-B572-2A01C4A2698B',
                    value: 0,
                    title: 'Denied HUD housing benefits',
                    score: 0,
                    question: {
                      id: '734E7A39-851C-4999-B660-D40317FD3299',
                    },
                  },
                  {
                    updatedAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    level: 3,
                    message: 'Public housing',
                    type: 'housing',
                    createdAt: '2024-04-23T01:58:35Z',
                    id: '2C733538-09E7-4950-89F4-847DF25B54EA',
                    value: 0,
                    title: 'Public housing',
                    score: 0,
                    question: {
                      id: '734E7A39-851C-4999-B660-D40317FD3299',
                    },
                  },
                  {
                    updatedAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    level: 3,
                    message: 'Section 8',
                    type: 'housing',
                    createdAt: '2024-04-23T01:58:35Z',
                    id: '4E313EB1-9AD7-40DE-8782-25358D5F9030',
                    value: 0,
                    title: 'Section 8',
                    score: 0,
                    question: {
                      id: '734E7A39-851C-4999-B660-D40317FD3299',
                    },
                  },
                ],
                question: {
                  id: 'FB49668A-7A9D-4670-AD06-D76F91CA6540',
                },
                value: 0,
                id: '734E7A39-851C-4999-B660-D40317FD3299',
                title: 'We have lost or are at risk of losing housing',
                message: 'We have lost or are at risk of losing housing',
                level: 0,
                updatedAt: '2024-04-23T01:58:35Z',
                type: 'housing',
                section: {
                  id: null,
                },
                score: 0,
              },
              {
                createdAt: '2024-04-23T01:58:35Z',
                questions: [
                  {
                    level: 3,
                    id: 'F7B83402-519F-4B17-BDBB-B674A1CC87B3',
                    score: 0,
                    section: {
                      id: null,
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                    question: {
                      id: '0D0025D9-64A7-4750-AC1B-3DED6D074734',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    title: 'Pest infestation',
                    type: 'housing',
                    message: 'Pest infestation',
                  },
                  {
                    level: 3,
                    id: '5FBCB86B-FD1C-4635-8690-13F864433E61',
                    score: 0,
                    section: {
                      id: null,
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                    question: {
                      id: '0D0025D9-64A7-4750-AC1B-3DED6D074734',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    title: 'Housing quality',
                    type: 'housing',
                    message: 'Housing quality',
                  },
                  {
                    level: 3,
                    id: 'FC7756B8-6FBC-47E4-B697-2EFD9E7AC6F9',
                    score: 0,
                    section: {
                      id: null,
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                    question: {
                      id: '0D0025D9-64A7-4750-AC1B-3DED6D074734',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    title: 'Water leak',
                    type: 'housing',
                    message: 'Water leak',
                  },
                  {
                    level: 3,
                    id: 'DE0FF593-8F24-44E7-BA04-B6819AE97C12',
                    score: 0,
                    section: {
                      id: null,
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                    question: {
                      id: '0D0025D9-64A7-4750-AC1B-3DED6D074734',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    title: 'Mold',
                    type: 'housing',
                    message: 'Mold',
                  },
                  {
                    level: 3,
                    id: 'D7FC4719-6B1D-4F48-86F6-C1D44C866F92',
                    score: 0,
                    section: {
                      id: null,
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                    question: {
                      id: '0D0025D9-64A7-4750-AC1B-3DED6D074734',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    title: 'Physical damage (roof, walls, window)',
                    type: 'housing',
                    message: 'Physical damage (roof, walls, window)',
                  },
                ],
                question: {
                  id: 'FB49668A-7A9D-4670-AD06-D76F91CA6540',
                },
                value: 1,
                id: '0D0025D9-64A7-4750-AC1B-3DED6D074734',
                title: 'Does your home need major repairs?',
                message: 'Does your home need major repairs?',
                level: 0,
                updatedAt: '2024-04-23T01:58:35Z',
                type: 'housing',
                section: {
                  id: null,
                },
                score: 0,
              },
              {
                createdAt: '2024-04-23T01:58:35Z',
                questions: [],
                question: {
                  id: 'FB49668A-7A9D-4670-AD06-D76F91CA6540',
                },
                value: 2,
                id: 'FE0C2104-E100-4419-A670-D7F4DCFD11A1',
                title: 'Two or more people are sleeping in each bedroom',
                message: 'Two or more people are sleeping in each bedroom',
                level: 0,
                updatedAt: '2024-04-23T01:58:35Z',
                type: 'housing',
                section: {
                  id: null,
                },
                score: 0,
              },
            ],
            id: 'FB49668A-7A9D-4670-AD06-D76F91CA6540',
            section: {
              id: '20229E67-6641-417D-A5EA-30815D9E8051',
            },
            value: 0,
            createdAt: '2024-04-23T01:58:35Z',
            title: 'Housing',
            message: 'Select best option',
            question: {
              id: null,
            },
          },
          {
            type: 'income',
            level: 0,
            updatedAt: '2024-04-23T01:58:42Z',
            score: 0,
            questions: [
              {
                createdAt: '2024-04-23T01:58:35Z',
                title: 'We cannot pay any or most of our bills on time every month.',
                value: 0,
                score: 0,
                questions: [
                  {
                    value: 0,
                    score: 0,
                    id: '72C4F76C-F5FD-41C8-A015-787D25A03EF6',
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: 'CE5B222D-29C7-40F4-B567-7A016D39555E',
                    },
                    section: {
                      id: null,
                    },
                    type: 'income',
                    title: 'TANF',
                    message: 'TANF',
                    updatedAt: '2024-04-23T01:58:35Z',
                    level: 3,
                  },
                  {
                    section: {
                      id: null,
                    },
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    id: '071A0D48-D370-4672-9E91-540B2FC7EA63',
                    type: 'income',
                    level: 3,
                    value: 0,
                    title: 'Child support',
                    message: 'Child support',
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: 'CE5B222D-29C7-40F4-B567-7A016D39555E',
                    },
                  },
                  {
                    value: 0,
                    score: 0,
                    id: '6A460178-A19F-4F77-9F65-F54B7D7A8A4A',
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: 'CE5B222D-29C7-40F4-B567-7A016D39555E',
                    },
                    section: {
                      id: null,
                    },
                    type: 'income',
                    title: 'Child support',
                    message: 'Child support',
                    updatedAt: '2024-04-23T01:58:35Z',
                    level: 3,
                  },
                  {
                    value: 0,
                    score: 0,
                    id: '569F42C5-B1F9-43AB-BD3A-FD0A11C2AA22',
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: 'CE5B222D-29C7-40F4-B567-7A016D39555E',
                    },
                    section: {
                      id: null,
                    },
                    type: 'income',
                    title: 'Veteran Benefits',
                    message: 'Veteran Benefits',
                    updatedAt: '2024-04-23T01:58:35Z',
                    level: 3,
                  },
                ],
                id: 'CE5B222D-29C7-40F4-B567-7A016D39555E',
                message: 'We cannot pay any or most of our bills on time every month.',
                level: 0,
                updatedAt: '2024-04-23T01:58:35Z',
                type: 'income',
                section: {
                  id: null,
                },
                question: {
                  id: '851BFA6A-3683-4DA3-8E44-A1B7D54DDE52',
                },
              },
              {
                createdAt: '2024-04-23T01:58:35Z',
                title: 'There is no or very little money left after we pay our bills',
                value: 1,
                score: 0,
                questions: [
                  {
                    message: 'Financial literacy',
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'income',
                    score: 0,
                    value: 0,
                    level: 3,
                    title: 'Financial literacy',
                    id: '9E030A33-D771-4E51-A74B-5071FE9B04D8',
                    section: {
                      id: null,
                    },
                    question: {
                      id: 'DACEE1B7-C5D1-4853-891C-C0995EA95FD9',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                  },
                  {
                    message: 'Budgeting',
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'income',
                    score: 0,
                    value: 0,
                    level: 3,
                    title: 'Budgeting',
                    id: 'DCFCB863-5ACA-4FBC-8F2F-4949F7E30B1F',
                    section: {
                      id: null,
                    },
                    question: {
                      id: 'DACEE1B7-C5D1-4853-891C-C0995EA95FD9',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                  },
                  {
                    message: 'Retirement planning',
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'income',
                    score: 0,
                    value: 0,
                    level: 3,
                    title: 'Retirement planning',
                    id: 'FA3DBF37-7669-42B5-827A-1B9A13B336A6',
                    section: {
                      id: null,
                    },
                    question: {
                      id: 'DACEE1B7-C5D1-4853-891C-C0995EA95FD9',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                  },
                  {
                    message: 'Living Will',
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'income',
                    score: 0,
                    value: 0,
                    level: 3,
                    title: 'Living Will',
                    id: '4682197E-BFFE-4E15-AAF7-9FBEC9684F94',
                    section: {
                      id: null,
                    },
                    question: {
                      id: 'DACEE1B7-C5D1-4853-891C-C0995EA95FD9',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                  },
                  {
                    message: 'Credit Repair',
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'income',
                    score: 0,
                    value: 0,
                    level: 3,
                    title: 'Credit Repair',
                    id: 'C0EC8663-A92C-4E79-8A94-F7D90DFBCE62',
                    section: {
                      id: null,
                    },
                    question: {
                      id: 'DACEE1B7-C5D1-4853-891C-C0995EA95FD9',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                  },
                ],
                id: 'DACEE1B7-C5D1-4853-891C-C0995EA95FD9',
                message: 'There is no or very little money left after we pay our bills',
                level: 0,
                updatedAt: '2024-04-23T01:58:35Z',
                type: 'income',
                section: {
                  id: null,
                },
                question: {
                  id: '851BFA6A-3683-4DA3-8E44-A1B7D54DDE52',
                },
              },
              {
                createdAt: '2024-04-23T01:58:35Z',
                title: 'After paying all of our bills, we are, or could be saving money each month.',
                value: 2,
                score: 0,
                questions: [],
                id: '2C5D607A-2BE4-4041-A841-C0D1ACA2C1CE',
                message: 'After paying all of our bills, we are, or could be saving money each month.',
                level: 0,
                updatedAt: '2024-04-23T01:58:35Z',
                type: 'income',
                section: {
                  id: null,
                },
                question: {
                  id: '851BFA6A-3683-4DA3-8E44-A1B7D54DDE52',
                },
              },
            ],
            id: '851BFA6A-3683-4DA3-8E44-A1B7D54DDE52',
            section: {
              id: '20229E67-6641-417D-A5EA-30815D9E8051',
            },
            value: 0,
            createdAt: '2024-04-23T01:58:35Z',
            title: 'Income',
            message: 'Select best option',
            question: {
              id: null,
            },
          },
        ],
        kind: null,
        updatedAt: '2024-04-23T01:59:55Z',
        title: 'HOUSEHOLD LEVEL DATA',
        type: 'household',
        complete: true,
        createdAt: '2024-04-23T01:58:35Z',
        survey: {
          id: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
        },
        id: '20229E67-6641-417D-A5EA-30815D9E8051',
      },
      {
        score: '17',
        questions: [
          {
            updatedAt: '2024-04-23T01:59:45Z',
            value: 0,
            id: '294263BC-F585-4262-A6A0-95BE3CFC8E7B',
            question: {
              id: null,
            },
            createdAt: '2024-04-23T01:58:35Z',
            level: 0,
            type: 'employment',
            title: 'Employment',
            section: {
              id: '9242F61D-9B4B-440A-88AA-053D29587274',
            },
            questions: [
              {
                section: {
                  id: null,
                },
                level: 0,
                questions: [
                  {
                    id: '818F5426-BB14-4390-B27D-0071D17C495E',
                    title: 'Wage dispute',
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    score: 0,
                    message: 'Wage dispute',
                    value: 0,
                    question: {
                      id: 'CA9D8D7C-CE5B-499B-AEE0-2C8C4276D2F2',
                    },
                    section: {
                      id: null,
                    },
                    type: null,
                    level: 3,
                  },
                  {
                    id: '7446BF94-FB23-4434-AD9C-7C9A30E8F5E9',
                    title: 'Disability claim',
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    score: 0,
                    message: 'Disability claim',
                    value: 0,
                    question: {
                      id: 'CA9D8D7C-CE5B-499B-AEE0-2C8C4276D2F2',
                    },
                    section: {
                      id: null,
                    },
                    type: 'employment',
                    level: 3,
                  },
                  {
                    id: '2F475E3F-9BDE-41CD-85D4-6BA750B6B0C7',
                    title: 'Driver License suspended Expired tag',
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    score: 0,
                    message: 'Driver License suspended Expired tag',
                    value: 0,
                    question: {
                      id: 'CA9D8D7C-CE5B-499B-AEE0-2C8C4276D2F2',
                    },
                    section: {
                      id: null,
                    },
                    type: 'employment',
                    level: 3,
                  },
                  {
                    id: '496DE304-712A-4946-9EAF-F68041CD4C7B',
                    title: 'Hostile or unsafe working conditions',
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    score: 0,
                    message: 'Hostile or unsafe working conditions',
                    value: 0,
                    question: {
                      id: 'CA9D8D7C-CE5B-499B-AEE0-2C8C4276D2F2',
                    },
                    section: {
                      id: null,
                    },
                    type: 'employment',
                    level: 3,
                  },
                  {
                    id: '19084616-ACD9-40D4-9724-6B9AC9B62D36',
                    title: 'Illegal separation or firing',
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    score: 0,
                    message: 'Illegal separation or firing',
                    value: 0,
                    question: {
                      id: 'CA9D8D7C-CE5B-499B-AEE0-2C8C4276D2F2',
                    },
                    section: {
                      id: null,
                    },
                    type: 'employment',
                    level: 3,
                  },
                ],
                updatedAt: '2024-04-23T01:58:35Z',
                id: 'CA9D8D7C-CE5B-499B-AEE0-2C8C4276D2F2',
                score: 0,
                title: 'I am currently unemployed or work too few hours',
                type: 'employment',
                createdAt: '2024-04-23T01:58:35Z',
                value: 0,
                question: {
                  id: '294263BC-F585-4262-A6A0-95BE3CFC8E7B',
                },
                message: 'I am currently unemployed or work too few hours',
              },
              {
                section: {
                  id: null,
                },
                level: 0,
                questions: [
                  {
                    section: {
                      id: null,
                    },
                    title: 'Wage dispute',
                    question: {
                      id: 'D0CE0EE4-E65E-4C2D-85AF-05E4E6FEB301',
                    },
                    message: 'Wage dispute',
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    id: '34D42F02-4856-4E4B-8BE2-5D71AC0EB90A',
                    value: 0,
                    type: 'employment',
                  },
                  {
                    section: {
                      id: null,
                    },
                    title: 'Disability claim',
                    question: {
                      id: 'D0CE0EE4-E65E-4C2D-85AF-05E4E6FEB301',
                    },
                    message: 'Disability claim',
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    id: 'F7FBAFFE-D9BD-4E62-B7A2-69CC44D9C36C',
                    value: 0,
                    type: 'employment',
                  },
                  {
                    section: {
                      id: null,
                    },
                    title: 'Driver License suspended Expired tag',
                    question: {
                      id: 'D0CE0EE4-E65E-4C2D-85AF-05E4E6FEB301',
                    },
                    message: 'Driver License suspended Expired tag',
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    id: 'F13B88AE-B187-42E7-B323-70D6B55DEA67',
                    value: 0,
                    type: 'employment',
                  },
                  {
                    section: {
                      id: null,
                    },
                    title: 'Hostile or unsafe working conditions',
                    question: {
                      id: 'D0CE0EE4-E65E-4C2D-85AF-05E4E6FEB301',
                    },
                    message: 'Hostile or unsafe working conditions',
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    id: 'B03E09C1-D7F4-4B3D-9B2E-6B44C2E52309',
                    value: 0,
                    type: 'employment',
                  },
                  {
                    section: {
                      id: null,
                    },
                    title: 'Illegal separation or firing',
                    question: {
                      id: 'D0CE0EE4-E65E-4C2D-85AF-05E4E6FEB301',
                    },
                    message: 'Illegal separation or firing',
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    id: '0313CBE6-A6B5-4B6D-9354-9DAD8C6CBC9B',
                    value: 0,
                    type: 'employment',
                  },
                ],
                updatedAt: '2024-04-23T01:58:35Z',
                id: 'D0CE0EE4-E65E-4C2D-85AF-05E4E6FEB301',
                score: 0,
                title: 'My current employment does not provide paid days off or health insurance',
                type: 'employment',
                createdAt: '2024-04-23T01:58:35Z',
                value: 1,
                question: {
                  id: '294263BC-F585-4262-A6A0-95BE3CFC8E7B',
                },
                message: 'My current employment does not provide paid days off or health insurance',
              },
              {
                section: {
                  id: null,
                },
                level: 0,
                questions: [],
                updatedAt: '2024-04-23T01:58:35Z',
                id: '5BB2F178-1405-464E-8CE4-AE3A7B43F84C',
                score: 0,
                title: 'I currently have full-time employment with paid days off and health insurance',
                type: 'employment',
                createdAt: '2024-04-23T01:58:35Z',
                value: 2,
                question: {
                  id: '294263BC-F585-4262-A6A0-95BE3CFC8E7B',
                },
                message: 'I currently have full-time employment with paid days off and health insurance',
              },
            ],
            message: 'Which best describes you?',
            score: 0,
          },
          {
            section: {
              id: '9242F61D-9B4B-440A-88AA-053D29587274',
            },
            title: 'Transportation',
            question: {
              id: null,
            },
            message: 'Which best describes you?',
            score: 0,
            questions: [
              {
                updatedAt: '2024-04-23T01:58:35Z',
                value: 0,
                id: '6E404B46-00CA-41A9-A913-C696432F45DE',
                question: {
                  id: '2D9397E8-8B96-428D-8A61-FDEC14D6ECDA',
                },
                createdAt: '2024-04-23T01:58:35Z',
                level: 0,
                type: 'transportation',
                title: 'I am not able to get to most places easily and affordably',
                section: {
                  id: null,
                },
                questions: [
                  {
                    title: 'Automobile Repairs',
                    message: 'Automobile Repairs',
                    level: 3,
                    question: {
                      id: '6E404B46-00CA-41A9-A913-C696432F45DE',
                    },
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    updatedAt: '2024-04-23T01:58:35Z',
                    id: '31F481F6-40F2-4B53-A67B-489E3BE8F945',
                    score: 0,
                    section: {
                      id: null,
                    },
                    type: 'transportation',
                  },
                  {
                    title: 'Driver License suspended Unpaid tickets',
                    message: 'Driver License suspended Unpaid tickets',
                    level: 3,
                    question: {
                      id: '6E404B46-00CA-41A9-A913-C696432F45DE',
                    },
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    updatedAt: '2024-04-23T01:58:35Z',
                    id: 'FF94BBDA-5370-47C8-91D1-844853B74292',
                    score: 0,
                    section: {
                      id: null,
                    },
                    type: 'transportation',
                  },
                  {
                    title: 'Driver License suspended Expired tag',
                    message: 'Driver License suspended Expired tag',
                    level: 3,
                    question: {
                      id: '6E404B46-00CA-41A9-A913-C696432F45DE',
                    },
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    updatedAt: '2024-04-23T01:58:35Z',
                    id: 'E3F80B9F-9F77-4EF9-BFF3-B0BC0C3A1AF8',
                    score: 0,
                    section: {
                      id: null,
                    },
                    type: 'transportation',
                  },
                  {
                    title: 'Child transport school bus service appeal',
                    message: 'Child transport school bus service appeal',
                    level: 3,
                    question: {
                      id: '6E404B46-00CA-41A9-A913-C696432F45DE',
                    },
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    updatedAt: '2024-04-23T01:58:35Z',
                    id: '54A4CC02-4BB8-4B0B-B9DF-F9951D1356E5',
                    score: 0,
                    section: {
                      id: null,
                    },
                    type: 'transportation',
                  },
                  {
                    title: 'Child transport Private transport',
                    message: 'Child transport Private transport',
                    level: 3,
                    question: {
                      id: '6E404B46-00CA-41A9-A913-C696432F45DE',
                    },
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    updatedAt: '2024-04-23T01:58:35Z',
                    id: '3BB230F8-8765-4DF6-A002-6935B83B5AD2',
                    score: 0,
                    section: {
                      id: null,
                    },
                    type: 'transportation',
                  },
                ],
                message: 'I am not able to get to most places easily and affordably',
                score: 0,
              },
              {
                updatedAt: '2024-04-23T01:58:35Z',
                value: 1,
                id: '30818F12-71B3-416C-AB20-DCC46DB2E380',
                question: {
                  id: '2D9397E8-8B96-428D-8A61-FDEC14D6ECDA',
                },
                createdAt: '2024-04-23T01:58:35Z',
                level: 0,
                type: 'transportation',
                title: 'I can get to most places but it expensive or takes a lot of time.',
                section: {
                  id: null,
                },
                questions: [
                  {
                    question: {
                      id: '30818F12-71B3-416C-AB20-DCC46DB2E380',
                    },
                    message: 'Automobile Repairs',
                    id: 'B14E0ED4-3CF1-4765-A412-095738ACA6A5',
                    level: 3,
                    section: {
                      id: null,
                    },
                    type: 'transportation',
                    title: 'Automobile Repairs',
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    question: {
                      id: '30818F12-71B3-416C-AB20-DCC46DB2E380',
                    },
                    message: 'Driver License suspended Unpaid tickets',
                    id: 'F59C8521-60AD-4F39-880E-E2CAF1263270',
                    level: 3,
                    section: {
                      id: null,
                    },
                    type: 'transportation',
                    title: 'Driver License suspended Unpaid tickets',
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    question: {
                      id: '30818F12-71B3-416C-AB20-DCC46DB2E380',
                    },
                    message: 'Driver License suspended Expired tag',
                    id: 'F516BF8A-0396-4394-B48C-F4FFDD17F7A2',
                    level: 3,
                    section: {
                      id: null,
                    },
                    type: 'transportation',
                    title: 'Driver License suspended Expired tag',
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    question: {
                      id: '30818F12-71B3-416C-AB20-DCC46DB2E380',
                    },
                    message: 'Child transport school bus service appeal',
                    id: 'AC8BF5C3-9A56-4B79-8D13-01DEA08C3A65',
                    level: 3,
                    section: {
                      id: null,
                    },
                    type: 'transportation',
                    title: 'Child transport school bus service appeal',
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                  {
                    question: {
                      id: '30818F12-71B3-416C-AB20-DCC46DB2E380',
                    },
                    message: 'Child transport Private transport',
                    id: 'CC906CFA-B3B1-4A31-85E3-F972C4B85382',
                    level: 3,
                    section: {
                      id: null,
                    },
                    type: 'transportation',
                    title: 'Child transport Private transport',
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    value: 0,
                  },
                ],
                message: 'I can get to most places but it expensive or takes a lot of time.',
                score: 0,
              },
              {
                updatedAt: '2024-04-23T01:58:35Z',
                value: 2,
                id: 'E0E99C48-5B97-4367-87CC-419D5B48EAF4',
                question: {
                  id: '2D9397E8-8B96-428D-8A61-FDEC14D6ECDA',
                },
                createdAt: '2024-04-23T01:58:35Z',
                level: 0,
                type: 'transportation',
                title: 'I can comfortably get to the places I need to go.',
                section: {
                  id: null,
                },
                questions: [],
                message: 'I can comfortably get to the places I need to go.',
                score: 0,
              },
            ],
            updatedAt: '2024-04-23T01:59:06Z',
            createdAt: '2024-04-23T01:58:35Z',
            level: 0,
            type: 'transportation',
            id: '2D9397E8-8B96-428D-8A61-FDEC14D6ECDA',
            value: 0,
          },
          {
            section: {
              id: '9242F61D-9B4B-440A-88AA-053D29587274',
            },
            title: 'Life Skills & Education',
            question: {
              id: null,
            },
            message: 'Which best describes you?',
            score: 1,
            questions: [
              {
                title: 'I did not complete high school',
                message: 'I did not complete high school',
                score: 0,
                id: 'F66BC262-043C-48D7-A35F-7C4269636872',
                question: {
                  id: '2D86D0EC-0E64-490C-ADEC-B7FB329B8320',
                },
                questions: [
                  {
                    value: 0,
                    question: {
                      id: 'F66BC262-043C-48D7-A35F-7C4269636872',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    updatedAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    type: 'education',
                    title: 'For profit university debt',
                    section: {
                      id: null,
                    },
                    score: 0,
                    message: 'For profit university debt',
                    id: '0903A9A5-F57F-4496-A856-7BD255F8812E',
                  },
                  {
                    value: 0,
                    question: {
                      id: 'F66BC262-043C-48D7-A35F-7C4269636872',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    updatedAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    type: 'education',
                    title: 'Completing financial aid forms',
                    section: {
                      id: null,
                    },
                    score: 0,
                    message: 'Completing financial aid forms',
                    id: 'DB52E7D9-565F-4B08-9201-92E7FD2BCDB7',
                  },
                  {
                    value: 0,
                    question: {
                      id: 'F66BC262-043C-48D7-A35F-7C4269636872',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    updatedAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    type: 'education',
                    title: 'English Second Language (ESL) course enrollment',
                    section: {
                      id: null,
                    },
                    score: 0,
                    message: 'English Second Language (ESL) course enrollment',
                    id: '78C73835-E4AE-4AD5-8129-11878D64C9D5',
                  },
                  {
                    question: {
                      id: 'F66BC262-043C-48D7-A35F-7C4269636872',
                    },
                    value: 0,
                    id: '3EE00593-B658-4F73-BC47-DF99A9A17893',
                    score: 0,
                    title: 'Resume Writing',
                    level: 3,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    type: 'education',
                    message: 'Resume Writing',
                  },
                  {
                    question: {
                      id: 'F66BC262-043C-48D7-A35F-7C4269636872',
                    },
                    value: 0,
                    id: '8B0323A6-DE62-41DA-B3D3-91BC44891D5C',
                    score: 0,
                    title: 'Interview skills',
                    level: 3,
                    updatedAt: '2024-04-23T01:58:35Z',
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    type: 'education',
                    message: 'Interview skills',
                  },
                ],
                level: 0,
                section: {
                  id: null,
                },
                updatedAt: '2024-04-23T01:58:35Z',
                value: 0,
                createdAt: '2024-04-23T01:58:35Z',
                type: 'education',
              },
              {
                title: 'I completed high school or I have completed a training that provided knowledge and skills for a specific job.',
                message: 'I completed high school or I have completed a training that provided knowledge and skills for a specific job.',
                score: 0,
                id: 'CB75AEB6-04DD-4C95-A489-5C5748066DCD',
                question: {
                  id: '2D86D0EC-0E64-490C-ADEC-B7FB329B8320',
                },
                questions: [
                  {
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: 'CB75AEB6-04DD-4C95-A489-5C5748066DCD',
                    },
                    id: '09B22A25-A883-4E7C-808C-6AD624BAF48F',
                    section: {
                      id: null,
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    title: 'For profit university debt',
                    value: 0,
                    message: 'For profit university debt',
                    type: 'education',
                    level: 3,
                    score: 0,
                  },
                  {
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: 'CB75AEB6-04DD-4C95-A489-5C5748066DCD',
                    },
                    id: 'FA9AE969-6930-4981-8513-652A63803027',
                    section: {
                      id: null,
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    title: 'Completing financial aid forms',
                    value: 0,
                    message: 'Completing financial aid forms',
                    type: 'education',
                    level: 3,
                    score: 0,
                  },
                  {
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: 'CB75AEB6-04DD-4C95-A489-5C5748066DCD',
                    },
                    id: 'F9F4ED01-DC1A-4B2C-BD7B-158CA368AD68',
                    section: {
                      id: null,
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    title: 'English Second Language (ESL) course enrollment',
                    value: 0,
                    message: 'English Second Language (ESL) course enrollment',
                    type: 'education',
                    level: 3,
                    score: 0,
                  },
                  {
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: 'CB75AEB6-04DD-4C95-A489-5C5748066DCD',
                    },
                    id: 'E6D8812C-86D1-4686-BBEF-E29D7AEB7947',
                    section: {
                      id: null,
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    title: 'Resume Writing',
                    value: 0,
                    message: 'Resume Writing',
                    type: 'education',
                    level: 3,
                    score: 0,
                  },
                  {
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: 'CB75AEB6-04DD-4C95-A489-5C5748066DCD',
                    },
                    id: '5A61C047-3938-4480-8E87-5D441211ACC4',
                    section: {
                      id: null,
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    title: 'Interview skills',
                    value: 0,
                    message: 'Interview skills',
                    type: 'education',
                    level: 3,
                    score: 0,
                  },
                ],
                level: 0,
                section: {
                  id: null,
                },
                updatedAt: '2024-04-23T01:58:35Z',
                value: 1,
                createdAt: '2024-04-23T01:58:35Z',
                type: 'education',
              },
              {
                title: 'I have completed at least college',
                message: 'I have completed at least college',
                score: 0,
                id: '83D87D14-8C7B-450F-9D40-68732E6090A7',
                question: {
                  id: '2D86D0EC-0E64-490C-ADEC-B7FB329B8320',
                },
                questions: [],
                level: 0,
                section: {
                  id: null,
                },
                updatedAt: '2024-04-23T01:58:35Z',
                value: 2,
                createdAt: '2024-04-23T01:58:35Z',
                type: 'education',
              },
            ],
            updatedAt: '2024-04-23T01:59:11Z',
            createdAt: '2024-04-23T01:58:35Z',
            level: 0,
            type: 'education',
            id: '2D86D0EC-0E64-490C-ADEC-B7FB329B8320',
            value: 0,
          },
        ],
        kind: null,
        updatedAt: '2024-04-23T01:59:55Z',
        title: 'INDIVIDUAL LEVEL DATA',
        type: 'individual',
        complete: true,
        createdAt: '2024-04-23T01:58:35Z',
        survey: {
          id: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
        },
        id: '9242F61D-9B4B-440A-88AA-053D29587274',
      },
      {
        score: '25',
        questions: [
          {
            section: {
              id: '3304A9EF-810E-4724-9C6E-FBCCFCFE92CC',
            },
            questions: [
              {
                question: {
                  id: 'BCB8739D-2C30-44ED-A281-149061F1C3CC',
                },
                updatedAt: '2024-04-23T01:58:35Z',
                score: 0,
                message: 'I am unable to walk around my home, work or play without pain or discomfort.',
                value: 0,
                type: 'policy',
                id: '41F66EF6-4BCC-441C-8DB4-8C70D150D0B3',
                createdAt: '2024-04-23T01:58:35Z',
                level: 0,
                title: 'I am unable to walk around my home, work or play without pain or discomfort.',
                questions: [
                  {
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'policy',
                    title: 'Need medical support devices (cane, crutches)',
                    question: {
                      id: '41F66EF6-4BCC-441C-8DB4-8C70D150D0B3',
                    },
                    message: 'Need medical support devices (cane, crutches)',
                    updatedAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    section: {
                      id: null,
                    },
                    score: 0,
                    id: '4BA048B0-7668-4646-82A9-7C678BD4F31E',
                  },
                  {
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'policy',
                    title: 'Need to install handrails or other support devices within home',
                    question: {
                      id: '41F66EF6-4BCC-441C-8DB4-8C70D150D0B3',
                    },
                    message: 'Need to install handrails or other support devices within home',
                    updatedAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    section: {
                      id: null,
                    },
                    score: 0,
                    id: '6FFE98C9-0BA8-4ED5-A7C5-0575C2178A89',
                  },
                  {
                    value: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                    type: 'policy',
                    title: 'Help identifying a location to exercise',
                    question: {
                      id: '41F66EF6-4BCC-441C-8DB4-8C70D150D0B3',
                    },
                    message: 'Help identifying a location to exercise',
                    updatedAt: '2024-04-23T01:58:35Z',
                    level: 3,
                    section: {
                      id: null,
                    },
                    score: 0,
                    id: '5625B3DA-F345-4232-8FA0-65AAE969DF48',
                  },
                ],
                section: {
                  id: null,
                },
              },
              {
                question: {
                  id: 'BCB8739D-2C30-44ED-A281-149061F1C3CC',
                },
                updatedAt: '2024-04-23T01:58:35Z',
                score: 0,
                message: 'I am sometimes able to walk around my home, work or play without pain or discomfort.',
                value: 1,
                type: 'policy',
                id: '7250C443-A9AF-40AC-BC11-9B233C2F1DC3',
                createdAt: '2024-04-23T01:58:35Z',
                level: 0,
                title: 'I am sometimes able to walk around my home, work or play without pain or discomfort.',
                questions: [
                  {
                    question: {
                      id: '7250C443-A9AF-40AC-BC11-9B233C2F1DC3',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    title: 'Need medical support devices (cane, crutches)',
                    score: 0,
                    value: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    type: 'policy',
                    level: 3,
                    section: {
                      id: null,
                    },
                    message: 'Need medical support devices (cane, crutches)',
                    id: 'D51847D9-47DA-4537-89E5-303D8680237D',
                  },
                  {
                    question: {
                      id: '7250C443-A9AF-40AC-BC11-9B233C2F1DC3',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    title: 'Need to install handrails or other support devices within home',
                    score: 0,
                    value: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    type: 'policy',
                    level: 3,
                    section: {
                      id: null,
                    },
                    message: 'Need to install handrails or other support devices within home',
                    id: '6E61BF94-3D24-4E7A-8B33-1FAAA2594F10',
                  },
                  {
                    question: {
                      id: '7250C443-A9AF-40AC-BC11-9B233C2F1DC3',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    title: 'Help identifying a location to exercise',
                    score: 0,
                    value: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    type: 'policy',
                    level: 3,
                    section: {
                      id: null,
                    },
                    message: 'Help identifying a location to exercise',
                    id: 'C8885EB9-6DC8-45E3-9908-C18AE183027D',
                  },
                ],
                section: {
                  id: null,
                },
              },
              {
                question: {
                  id: 'BCB8739D-2C30-44ED-A281-149061F1C3CC',
                },
                updatedAt: '2024-04-23T01:58:35Z',
                score: 0,
                message: 'I am able to walk around my home, work and play without pain or discomfort.',
                value: 2,
                type: 'policy',
                id: '48AAA390-FEEE-40D3-9121-E15A48E4F3B3',
                createdAt: '2024-04-23T01:58:35Z',
                level: 0,
                title: 'I am able to walk around my home, work and play without pain or discomfort.',
                questions: [],
                section: {
                  id: null,
                },
              },
            ],
            value: 0,
            title: 'Daily Activites - Physical',
            score: 0,
            question: {
              id: null,
            },
            id: 'BCB8739D-2C30-44ED-A281-149061F1C3CC',
            createdAt: '2024-04-23T01:58:35Z',
            message: 'Which best describes how you have felt during the past 7 days?',
            type: 'policy',
            level: 0,
            updatedAt: '2024-04-23T01:59:37Z',
          },
          {
            section: {
              id: '3304A9EF-810E-4724-9C6E-FBCCFCFE92CC',
            },
            questions: [
              {
                score: 0,
                value: 0,
                title: 'Most days I am not motivated to get out of bed, take care of myself or complete tasks at home or work.',
                type: 'policy',
                id: 'E8BAA4F0-098F-4767-9A25-F93D95AF0D9B',
                createdAt: '2024-04-23T01:58:35Z',
                question: {
                  id: '6D5887AE-CA0C-47BA-A4AB-41BE23D0B384',
                },
                section: {
                  id: null,
                },
                questions: [
                  {
                    value: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    message: 'Treatment services Alcohol',
                    question: {
                      id: 'E8BAA4F0-098F-4767-9A25-F93D95AF0D9B',
                    },
                    id: '227DF78E-B5D5-42E1-8138-13D608D191C0',
                    title: 'Treatment services Alcohol',
                    section: {
                      id: null,
                    },
                    type: 'policy',
                    level: 3,
                    score: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                  },
                  {
                    value: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    message: 'Treatment services Opiod or other substance use',
                    question: {
                      id: 'E8BAA4F0-098F-4767-9A25-F93D95AF0D9B',
                    },
                    id: 'E57EDB12-4454-4A9C-951A-BD320143004A',
                    title: 'Treatment services Opiod or other substance use',
                    section: {
                      id: null,
                    },
                    type: 'policy',
                    level: 3,
                    score: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                  },
                  {
                    value: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    message: 'Treatment services Gambling',
                    question: {
                      id: 'E8BAA4F0-098F-4767-9A25-F93D95AF0D9B',
                    },
                    id: 'F77A8B4A-43C4-4EBF-93E2-E7026A80006C',
                    title: 'Treatment services Gambling',
                    section: {
                      id: null,
                    },
                    type: 'policy',
                    level: 3,
                    score: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                  },
                  {
                    value: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    message: 'Need help providing emotional/behavioral support to a family member or friend',
                    question: {
                      id: 'E8BAA4F0-098F-4767-9A25-F93D95AF0D9B',
                    },
                    id: '4C72AB0F-214D-4BA1-993C-30258BB0E4F0',
                    title: 'Need help providing emotional/behavioral support to a family member or friend',
                    section: {
                      id: null,
                    },
                    type: 'policy',
                    level: 3,
                    score: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                  },
                  {
                    value: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    message: 'Help identifying a location for guided meditation',
                    question: {
                      id: 'E8BAA4F0-098F-4767-9A25-F93D95AF0D9B',
                    },
                    id: 'F8AD182D-EDAE-47A7-A889-DC3629E04A19',
                    title: 'Help identifying a location for guided meditation',
                    section: {
                      id: null,
                    },
                    type: 'policy',
                    level: 3,
                    score: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                  },
                  {
                    value: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    message: 'Mindfulness',
                    question: {
                      id: 'E8BAA4F0-098F-4767-9A25-F93D95AF0D9B',
                    },
                    id: '51307612-26F8-4B7B-9E6C-EB39A6195677',
                    title: 'Mindfulness',
                    section: {
                      id: null,
                    },
                    type: 'policy',
                    level: 3,
                    score: 0,
                    createdAt: '2024-04-23T01:58:35Z',
                  },
                ],
                level: 0,
                updatedAt: '2024-04-23T01:58:35Z',
                message: 'Most days I am not motivated to get out of bed, take care of myself or complete tasks at home or work.',
              },
              {
                score: 0,
                value: 1,
                title: 'Sometimes I am not motivated to get out of bed, take care of myself or complete tasks at home or work.',
                type: 'policy',
                id: '887067C5-DC14-4982-B7DD-7CDF0AD92032',
                createdAt: '2024-04-23T01:58:35Z',
                question: {
                  id: '6D5887AE-CA0C-47BA-A4AB-41BE23D0B384',
                },
                section: {
                  id: null,
                },
                questions: [
                  {
                    title: 'Treatment services Alcohol',
                    score: 0,
                    level: 3,
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: '887067C5-DC14-4982-B7DD-7CDF0AD92032',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    id: '865794DE-49B5-42C1-8676-AC3117BEB25C',
                    section: {
                      id: null,
                    },
                    value: 0,
                    type: 'policy',
                    message: 'Treatment services Alcohol',
                  },
                  {
                    title: 'Treatment services Opiod or other substance use',
                    score: 0,
                    level: 3,
                    createdAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: '887067C5-DC14-4982-B7DD-7CDF0AD92032',
                    },
                    updatedAt: '2024-04-23T01:58:35Z',
                    id: '39C1490C-C19F-4B45-AA55-880590A3FDA2',
                    section: {
                      id: null,
                    },
                    value: 0,
                    type: 'policy',
                    message: 'Treatment services Opiod or other substance use',
                  },
                  {
                    message: 'Treatment services Gambling',
                    question: {
                      id: '887067C5-DC14-4982-B7DD-7CDF0AD92032',
                    },
                    createdAt: '2024-04-23T01:58:35Z',
                    section: {
                      id: null,
                    },
                    id: 'D3C0CEFB-6D35-4B6D-8397-8173A121B128',
                    score: 0,
                    level: 3,
                    updatedAt: '2024-04-23T01:58:35Z',
                    type: 'policy',
                    value: 0,
                    title: 'Treatment services Gambling',
                  },
                  {
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: '887067C5-DC14-4982-B7DD-7CDF0AD92032',
                    },
                    title: 'Need help providing emotional/behavioral support to a family member or friend',
                    message: 'Need help providing emotional/behavioral support to a family member or friend',
                    type: 'policy',
                    value: 0,
                    section: {
                      id: null,
                    },
                    level: 3,
                    createdAt: '2024-04-23T01:58:35Z',
                    id: 'A2462E9E-A253-4F4A-9E52-F2FF40F4231E',
                  },
                  {
                    score: 0,
                    updatedAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: '887067C5-DC14-4982-B7DD-7CDF0AD92032',
                    },
                    title: 'Help identifying a location for guided meditation',
                    message: 'Help identifying a location for guided meditation',
                    type: 'policy',
                    value: 0,
                    section: {
                      id: null,
                    },
                    level: 3,
                    createdAt: '2024-04-23T01:58:35Z',
                    id: '64D172C9-2293-401E-84A8-6DA6E08766F7',
                  },
                  {
                    type: 'policy',
                    createdAt: '2024-04-23T01:58:35Z',
                    id: '44AE546E-1348-4377-8AC9-C7E8270EF884',
                    section: {
                      id: null,
                    },
                    value: 0,
                    level: 3,
                    score: 0,
                    message: 'Mindfulness',
                    updatedAt: '2024-04-23T01:58:35Z',
                    question: {
                      id: '887067C5-DC14-4982-B7DD-7CDF0AD92032',
                    },
                    title: 'Mindfulness',
                  },
                ],
                level: 0,
                updatedAt: '2024-04-23T01:58:35Z',
                message: 'Sometimes I am not motivated to get out of bed, take care of myself or complete tasks at home or work.',
              },
              {
                score: 0,
                value: 2,
                title: 'Most days I am  motivated to get out of bed, take care of myself or complete tasks at home or work.',
                type: 'policy',
                id: '34DBE190-D7D2-4FEC-AA21-FF65BF50333C',
                createdAt: '2024-04-23T01:58:35Z',
                question: {
                  id: '6D5887AE-CA0C-47BA-A4AB-41BE23D0B384',
                },
                section: {
                  id: null,
                },
                questions: [],
                level: 0,
                updatedAt: '2024-04-23T01:58:35Z',
                message: 'Most days I am  motivated to get out of bed, take care of myself or complete tasks at home or work.',
              },
            ],
            value: 0,
            title: 'Daily Activites - Mental',
            score: 1,
            question: {
              id: null,
            },
            id: '6D5887AE-CA0C-47BA-A4AB-41BE23D0B384',
            createdAt: '2024-04-23T01:58:35Z',
            message: 'Which best describes how you have felt during the past 7 days?',
            type: 'policy',
            level: 0,
            updatedAt: '2024-04-23T01:59:32Z',
          },
        ],
        kind: null,
        updatedAt: '2024-04-23T01:59:55Z',
        title: 'OUTCOME LEVEL DATA',
        type: 'outcome',
        complete: true,
        createdAt: '2024-04-23T01:58:35Z',
        survey: {
          id: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
        },
        id: '3304A9EF-810E-4724-9C6E-FBCCFCFE92CC',
      },
    ],
    name: 'Health Risk Profile',
    taker: {
      id: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
    },
    memberID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
    key: 'full_hrp',
    status: 'complete',
  },
  answers: [
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '851BFA6A-3683-4DA3-8E44-A1B7D54DDE52',
      questionID: '569F42C5-B1F9-43AB-BD3A-FD0A11C2AA22',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:42Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '2B66C8A4-7BB3-47FE-948A-7D1B9CB0B90A',
      updatedAt: '2024-04-23T01:58:42Z',
      answer: 'veteran benefits',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '851BFA6A-3683-4DA3-8E44-A1B7D54DDE52',
      questionID: 'CE5B222D-29C7-40F4-B567-7A016D39555E',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:42Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '4116EF7F-9455-4BA6-B2B2-208F7BC08016',
      updatedAt: '2024-04-23T01:58:42Z',
      answer: 'we cannot pay any or most of our bills on time every month.',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '851BFA6A-3683-4DA3-8E44-A1B7D54DDE52',
      questionID: '071A0D48-D370-4672-9E91-540B2FC7EA63',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:42Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'FE71C4AE-02BB-4BB0-9C08-F4EE74E559C1',
      updatedAt: '2024-04-23T01:58:42Z',
      answer: 'child support',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '9F513F32-6353-4E98-BF3E-436AD75B0E2A',
      questionID: '7A4CEDF9-A439-4C2E-AD4E-242934D04BC9',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:51Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'DFFB2FF0-3407-4441-9440-60FA8B9F7064',
      updatedAt: '2024-04-23T01:58:51Z',
      answer: 'our household does not have access to internet service.',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '9F513F32-6353-4E98-BF3E-436AD75B0E2A',
      questionID: '650B4426-222C-443F-BF5E-63FA11F39610',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:51Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'CBF39014-4F58-4A01-9D10-190FE611EFD2',
      updatedAt: '2024-04-23T01:58:51Z',
      answer: 'phone service diconnected',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '9F513F32-6353-4E98-BF3E-436AD75B0E2A',
      questionID: 'A80DAE8E-1E4A-48CB-A5D8-11549CF2A7FD',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:51Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'C1F3EEBB-5FC1-405D-8789-06EECA3F5F6F',
      updatedAt: '2024-04-23T01:58:51Z',
      answer: 'computer access',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '9F513F32-6353-4E98-BF3E-436AD75B0E2A',
      questionID: 'BFAFC585-1892-49F5-B243-B1CE6D12AD7B',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:51Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'AF31A9CD-0D1D-45FD-825E-F5D40D47750B',
      updatedAt: '2024-04-23T01:58:51Z',
      answer: 'internet disconnected',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: 'FB49668A-7A9D-4670-AD06-D76F91CA6540',
      questionID: '2C733538-09E7-4950-89F4-847DF25B54EA',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:59Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '75E9B7F7-425F-4872-865C-D94DCA549172',
      updatedAt: '2024-04-23T01:58:59Z',
      answer: 'public housing',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: 'FB49668A-7A9D-4670-AD06-D76F91CA6540',
      questionID: '4E313EB1-9AD7-40DE-8782-25358D5F9030',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:59Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '81B97F02-E368-41E1-81DC-EA75ABE619CE',
      updatedAt: '2024-04-23T01:58:59Z',
      answer: 'section 8',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: 'FB49668A-7A9D-4670-AD06-D76F91CA6540',
      questionID: '0EF6EB9B-FC4F-441E-B572-2A01C4A2698B',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:59Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'B58B8B0B-1FAC-4AF4-A3CD-1073E0463508',
      updatedAt: '2024-04-23T01:58:59Z',
      answer: 'denied hud housing benefits',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: 'FB49668A-7A9D-4670-AD06-D76F91CA6540',
      questionID: '734E7A39-851C-4999-B660-D40317FD3299',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:58:59Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '257F5EF3-62B1-44A1-8C3D-83D8C9AF76CF',
      updatedAt: '2024-04-23T01:58:59Z',
      answer: 'we have lost or are at risk of losing housing',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '2D9397E8-8B96-428D-8A61-FDEC14D6ECDA',
      questionID: '31F481F6-40F2-4B53-A67B-489E3BE8F945',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:06Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '82430666-2361-4521-A1E1-9F134DB0B6BD',
      updatedAt: '2024-04-23T01:59:06Z',
      answer: 'automobile repairs',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '2D9397E8-8B96-428D-8A61-FDEC14D6ECDA',
      questionID: '54A4CC02-4BB8-4B0B-B9DF-F9951D1356E5',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:06Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '37C09338-79AE-4ADA-A33B-BAA7346A7D08',
      updatedAt: '2024-04-23T01:59:06Z',
      answer: 'child transport school bus service appeal',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '2D9397E8-8B96-428D-8A61-FDEC14D6ECDA',
      questionID: 'E3F80B9F-9F77-4EF9-BFF3-B0BC0C3A1AF8',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:06Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '55E4FAD3-0F38-46B5-B2FB-AA0B784CB353',
      updatedAt: '2024-04-23T01:59:06Z',
      answer: 'driver license suspended expired tag',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '2D9397E8-8B96-428D-8A61-FDEC14D6ECDA',
      questionID: 'FF94BBDA-5370-47C8-91D1-844853B74292',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:06Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '142817E4-19C5-441B-9790-E7AA7F26C75E',
      updatedAt: '2024-04-23T01:59:06Z',
      answer: 'driver license suspended unpaid tickets',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '2D9397E8-8B96-428D-8A61-FDEC14D6ECDA',
      questionID: '3BB230F8-8765-4DF6-A002-6935B83B5AD2',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:06Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '5BC366B1-4129-4454-9065-9798B09EDFFD',
      updatedAt: '2024-04-23T01:59:06Z',
      answer: 'child transport private transport',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '2D9397E8-8B96-428D-8A61-FDEC14D6ECDA',
      questionID: '6E404B46-00CA-41A9-A913-C696432F45DE',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:06Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '566D6026-4E2B-40A7-9A38-9B858D64D696',
      updatedAt: '2024-04-23T01:59:06Z',
      answer: 'i am not able to get to most places easily and affordably',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '2D86D0EC-0E64-490C-ADEC-B7FB329B8320',
      questionID: 'FA9AE969-6930-4981-8513-652A63803027',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:11Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'D5CD8DC2-BBAB-449E-A344-5B63CFE7CCD1',
      updatedAt: '2024-04-23T01:59:11Z',
      answer: 'completing financial aid forms',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '2D86D0EC-0E64-490C-ADEC-B7FB329B8320',
      questionID: 'F9F4ED01-DC1A-4B2C-BD7B-158CA368AD68',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:11Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '47824F8F-A4D4-4778-862D-5A6BFE89E753',
      updatedAt: '2024-04-23T01:59:11Z',
      answer: 'english second language (esl) course enrollment',
      key: 'full_hrp',
    },
    {
      value: '1',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '2D86D0EC-0E64-490C-ADEC-B7FB329B8320',
      questionID: 'CB75AEB6-04DD-4C95-A489-5C5748066DCD',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:11Z',
      score: '1',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '0BE2CD67-E406-45C5-AEAF-79A0523CDA0B',
      updatedAt: '2024-04-23T01:59:11Z',
      answer: 'i completed high school or i have completed a training that provided knowledge and skills for a specific job.',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '2D86D0EC-0E64-490C-ADEC-B7FB329B8320',
      questionID: '09B22A25-A883-4E7C-808C-6AD624BAF48F',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:11Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '98ADE23C-8D9E-42FC-8656-A898129DD3F6',
      updatedAt: '2024-04-23T01:59:11Z',
      answer: 'for profit university debt',
      key: 'full_hrp',
    },
    {
      value: '1',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '6D5887AE-CA0C-47BA-A4AB-41BE23D0B384',
      questionID: '887067C5-DC14-4982-B7DD-7CDF0AD92032',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:32Z',
      score: '1',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'D726491A-B8B9-4044-B7CE-EC913D215C83',
      updatedAt: '2024-04-23T01:59:32Z',
      answer: 'sometimes i am not motivated to get out of bed, take care of myself or complete tasks at home or work.',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '6D5887AE-CA0C-47BA-A4AB-41BE23D0B384',
      questionID: 'D3C0CEFB-6D35-4B6D-8397-8173A121B128',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:32Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '351FD0FC-626C-42A2-8CD3-414A1BD805B6',
      updatedAt: '2024-04-23T01:59:32Z',
      answer: 'treatment services gambling',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '6D5887AE-CA0C-47BA-A4AB-41BE23D0B384',
      questionID: 'A2462E9E-A253-4F4A-9E52-F2FF40F4231E',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:32Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '76B2D30B-1698-48CB-B709-E04702E7DF60',
      updatedAt: '2024-04-23T01:59:32Z',
      answer: 'need help providing emotional/behavioral support to a family member or friend',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: 'BCB8739D-2C30-44ED-A281-149061F1C3CC',
      questionID: '4BA048B0-7668-4646-82A9-7C678BD4F31E',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:37Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '7A62E7CB-42FC-440A-91E9-4B13FBFF0D71',
      updatedAt: '2024-04-23T01:59:37Z',
      answer: 'need medical support devices (cane, crutches)',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: 'BCB8739D-2C30-44ED-A281-149061F1C3CC',
      questionID: '6FFE98C9-0BA8-4ED5-A7C5-0575C2178A89',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:37Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'F5024AC7-ACD4-40D2-878A-526ABC3B7442',
      updatedAt: '2024-04-23T01:59:37Z',
      answer: 'need to install handrails or other support devices within home',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: 'BCB8739D-2C30-44ED-A281-149061F1C3CC',
      questionID: '41F66EF6-4BCC-441C-8DB4-8C70D150D0B3',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:37Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '968585A2-A416-491D-BF5C-42001653E9CB',
      updatedAt: '2024-04-23T01:59:37Z',
      answer: 'i am unable to walk around my home, work or play without pain or discomfort.',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: 'BCB8739D-2C30-44ED-A281-149061F1C3CC',
      questionID: '5625B3DA-F345-4232-8FA0-65AAE969DF48',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:37Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'E85C184A-85CC-459B-AB2A-5AA9B018D7DD',
      updatedAt: '2024-04-23T01:59:37Z',
      answer: 'help identifying a location to exercise',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '294263BC-F585-4262-A6A0-95BE3CFC8E7B',
      questionID: '19084616-ACD9-40D4-9724-6B9AC9B62D36',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:45Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'C27973FC-337F-474E-9C49-D96553679FDA',
      updatedAt: '2024-04-23T01:59:45Z',
      answer: 'illegal separation or firing',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '294263BC-F585-4262-A6A0-95BE3CFC8E7B',
      questionID: 'CA9D8D7C-CE5B-499B-AEE0-2C8C4276D2F2',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:45Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: 'BCA2F877-CAFF-4F64-84A7-278781C81A33',
      updatedAt: '2024-04-23T01:59:45Z',
      answer: 'i am currently unemployed or work too few hours',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '7502C6C2-5A6E-405C-A3AD-0B3678F19C92',
      questionID: 'EAC3D3FF-F72A-4739-BC30-814B4E210184',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:53Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '48556903-E0AB-45A5-93FD-3138B1B8504B',
      updatedAt: '2024-04-23T01:59:53Z',
      answer: 'improve eating habits',
      key: 'full_hrp',
    },
    {
      value: '1',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '7502C6C2-5A6E-405C-A3AD-0B3678F19C92',
      questionID: '74744F4D-09C8-4DE7-B0CD-3EF8A32F8B66',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:53Z',
      score: '1',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '266554B2-8C32-4A5C-BCF3-7AF8E6B1030E',
      updatedAt: '2024-04-23T01:59:53Z',
      answer: 'without help (e.g., snap, wic, church) we would not have enough food to eat',
      key: 'full_hrp',
    },
    {
      value: '0',
      giverID: '423A4A02-AC78-45EC-82A7-281ADCC34DE1',
      parentID: '7502C6C2-5A6E-405C-A3AD-0B3678F19C92',
      questionID: 'E2626D9B-F5FB-499B-9170-99A3CE7A2E21',
      surveyID: '83AF2212-9F2B-40EA-B72E-7EF6574E3EBE',
      takerID: 'C332F30C-9090-4081-8476-2FA152FA9DD9',
      createdAt: '2024-04-23T01:59:53Z',
      score: '0',
      orgID: '5b7ed772-68bf-48c9-b956-d1b5cd1470d9',
      id: '50A89AC9-2ED2-48EF-815B-B925353890EE',
      updatedAt: '2024-04-23T01:59:53Z',
      answer: 'diabetes',
      key: 'full_hrp',
    },
  ],
};

const questions = data.survey.sections;
const { answers } = data;



const processQuestions = ()=>{
  const result = []
  for (const section of questions) {
    for (const question of section.questions) {
      const answer = answers.find((a) => a.parentID === question.id);
      result.push({
        section: section.title,
        question: question.message,
        answer: answer?.answer || 'no answer',
        nestedAnswer: question.questions.map((nestedQuestion)=>{
          const nestedAnswer = answers.find((a) => a.questionID === nestedQuestion.id);
          return nestedAnswer?.answer || null;
        }
        ).filter(x=>x)
      });
      // console.log(answer?.answer || 'no answer');
    }
  }
  return result;
}
console.log(processQuestions())
// const getAnswer = (id, question)=>{
//   const answer = answers.find((a) => a.questionID === id);
//   return {
//     answer: answer?.answer || 'no answer',
//     question: question.message,
//   }
// }

// function processQuestions(questions, answers, questionId = null) {
//   const result = [];
//   const _questions = questions.slice(0,1); 
//   for (const question of _questions) {
//     const parentId = question.id;
//     console.log(parentId)
//     const answerIds = answers.filter(x=>x.parentID === parentId).map(x=>x.questionID);
//     // console.log(question.message, 'question')
//     const nesteds = question.questions.filter(x=> answerIds.includes(x.id));

//     // console.log(answers.filter(x=>x.parentID === parentId).map(x=>x.questionID), 'answers')
//     // console.log(nesteds, 'nesteds')
//     // console.log(question.message, 'question')
//     // console.log(getAnswer(question.questions[1].id, question), 'question')
//     // const answer = answers.find((a) => a.questionID === question.questions[1].title);
//     // if (answer) {
//     //   result.push({
//     //     question: question.title,
//     //     answer: answer?.answer || 'no answer',
//     //     questionId: question.id,
//     //     parentId: question.parentID,
//     //     questionType: question.type,
//     //   });
//     // }
//   }
//   console.log(result.length)
//   return result;
// }

// function transformSurveyAnswers(surveySections, answers) {
//   const finalResult = [];
//   for (const section of surveySections) {
//     const sectionResult = {
//       section: section.title,
//       questions: processQuestions(section.questions.slice(0,1), answers, section.id),
//     };
//     finalResult.push(sectionResult);
//   }
//   return finalResult;
// }

// transformSurveyAnswers(questions, answers);
